//
//  EmotionalParticleSystem.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Beautiful particle system for emotional moments and celebrations
/// Creates magical visual effects that enhance the emotional connection
struct EmotionalParticleSystem: View {
    let particleType: ParticleType
    let isActive: Bool
    let sourcePosition: CGPoint
    
    @State private var particles: [Particle] = []
    @State private var animationTimer: Timer?
    
    enum ParticleType {
        case heartbeat
        case celebration
        case birthday
        case connection
        case favorite
        case achievement
        
        var particleCount: Int {
            switch self {
            case .heartbeat: return 8
            case .celebration: return 25
            case .birthday: return 30
            case .connection: return 12
            case .favorite: return 15
            case .achievement: return 20
            }
        }
        
        var colors: [Color] {
            switch self {
            case .heartbeat:
                return [.red, .pink, .red.opacity(0.7)]
            case .celebration:
                return [.yellow, .orange, .red, .purple, .blue, .green]
            case .birthday:
                return [.pink, .purple, .blue, .cyan, .mint]
            case .connection:
                return [.blue, .cyan, .teal, .mint]
            case .favorite:
                return [.red, .pink, .orange, .yellow]
            case .achievement:
                return [.yellow, .orange, .green, .blue]
            }
        }
        
        var particleSize: CGFloat {
            switch self {
            case .heartbeat: return 4
            case .celebration: return 6
            case .birthday: return 5
            case .connection: return 3
            case .favorite: return 5
            case .achievement: return 4
            }
        }
    }
    
    struct Particle: Identifiable {
        let id = UUID()
        var position: CGPoint
        var velocity: CGVector
        var color: Color
        var size: CGFloat
        var opacity: Double
        var life: Double
        var maxLife: Double
        var rotation: Double
        var rotationSpeed: Double
        
        mutating func update() {
            position.x += velocity.dx
            position.y += velocity.dy
            
            // Apply gravity and air resistance
            velocity.dy += 0.2 // gravity
            velocity.dx *= 0.98 // air resistance
            velocity.dy *= 0.98
            
            // Update rotation
            rotation += rotationSpeed
            
            // Update life
            life -= 0.016 // ~60fps
            opacity = max(0, life / maxLife)
            
            // Fade out effect
            if life < maxLife * 0.3 {
                opacity *= (life / (maxLife * 0.3))
            }
        }
        
        var isAlive: Bool {
            life > 0
        }
    }
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                Circle()
                    .fill(particle.color)
                    .frame(width: particle.size, height: particle.size)
                    .opacity(particle.opacity)
                    .rotationEffect(.degrees(particle.rotation))
                    .position(particle.position)
                    .blur(radius: particle.size * 0.1)
            }
        }
        .onChange(of: isActive) { _, active in
            if active {
                startParticleSystem()
            } else {
                stopParticleSystem()
            }
        }
        .onDisappear {
            stopParticleSystem()
        }
    }
    
    private func startParticleSystem() {
        generateParticles()
        
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.016, repeats: true) { _ in
            updateParticles()
        }
    }
    
    private func stopParticleSystem() {
        animationTimer?.invalidate()
        animationTimer = nil
        
        withAnimation(.easeOut(duration: 0.5)) {
            particles.removeAll()
        }
    }
    
    private func generateParticles() {
        var newParticles: [Particle] = []
        
        for _ in 0..<particleType.particleCount {
            let angle = Double.random(in: 0...(2 * .pi))
            let speed = Double.random(in: 2...8)
            let velocity = CGVector(
                dx: cos(angle) * speed,
                dy: sin(angle) * speed - Double.random(in: 2...5) // Initial upward bias
            )
            
            let particle = Particle(
                position: sourcePosition,
                velocity: velocity,
                color: particleType.colors.randomElement() ?? .blue,
                size: particleType.particleSize + CGFloat.random(in: -1...2),
                opacity: 1.0,
                life: Double.random(in: 1.5...3.0),
                maxLife: Double.random(in: 1.5...3.0),
                rotation: Double.random(in: 0...360),
                rotationSpeed: Double.random(in: -5...5)
            )
            
            newParticles.append(particle)
        }
        
        withAnimation(.easeOut(duration: 0.3)) {
            particles = newParticles
        }
    }
    
    private func updateParticles() {
        DispatchQueue.main.async {
            for i in particles.indices.reversed() {
                particles[i].update()
                
                if !particles[i].isAlive {
                    particles.remove(at: i)
                }
            }
            
            // Generate new particles for continuous effects
            if particleType == .heartbeat || particleType == .connection {
                if particles.count < particleType.particleCount / 2 {
                    generateParticles()
                }
            }
        }
    }
}

/// Particle effect modifier for easy integration
struct ParticleEffect: ViewModifier {
    let particleType: EmotionalParticleSystem.ParticleType
    @State private var isActive = false
    @State private var sourcePosition: CGPoint = .zero
    
    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    EmotionalParticleSystem(
                        particleType: particleType,
                        isActive: isActive,
                        sourcePosition: CGPoint(
                            x: geometry.size.width / 2,
                            y: geometry.size.height / 2
                        )
                    )
                    .onAppear {
                        sourcePosition = CGPoint(
                            x: geometry.size.width / 2,
                            y: geometry.size.height / 2
                        )
                    }
                }
            )
            .onTapGesture {
                triggerParticles()
            }
    }
    
    private func triggerParticles() {
        isActive = true
        
        // Auto-stop after a duration
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            isActive = false
        }
    }
}

/// Celebration particle burst for special moments
struct CelebrationBurst: View {
    let isTriggered: Bool
    let position: CGPoint
    
    var body: some View {
        EmotionalParticleSystem(
            particleType: .celebration,
            isActive: isTriggered,
            sourcePosition: position
        )
    }
}

/// Heartbeat particles for favorites
struct HeartbeatParticles: View {
    let isFavorite: Bool
    let position: CGPoint
    
    var body: some View {
        EmotionalParticleSystem(
            particleType: .heartbeat,
            isActive: isFavorite,
            sourcePosition: position
        )
    }
}

/// Birthday celebration particles
struct BirthdayParticles: View {
    let isActive: Bool
    let position: CGPoint
    
    var body: some View {
        EmotionalParticleSystem(
            particleType: .birthday,
            isActive: isActive,
            sourcePosition: position
        )
    }
}

extension View {
    /// Add particle effects to any view
    func particleEffect(_ type: EmotionalParticleSystem.ParticleType) -> some View {
        self.modifier(ParticleEffect(particleType: type))
    }
    
    /// Add celebration burst on tap
    func celebrationBurst(isTriggered: Binding<Bool>) -> some View {
        self.overlay(
            GeometryReader { geometry in
                CelebrationBurst(
                    isTriggered: isTriggered.wrappedValue,
                    position: CGPoint(
                        x: geometry.size.width / 2,
                        y: geometry.size.height / 2
                    )
                )
            }
        )
    }
}
