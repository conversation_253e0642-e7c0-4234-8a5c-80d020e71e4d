//
//  WorkflowNavigationCoordinator.swift
//  Keeps
//
//  Smart navigation coordinator for cross-section workflows
//  Manages seamless transitions between People, Teams, and Timeline
//

import SwiftUI
import Combine

// MARK: - Workflow Navigation Coordinator

/// Coordinates navigation between app sections during workflows
class WorkflowNavigationCoordinator: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentWorkflowSection: AppSection = .people
    @Published var isWorkflowNavigationActive = false
    @Published var navigationPath: [AppSection] = []
    @Published var sectionTransitionProgress: Double = 0.0
    
    // MARK: - Navigation State
    
    @Published var showingSectionTransition = false
    @Published var transitionDirection: TransitionDirection = .forward
    @Published var isAnimatingTransition = false
    
    // MARK: - Workflow Integration
    
    private var workflowManager: CrossSectionWorkflowManager?
    var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init() {
        setupWorkflowObservation()
    }
    
    // MARK: - Public Methods
    
    /// Connect with workflow manager
    func connect(with workflowManager: CrossSectionWorkflowManager) {
        self.workflowManager = workflowManager
        observeWorkflowChanges(workflowManager)
    }
    
    /// Navigate to specific section during workflow
    func navigateToSection(_ section: AppSection, animated: Bool = true) {
        guard isWorkflowNavigationActive else { return }
        
        let previousSection = currentWorkflowSection
        transitionDirection = getTransitionDirection(from: previousSection, to: section)
        
        if animated {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                performSectionTransition(to: section)
            }
        } else {
            performSectionTransition(to: section)
        }
    }
    
    /// Start workflow navigation mode
    func startWorkflowNavigation() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            isWorkflowNavigationActive = true
            navigationPath = []
            sectionTransitionProgress = 0.0
        }
    }
    
    /// End workflow navigation mode
    func endWorkflowNavigation() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            isWorkflowNavigationActive = false
            navigationPath = []
            currentWorkflowSection = .people
            sectionTransitionProgress = 0.0
        }
    }
    
    /// Get navigation suggestions for current workflow step
    func getNavigationSuggestions() -> [NavigationSuggestion] {
        guard let workflowManager = workflowManager,
              let currentStep = workflowManager.currentStep else {
            return []
        }
        
        return generateNavigationSuggestions(for: currentStep)
    }
    
    /// Trigger smart navigation based on workflow context
    func triggerSmartNavigation() {
        guard let workflowManager = workflowManager,
              let currentStep = workflowManager.currentStep else { return }
        
        let targetSection = currentStep.primarySection
        if targetSection != currentWorkflowSection {
            navigateToSection(targetSection)
        }
    }
    
    // MARK: - Private Methods
    
    private func setupWorkflowObservation() {
        // Setup initial observation patterns
    }
    
    private func observeWorkflowChanges(_ workflowManager: CrossSectionWorkflowManager) {
        // Observe workflow state changes
        workflowManager.$isWorkflowActive
            .sink { [weak self] isActive in
                if isActive {
                    self?.startWorkflowNavigation()
                } else {
                    self?.endWorkflowNavigation()
                }
            }
            .store(in: &cancellables)
        
        // Observe current step changes
        workflowManager.$currentStep
            .compactMap { $0 }
            .sink { [weak self] step in
                self?.handleStepChange(step)
            }
            .store(in: &cancellables)
    }
    
    private func handleStepChange(_ step: WorkflowStep) {
        let targetSection = step.primarySection
        
        // Auto-navigate to the step's primary section
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.navigateToSection(targetSection)
        }
    }
    
    private func performSectionTransition(to section: AppSection) {
        isAnimatingTransition = true
        showingSectionTransition = true
        
        // Add to navigation path
        if !navigationPath.contains(section) {
            navigationPath.append(section)
        }
        
        // Update current section
        currentWorkflowSection = section
        
        // Update progress
        updateTransitionProgress()
        
        // Complete transition
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            self.isAnimatingTransition = false
            self.showingSectionTransition = false
        }
    }
    
    private func getTransitionDirection(from: AppSection, to: AppSection) -> TransitionDirection {
        let sections = AppSection.allCases
        guard let fromIndex = sections.firstIndex(of: from),
              let toIndex = sections.firstIndex(of: to) else {
            return .forward
        }
        
        return toIndex > fromIndex ? .forward : .backward
    }
    
    private func updateTransitionProgress() {
        let totalSections = AppSection.allCases.count
        let currentIndex = AppSection.allCases.firstIndex(of: currentWorkflowSection) ?? 0
        sectionTransitionProgress = Double(currentIndex) / Double(totalSections - 1)
    }
    
    private func generateNavigationSuggestions(for step: WorkflowStep) -> [NavigationSuggestion] {
        var suggestions: [NavigationSuggestion] = []
        
        // Generate context-aware suggestions
        switch step.primarySection {
        case .people:
            suggestions.append(NavigationSuggestion(
                title: "Find Collaborators",
                description: "Browse your people to find the right collaborators",
                targetSection: .people,
                action: .viewPersonTeams(UUID()) // Placeholder UUID
            ))

        case .teams:
            suggestions.append(NavigationSuggestion(
                title: "Create Team",
                description: "Form a new team for this goal",
                targetSection: .teams,
                action: .viewTeamMembers(UUID()) // Placeholder UUID
            ))

        case .timeline:
            suggestions.append(NavigationSuggestion(
                title: "Set Milestone",
                description: "Add this goal to your timeline",
                targetSection: .timeline,
                action: .addToTimeline(UUID(), 0) // Placeholder values
            ))

        case .workflow:
            suggestions.append(NavigationSuggestion(
                title: "Continue Workflow",
                description: "Proceed with the current workflow",
                targetSection: .workflow,
                action: .viewInTimeline(UUID()) // Placeholder UUID
            ))

        case .general:
            // No specific suggestions for general section
            break
        }
        
        return suggestions
    }
}

// MARK: - Navigation Models

/// Direction of section transition
enum TransitionDirection {
    case forward
    case backward
}

/// Navigation suggestion for workflow steps
struct NavigationSuggestion: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let targetSection: AppSection
    let action: NavigationAction
}

// Note: Using existing NavigationAction from DeepLinkModels.swift

// MARK: - Workflow Navigation Views

/// Visual indicator for section transitions during workflows
struct WorkflowSectionTransitionView: View {
    @ObservedObject var navigationCoordinator: WorkflowNavigationCoordinator
    
    var body: some View {
        VStack(spacing: 16) {
            // Section transition indicator
            HStack(spacing: 12) {
                ForEach(AppSection.allCases, id: \.self) { section in
                    sectionIndicator(section)
                }
            }
            
            // Current section highlight
            Text("Currently in \(navigationCoordinator.currentWorkflowSection.rawValue)")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .opacity(navigationCoordinator.isWorkflowNavigationActive ? 1 : 0)
        .scaleEffect(navigationCoordinator.isWorkflowNavigationActive ? 1 : 0.8)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: navigationCoordinator.isWorkflowNavigationActive)
    }
    
    private func sectionIndicator(_ section: AppSection) -> some View {
        VStack(spacing: 8) {
            Circle()
                .fill(section == navigationCoordinator.currentWorkflowSection ? section.color : Color.gray.opacity(0.3))
                .frame(width: 30, height: 30)
                .overlay(
                    Image(systemName: section.icon)
                        .font(.caption)
                        .foregroundColor(section == navigationCoordinator.currentWorkflowSection ? .white : .secondary)
                )
                .scaleEffect(section == navigationCoordinator.currentWorkflowSection ? 1.2 : 1.0)
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: navigationCoordinator.currentWorkflowSection)
            
            Text(section.rawValue)
                .font(.caption2)
                .fontWeight(section == navigationCoordinator.currentWorkflowSection ? .semibold : .regular)
                .foregroundColor(section == navigationCoordinator.currentWorkflowSection ? section.color : .secondary)
        }
    }
}

/// Smart navigation suggestions overlay
struct WorkflowNavigationSuggestions: View {
    @ObservedObject var navigationCoordinator: WorkflowNavigationCoordinator
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Suggested Actions")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(navigationCoordinator.getNavigationSuggestions()) { suggestion in
                NavigationSuggestionCard(suggestion: suggestion) {
                    navigationCoordinator.navigateToSection(suggestion.targetSection)
                }
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }
}

/// Individual navigation suggestion card
struct NavigationSuggestionCard: View {
    let suggestion: NavigationSuggestion
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Circle()
                    .fill(suggestion.targetSection.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(systemName: suggestion.targetSection.icon)
                            .font(.headline)
                            .foregroundColor(suggestion.targetSection.color)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(suggestion.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(suggestion.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Workflow Integration Extensions

extension NavigationCoordinator {
    /// Integrate workflow navigation with main navigation
    func integrateWorkflowNavigation(_ workflowCoordinator: WorkflowNavigationCoordinator) {
        // Integration logic for main navigation coordinator
        workflowCoordinator.$currentWorkflowSection
            .sink { [weak self] section in
                if workflowCoordinator.isWorkflowNavigationActive {
                    switch section {
                    case .people:
                        self?.selectedTab = 1
                    case .teams:
                        self?.selectedTab = 0
                    case .timeline:
                        self?.selectedTab = 2
                    case .workflow:
                        // Workflow doesn't have a main tab
                        break
                    case .general:
                        // General doesn't have a main tab
                        break
                    }
                }
            }
            .store(in: &workflowCoordinator.cancellables)
    }
}
