//
//  TestFixes.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI

/// Test view to verify the fixes work correctly
struct TestFixesView: View {
    @StateObject private var teamManager = TeamManager()
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Testing Fixes")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            // Test filter tabs alignment
            Text("Filter Tabs Test:")
                .font(.headline)
            
            FilterTabsView(selectedCategory: $teamManager.selectedCategory)
                .border(Color.red, width: 1) // Visual border to check alignment
            
            // Test team card tap functionality
            Text("Team Card Tap Test:")
                .font(.headline)
            
            if let firstTeam = teamManager.filteredTeams.first {
                TeamCardView(team: firstTeam, teamManager: teamManager)
                    .onTapGesture {
                        print("✅ Team card tap works!")
                        // Show alert or visual feedback
                    }
                    .border(Color.green, width: 2) // Visual border to show tappable area
            }
            
            Spacer()
        }
        .padding()
    }
}

#Preview {
    TestFixesView()
}
