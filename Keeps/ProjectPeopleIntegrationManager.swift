//
//  ProjectPeopleIntegrationManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import Foundation
import CoreData
import SwiftUI

/// Manages intelligent integration between projects/teams and people
/// Provides smart team formation, role assignment, and collaboration features
class ProjectPeopleIntegrationManager: ObservableObject {
    
    // MARK: - Properties
    
    private let context: NSManagedObjectContext
    private let peopleManager: PeopleManager
    private let teamManager: TeamManager
    
    @Published var teamFormationSuggestions: [TeamFormationSuggestion] = []
    @Published var roleAssignmentRecommendations: [RoleAssignmentRecommendation] = []
    @Published var collaborativeGoals: [CollaborativeGoal] = []
    @Published var contributionTracking: [UUID: [IndividualContribution]] = [:]
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext, peopleManager: PeopleManager, teamManager: TeamManager) {
        self.context = context
        self.peopleManager = peopleManager
        self.teamManager = teamManager
    }
    
    // MARK: - Smart Team Formation
    
    /// Generate team formation suggestions based on relationships and project requirements
    func generateTeamFormationSuggestions(for projectType: ProjectType, requiredSkills: [String], teamSize: Int = 5) -> [TeamFormationSuggestion] {
        let allPeople = peopleManager.people
        var suggestions: [TeamFormationSuggestion] = []
        
        // Algorithm 1: Relationship-based formation
        let relationshipBasedTeam = formTeamByRelationships(people: allPeople, size: teamSize, skills: requiredSkills)
        if !relationshipBasedTeam.isEmpty {
            suggestions.append(TeamFormationSuggestion(
                id: UUID(),
                name: "Relationship-Optimized Team",
                members: relationshipBasedTeam,
                score: calculateTeamScore(relationshipBasedTeam),
                reasoning: "Based on strong existing relationships and collaboration history",
                type: .relationshipBased
            ))
        }
        
        // Algorithm 2: Skill-based formation
        let skillBasedTeam = formTeamBySkills(people: allPeople, size: teamSize, skills: requiredSkills)
        if !skillBasedTeam.isEmpty {
            suggestions.append(TeamFormationSuggestion(
                id: UUID(),
                name: "Skill-Optimized Team",
                members: skillBasedTeam,
                score: calculateTeamScore(skillBasedTeam),
                reasoning: "Based on complementary skills and expertise",
                type: .skillBased
            ))
        }
        
        // Algorithm 3: Balanced formation
        let balancedTeam = formBalancedTeam(people: allPeople, size: teamSize, skills: requiredSkills)
        if !balancedTeam.isEmpty {
            suggestions.append(TeamFormationSuggestion(
                id: UUID(),
                name: "Balanced Team",
                members: balancedTeam,
                score: calculateTeamScore(balancedTeam),
                reasoning: "Optimal balance of relationships, skills, and diversity",
                type: .balanced
            ))
        }
        
        teamFormationSuggestions = suggestions.sorted { $0.score > $1.score }
        return teamFormationSuggestions
    }
    
    /// Form team based on relationship strength
    private func formTeamByRelationships(people: [Person], size: Int, skills: [String]) -> [Person] {
        var selectedPeople: [Person] = []
        var remainingPeople = people
        
        // Start with person with highest average relationship strength
        if let starter = remainingPeople.max(by: { 
            getAverageRelationshipStrength($0) < getAverageRelationshipStrength($1) 
        }) {
            selectedPeople.append(starter)
            remainingPeople.removeAll { $0.id == starter.id }
        }
        
        // Add people with strongest relationships to existing team members
        while selectedPeople.count < size && !remainingPeople.isEmpty {
            let nextPerson = remainingPeople.max { person1, person2 in
                let score1 = calculateRelationshipScore(person1, with: selectedPeople)
                let score2 = calculateRelationshipScore(person2, with: selectedPeople)
                return score1 < score2
            }
            
            if let person = nextPerson {
                selectedPeople.append(person)
                remainingPeople.removeAll { $0.id == person.id }
            } else {
                break
            }
        }
        
        return selectedPeople
    }
    
    /// Form team based on required skills
    private func formTeamBySkills(people: [Person], size: Int, skills: [String]) -> [Person] {
        var selectedPeople: [Person] = []
        var remainingSkills = Set(skills)
        var remainingPeople = people
        
        // First, select people who have required skills
        for skill in skills {
            if let person = remainingPeople.first(where: { hasSkill($0, skill: skill) }) {
                selectedPeople.append(person)
                remainingPeople.removeAll { $0.id == person.id }
                remainingSkills.remove(skill)
                
                if selectedPeople.count >= size { break }
            }
        }
        
        // Fill remaining spots with people who have complementary skills
        while selectedPeople.count < size && !remainingPeople.isEmpty {
            let nextPerson = remainingPeople.max { person1, person2 in
                let score1 = calculateSkillComplementarity(person1, with: selectedPeople)
                let score2 = calculateSkillComplementarity(person2, with: selectedPeople)
                return score1 < score2
            }
            
            if let person = nextPerson {
                selectedPeople.append(person)
                remainingPeople.removeAll { $0.id == person.id }
            } else {
                break
            }
        }
        
        return selectedPeople
    }
    
    /// Form balanced team considering both relationships and skills
    private func formBalancedTeam(people: [Person], size: Int, skills: [String]) -> [Person] {
        var selectedPeople: [Person] = []
        var remainingPeople = people
        
        while selectedPeople.count < size && !remainingPeople.isEmpty {
            let nextPerson = remainingPeople.max { person1, person2 in
                let score1 = calculateBalancedScore(person1, with: selectedPeople, skills: skills)
                let score2 = calculateBalancedScore(person2, with: selectedPeople, skills: skills)
                return score1 < score2
            }
            
            if let person = nextPerson {
                selectedPeople.append(person)
                remainingPeople.removeAll { $0.id == person.id }
            } else {
                break
            }
        }
        
        return selectedPeople
    }
    
    // MARK: - Role Assignment
    
    /// Generate role assignment recommendations for team members
    func generateRoleAssignments(for team: Team, projectType: ProjectType) -> [RoleAssignmentRecommendation] {
        var recommendations: [RoleAssignmentRecommendation] = []
        
        let availableRoles = getAvailableRoles(for: projectType)
        
        for member in team.members {
            if let person = peopleManager.people.first(where: { $0.name == member.name }) {
                let suitableRoles = availableRoles.compactMap { role in
                    let suitability = calculateRoleSuitability(person: person, role: role)
                    return suitability > 0.6 ? (role, suitability) : nil
                }.sorted { $0.1 > $1.1 }
                
                if let bestRole = suitableRoles.first {
                    recommendations.append(RoleAssignmentRecommendation(
                        id: UUID(),
                        personId: person.id,
                        personName: person.name,
                        recommendedRole: bestRole.0,
                        suitabilityScore: bestRole.1,
                        reasoning: generateRoleReasoning(person: person, role: bestRole.0),
                        alternativeRoles: Array(suitableRoles.dropFirst().prefix(2).map { $0.0 })
                    ))
                }
            }
        }
        
        roleAssignmentRecommendations = recommendations
        return recommendations
    }
    
    // MARK: - Collaborative Goals
    
    /// Create collaborative goal between people and teams
    func createCollaborativeGoal(title: String, description: String, participants: [Person], team: Team?, targetDate: Date) -> CollaborativeGoal {
        let goal = CollaborativeGoal(
            id: UUID(),
            title: title,
            description: description,
            participantIds: participants.map { $0.id },
            teamId: team?.id,
            targetDate: targetDate,
            createdDate: Date(),
            status: .active,
            progress: 0.0
        )
        
        collaborativeGoals.append(goal)
        
        // Add goal to team if specified
        if let team = team {
            teamManager.addTeamGoal(to: team, title: title, description: description, targetDate: targetDate)
        }
        
        // Add goal to each participant's relationship goals
        for person in participants {
            peopleManager.addRelationshipGoal(for: person, title: "Collaborative: \(title)", description: description, targetDate: targetDate)
        }
        
        return goal
    }
    
    // MARK: - Contribution Tracking
    
    /// Track individual contribution to team project
    func trackContribution(personId: UUID, teamId: UUID, contribution: String, type: ContributionType, impact: ContributionImpact) {
        let individualContribution = IndividualContribution(
            id: UUID(),
            personId: personId,
            teamId: teamId,
            contribution: contribution,
            type: type,
            impact: impact,
            date: Date()
        )
        
        if contributionTracking[teamId] == nil {
            contributionTracking[teamId] = []
        }
        contributionTracking[teamId]?.append(individualContribution)
        
        // Update person's interaction history
        if let person = peopleManager.people.first(where: { $0.id == personId }) {
            peopleManager.addInteraction(for: person, type: .project, content: "Contributed: \(contribution)")
        }
    }
    
    /// Get contribution summary for person in team
    func getContributionSummary(personId: UUID, teamId: UUID) -> ContributionSummary {
        let contributions = contributionTracking[teamId]?.filter { $0.personId == personId } ?? []
        
        let totalContributions = contributions.count
        let highImpactContributions = contributions.filter { $0.impact == .high }.count
        let recentContributions = contributions.filter { 
            Calendar.current.dateComponents([.day], from: $0.date, to: Date()).day ?? 0 <= 30 
        }.count
        
        let contributionTypes = Dictionary(grouping: contributions, by: { $0.type })
            .mapValues { $0.count }
        
        return ContributionSummary(
            personId: personId,
            teamId: teamId,
            totalContributions: totalContributions,
            highImpactContributions: highImpactContributions,
            recentContributions: recentContributions,
            contributionsByType: contributionTypes,
            averageImpact: calculateAverageImpact(contributions)
        )
    }
    
    // MARK: - Helper Methods
    
    private func getAverageRelationshipStrength(_ person: Person) -> Double {
        return peopleManager.getRelationshipStrength(for: person)
    }
    
    private func calculateRelationshipScore(_ person: Person, with team: [Person]) -> Double {
        guard !team.isEmpty else { return 0.0 }
        
        let totalStrength = team.reduce(0.0) { sum, teamMember in
            // Simulate relationship strength calculation
            return sum + Double.random(in: 0.3...1.0)
        }
        
        return totalStrength / Double(team.count)
    }
    
    private func hasSkill(_ person: Person, skill: String) -> Bool {
        return person.role.localizedCaseInsensitiveContains(skill) ||
               person.company.localizedCaseInsensitiveContains(skill) ||
               person.tags.contains { $0.localizedCaseInsensitiveContains(skill) }
    }
    
    private func calculateSkillComplementarity(_ person: Person, with team: [Person]) -> Double {
        // Simplified skill complementarity calculation
        let personSkills = extractSkills(from: person)
        let teamSkills = team.flatMap { extractSkills(from: $0) }
        
        let uniqueSkills = Set(personSkills).subtracting(Set(teamSkills))
        return Double(uniqueSkills.count) / max(Double(personSkills.count), 1.0)
    }
    
    private func calculateBalancedScore(_ person: Person, with team: [Person], skills: [String]) -> Double {
        let relationshipScore = calculateRelationshipScore(person, with: team) * 0.4
        let skillScore = calculateSkillComplementarity(person, with: team) * 0.6
        return relationshipScore + skillScore
    }
    
    private func calculateTeamScore(_ team: [Person]) -> Double {
        guard team.count > 1 else { return 0.0 }
        
        var totalScore = 0.0
        let teamSize = Double(team.count)
        
        // Calculate average relationship strength
        for i in 0..<team.count {
            for j in (i+1)..<team.count {
                totalScore += Double.random(in: 0.5...1.0) // Simulate relationship strength
            }
        }
        
        let pairCount = (teamSize * (teamSize - 1)) / 2
        return totalScore / pairCount
    }
    
    private func extractSkills(from person: Person) -> [String] {
        var skills: [String] = []
        skills.append(person.role)
        skills.append(contentsOf: person.tags)
        return skills.filter { !$0.isEmpty }
    }
    
    private func getAvailableRoles(for projectType: ProjectType) -> [ProjectRole] {
        switch projectType {
        case .software:
            return [.projectManager, .developer, .designer, .tester, .analyst]
        case .creative:
            return [.creativeDirector, .designer, .writer, .producer, .coordinator]
        case .business:
            return [.projectManager, .analyst, .coordinator, .specialist, .consultant]
        case .research:
            return [.researcher, .analyst, .coordinator, .specialist, .writer]
        }
    }
    
    private func calculateRoleSuitability(person: Person, role: ProjectRole) -> Double {
        let roleKeywords = role.keywords
        let personProfile = "\(person.role) \(person.company) \(person.tags.joined(separator: " "))".lowercased()
        
        let matchCount = roleKeywords.filter { keyword in
            personProfile.contains(keyword.lowercased())
        }.count
        
        return Double(matchCount) / Double(roleKeywords.count)
    }
    
    private func generateRoleReasoning(person: Person, role: ProjectRole) -> String {
        return "Based on \(person.name)'s background in \(person.role) and experience with \(person.company), they would be well-suited for the \(role.rawValue) role."
    }
    
    private func calculateAverageImpact(_ contributions: [IndividualContribution]) -> Double {
        guard !contributions.isEmpty else { return 0.0 }
        
        let totalImpact = contributions.reduce(0.0) { sum, contribution in
            return sum + contribution.impact.numericValue
        }
        
        return totalImpact / Double(contributions.count)
    }
}

// MARK: - Supporting Models

/// Represents a team formation suggestion
struct TeamFormationSuggestion: Identifiable {
    let id: UUID
    let name: String
    let members: [Person]
    let score: Double
    let reasoning: String
    let type: FormationType

    enum FormationType {
        case relationshipBased
        case skillBased
        case balanced

        var icon: String {
            switch self {
            case .relationshipBased: return "person.2.fill"
            case .skillBased: return "brain.head.profile"
            case .balanced: return "scale.3d"
            }
        }

        var color: Color {
            switch self {
            case .relationshipBased: return .blue
            case .skillBased: return .green
            case .balanced: return .purple
            }
        }
    }
}

/// Represents a role assignment recommendation
struct RoleAssignmentRecommendation: Identifiable {
    let id: UUID
    let personId: UUID
    let personName: String
    let recommendedRole: ProjectRole
    let suitabilityScore: Double
    let reasoning: String
    let alternativeRoles: [ProjectRole]
}

/// Represents a collaborative goal
struct CollaborativeGoal: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let participantIds: [UUID]
    let teamId: UUID?
    let targetDate: Date
    let createdDate: Date
    var status: GoalStatus
    var progress: Double
    var completedDate: Date?

    enum GoalStatus: String, CaseIterable, Codable {
        case active = "Active"
        case paused = "Paused"
        case completed = "Completed"
        case cancelled = "Cancelled"

        var color: Color {
            switch self {
            case .active: return .green
            case .paused: return .orange
            case .completed: return .blue
            case .cancelled: return .red
            }
        }
    }
}

/// Represents an individual contribution to a team project
struct IndividualContribution: Identifiable, Codable {
    let id: UUID
    let personId: UUID
    let teamId: UUID
    let contribution: String
    let type: ContributionType
    let impact: ContributionImpact
    let date: Date
    var recognition: String?
    var linkedMilestoneId: UUID?
}

/// Types of contributions
enum ContributionType: String, CaseIterable, Codable {
    case leadership = "Leadership"
    case technical = "Technical"
    case creative = "Creative"
    case coordination = "Coordination"
    case research = "Research"
    case communication = "Communication"
    case problemSolving = "Problem Solving"
    case mentoring = "Mentoring"

    var icon: String {
        switch self {
        case .leadership: return "crown.fill"
        case .technical: return "gear.circle.fill"
        case .creative: return "paintbrush.fill"
        case .coordination: return "arrow.triangle.swap"
        case .research: return "magnifyingglass.circle.fill"
        case .communication: return "bubble.left.and.bubble.right.fill"
        case .problemSolving: return "lightbulb.fill"
        case .mentoring: return "person.fill.checkmark"
        }
    }

    var color: Color {
        switch self {
        case .leadership: return .purple
        case .technical: return .blue
        case .creative: return .pink
        case .coordination: return .orange
        case .research: return .green
        case .communication: return .cyan
        case .problemSolving: return .yellow
        case .mentoring: return .indigo
        }
    }
}

/// Impact levels of contributions
enum ContributionImpact: String, CaseIterable, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case critical = "Critical"

    var numericValue: Double {
        switch self {
        case .low: return 1.0
        case .medium: return 2.0
        case .high: return 3.0
        case .critical: return 4.0
        }
    }

    var color: Color {
        switch self {
        case .low: return .gray
        case .medium: return .blue
        case .high: return .orange
        case .critical: return .red
        }
    }
}

/// Project types for role assignment
enum ProjectType: String, CaseIterable {
    case software = "Software"
    case creative = "Creative"
    case business = "Business"
    case research = "Research"

    var icon: String {
        switch self {
        case .software: return "laptopcomputer"
        case .creative: return "paintbrush"
        case .business: return "briefcase"
        case .research: return "book"
        }
    }
}

/// Available project roles
enum ProjectRole: String, CaseIterable {
    case projectManager = "Project Manager"
    case developer = "Developer"
    case designer = "Designer"
    case tester = "Tester"
    case analyst = "Analyst"
    case creativeDirector = "Creative Director"
    case writer = "Writer"
    case producer = "Producer"
    case coordinator = "Coordinator"
    case researcher = "Researcher"
    case specialist = "Specialist"
    case consultant = "Consultant"

    var keywords: [String] {
        switch self {
        case .projectManager: return ["manager", "lead", "project", "coordination"]
        case .developer: return ["developer", "engineer", "programming", "coding"]
        case .designer: return ["designer", "design", "ui", "ux", "visual"]
        case .tester: return ["tester", "qa", "quality", "testing"]
        case .analyst: return ["analyst", "analysis", "data", "research"]
        case .creativeDirector: return ["creative", "director", "art", "vision"]
        case .writer: return ["writer", "content", "copy", "documentation"]
        case .producer: return ["producer", "production", "coordination"]
        case .coordinator: return ["coordinator", "coordination", "organization"]
        case .researcher: return ["researcher", "research", "investigation"]
        case .specialist: return ["specialist", "expert", "technical"]
        case .consultant: return ["consultant", "advisor", "strategy"]
        }
    }
}

/// Summary of individual contributions
struct ContributionSummary {
    let personId: UUID
    let teamId: UUID
    let totalContributions: Int
    let highImpactContributions: Int
    let recentContributions: Int
    let contributionsByType: [ContributionType: Int]
    let averageImpact: Double
}
