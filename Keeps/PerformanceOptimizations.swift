//
//  PerformanceOptimizations.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI
import Combine

// MARK: - Performance Monitoring

/// Performance monitoring and optimization utilities
class PerformanceMonitor: ObservableObject {
    static let shared = PerformanceMonitor()
    
    @Published var isOptimizationEnabled = true
    @Published var renderingMetrics = RenderingMetrics()
    
    private var frameTimer: Timer?
    private var lastFrameTime = CFAbsoluteTimeGetCurrent()
    
    private init() {
        startMonitoring()
    }
    
    private func startMonitoring() {
        frameTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.updateMetrics()
        }
    }
    
    private func updateMetrics() {
        let currentTime = CFAbsoluteTimeGetCurrent()
        let frameDuration = currentTime - lastFrameTime
        lastFrameTime = currentTime
        
        DispatchQueue.main.async {
            self.renderingMetrics.averageFrameTime = frameDuration
            self.renderingMetrics.fps = 1.0 / frameDuration
        }
    }
    
    deinit {
        frameTimer?.invalidate()
    }
}

struct RenderingMetrics {
    var averageFrameTime: Double = 0.016 // 60 FPS target
    var fps: Double = 60.0
    var memoryUsage: Double = 0.0
    var isPerformant: Bool { fps > 55.0 }
}

// MARK: - Lazy Loading Components

/// Lazy loading container for expensive views
struct LazyView<Content: View>: View {
    let build: () -> Content
    @State private var hasAppeared = false
    
    init(@ViewBuilder _ build: @escaping () -> Content) {
        self.build = build
    }
    
    var body: some View {
        Group {
            if hasAppeared {
                build()
            } else {
                ProgressView()
                    .frame(height: 100)
                    .onAppear {
                        // Delay to prevent blocking main thread
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            hasAppeared = true
                        }
                    }
            }
        }
    }
}

/// Optimized list view with virtualization
struct OptimizedListView<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let content: (Item) -> Content
    @State private var visibleRange: Range<Int> = 0..<10
    
    private let itemHeight: CGFloat = 80
    private let bufferSize = 5
    
    init(items: [Item], @ViewBuilder content: @escaping (Item) -> Content) {
        self.items = items
        self.content = content
    }
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 0) {
                    ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                        if shouldRenderItem(at: index) {
                            content(item)
                                .frame(height: itemHeight)
                                .id(index)
                        } else {
                            // Placeholder for non-visible items
                            Rectangle()
                                .fill(Color.clear)
                                .frame(height: itemHeight)
                                .id(index)
                        }
                    }
                }
            }
            .onPreferenceChange(ViewOffsetKey.self) { offset in
                updateVisibleRange(offset: offset)
            }
        }
    }
    
    private func shouldRenderItem(at index: Int) -> Bool {
        let expandedRange = max(0, visibleRange.lowerBound - bufferSize)..<min(items.count, visibleRange.upperBound + bufferSize)
        return expandedRange.contains(index)
    }
    
    private func updateVisibleRange(offset: CGFloat) {
        let startIndex = max(0, Int(-offset / itemHeight))
        let endIndex = min(items.count, startIndex + 15) // Show ~15 items
        visibleRange = startIndex..<endIndex
    }
}

// MARK: - Caching System

/// Advanced caching system for data and images
class CacheManager: ObservableObject {
    static let shared = CacheManager()
    
    private let memoryCache = NSCache<NSString, AnyObject>()
    private let diskCacheURL: URL
    private let maxMemorySize = 50 * 1024 * 1024 // 50MB
    private let maxDiskSize = 200 * 1024 * 1024 // 200MB
    
    private init() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        diskCacheURL = documentsPath.appendingPathComponent("KeepsCache")
        
        setupCache()
        createDiskCacheDirectory()
    }
    
    private func setupCache() {
        memoryCache.totalCostLimit = maxMemorySize
        memoryCache.countLimit = 1000
    }
    
    private func createDiskCacheDirectory() {
        try? FileManager.default.createDirectory(at: diskCacheURL, withIntermediateDirectories: true)
    }
    
    // MARK: - Generic Caching
    
    func store<T: Codable>(_ object: T, forKey key: String) {
        // Memory cache
        memoryCache.setObject(object as AnyObject, forKey: key as NSString)
        
        // Disk cache
        Task {
            await storeToDisk(object, forKey: key)
        }
    }
    
    func retrieve<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        // Check memory cache first
        if let cached = memoryCache.object(forKey: key as NSString) as? T {
            return cached
        }
        
        // Check disk cache
        return retrieveFromDisk(type, forKey: key)
    }
    
    private func storeToDisk<T: Codable>(_ object: T, forKey key: String) async {
        let url = diskCacheURL.appendingPathComponent(key)
        
        do {
            let data = try JSONEncoder().encode(object)
            try data.write(to: url)
        } catch {
            print("Failed to store to disk: \(error)")
        }
    }
    
    private func retrieveFromDisk<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        let url = diskCacheURL.appendingPathComponent(key)
        
        do {
            let data = try Data(contentsOf: url)
            let object = try JSONDecoder().decode(type, from: data)
            
            // Store back in memory cache
            memoryCache.setObject(object as AnyObject, forKey: key as NSString)
            
            return object
        } catch {
            return nil
        }
    }
    
    // MARK: - Image Caching
    
    func storeImage(_ image: UIImage, forKey key: String) {
        memoryCache.setObject(image, forKey: key as NSString)
        
        Task {
            await storeImageToDisk(image, forKey: key)
        }
    }
    
    func retrieveImage(forKey key: String) -> UIImage? {
        if let cached = memoryCache.object(forKey: key as NSString) as? UIImage {
            return cached
        }
        
        return retrieveImageFromDisk(forKey: key)
    }
    
    private func storeImageToDisk(_ image: UIImage, forKey key: String) async {
        let url = diskCacheURL.appendingPathComponent("\(key).jpg")
        
        if let data = image.jpegData(compressionQuality: 0.8) {
            try? data.write(to: url)
        }
    }
    
    private func retrieveImageFromDisk(forKey key: String) -> UIImage? {
        let url = diskCacheURL.appendingPathComponent("\(key).jpg")
        
        guard let data = try? Data(contentsOf: url),
              let image = UIImage(data: data) else {
            return nil
        }
        
        memoryCache.setObject(image, forKey: key as NSString)
        return image
    }
    
    // MARK: - Cache Management
    
    func clearMemoryCache() {
        memoryCache.removeAllObjects()
    }
    
    func clearDiskCache() {
        try? FileManager.default.removeItem(at: diskCacheURL)
        createDiskCacheDirectory()
    }
    
    func getCacheSize() -> (memory: Int, disk: Int) {
        let memorySize = memoryCache.totalCostLimit
        
        var diskSize = 0
        if let enumerator = FileManager.default.enumerator(at: diskCacheURL, includingPropertiesForKeys: [.fileSizeKey]) {
            for case let fileURL as URL in enumerator {
                if let fileSize = try? fileURL.resourceValues(forKeys: [.fileSizeKey]).fileSize {
                    diskSize += fileSize
                }
            }
        }
        
        return (memory: memorySize, disk: diskSize)
    }
}

// MARK: - View Offset Tracking

struct ViewOffsetKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

struct ViewOffsetModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .preference(key: ViewOffsetKey.self, value: geometry.frame(in: .global).minY)
                }
            )
    }
}

extension View {
    func trackOffset() -> some View {
        modifier(ViewOffsetModifier())
    }
}
