//
//  TimelineAnimations.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Timeline Search View

/// Search interface for finding specific weeks and insights
struct TimelineSearchView: View {
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Environment(\.dismiss) private var dismiss
    @State private var searchResults: [WeekEntry] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search weeks, insights, accomplishments...", text: $timelineManager.searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .onChange(of: timelineManager.searchText) { oldValue, newValue in
                            // Debounce search to prevent loops and improve performance
                            Task {
                                try? await Task.sleep(nanoseconds: 300_000_000) // 300ms delay
                                if timelineManager.searchText == newValue {
                                    await MainActor.run {
                                        updateSearchResults()
                                    }
                                }
                            }
                        }
                    
                    if !timelineManager.searchText.isEmpty {
                        Button(action: {
                            timelineManager.searchText = ""
                            Task {
                                await MainActor.run {
                                    updateSearchResults()
                                }
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                )
                .padding()
                
                // Search results
                if searchResults.isEmpty && !timelineManager.searchText.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 50))
                            .foregroundColor(.secondary)
                        
                        Text("No results found")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text("Try searching for different keywords")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List(searchResults, id: \.id) { week in
                        SearchResultRow(week: week, searchText: timelineManager.searchText)
                            .onTapGesture {
                                timelineManager.navigateToWeek(week.weekNumber)
                                dismiss()
                            }
                    }
                    .listStyle(PlainListStyle())
                }
                
                Spacer()
            }
            .navigationTitle("Search Timeline")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            updateSearchResults()
        }
    }
    
    private func updateSearchResults() {
        searchResults = timelineManager.filteredWeekEntries
    }
}

// MARK: - Search Result Row

struct SearchResultRow: View {
    let week: WeekEntry
    let searchText: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                // Emotional tag indicator
                Circle()
                    .fill(week.emotionalTag.color)
                    .frame(width: 12, height: 12)
                
                Text("Week \(week.weekNumber)")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text(week.startDate, format: .dateTime.month(.abbreviated).day())
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            if !week.title.isEmpty {
                Text(week.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            if !week.insight.isEmpty {
                Text(week.insight)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
            }
            
            if !week.accomplishments.isEmpty {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                    
                    Text("\(week.accomplishments.count) accomplishments")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Timeline Filters View

/// Filter interface for emotional tags and date ranges
struct TimelineFiltersView: View {
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Emotional tag filters
                VStack(alignment: .leading, spacing: 16) {
                    Text("Filter by Emotional Theme")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        // All option
                        FilterChip(
                            title: "All",
                            emoji: "📝",
                            isSelected: timelineManager.selectedEmotionalTag == nil,
                            color: .blue
                        ) {
                            timelineManager.selectedEmotionalTag = nil
                        }
                        
                        // Emotional tag options
                        ForEach(EmotionalTag.allCases, id: \.self) { tag in
                            FilterChip(
                                title: tag.rawValue,
                                emoji: tag.emoji,
                                isSelected: timelineManager.selectedEmotionalTag == tag,
                                color: tag.color
                            ) {
                                timelineManager.selectedEmotionalTag = timelineManager.selectedEmotionalTag == tag ? nil : tag
                            }
                        }
                    }
                }
                .padding()
                
                // Analytics section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Timeline Analytics")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    VStack(spacing: 12) {
                        AnalyticsRow(
                            title: "Completion Rate",
                            value: "\(Int(timelineManager.completionRate * 100))%",
                            color: .green
                        )
                        
                        AnalyticsRow(
                            title: "Current Streak",
                            value: "\(timelineManager.currentStreak) weeks",
                            color: .blue
                        )
                        
                        if let dominantTag = timelineManager.dominantEmotionalTag {
                            AnalyticsRow(
                                title: "Dominant Theme",
                                value: dominantTag.rawValue,
                                color: dominantTag.color
                            )
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(.ultraThinMaterial)
                )
                .padding(.horizontal)
                
                Spacer()
            }
            .navigationTitle("Timeline Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Filter Chip

struct FilterChip: View {
    let title: String
    let emoji: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Text(emoji)
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? color : Color(.systemGray5))
            )
        }
    }
}

// MARK: - Analytics Row

struct AnalyticsRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            Text(title)
                .font(.body)
            
            Spacer()
            
            Text(value)
                .font(.body)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

// MARK: - Full Screen Text Editor

/// Full-screen text editor for detailed insights
struct FullScreenTextEditor: View {
    @Binding var text: String
    let title: String
    @Binding var isPresented: Bool
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Writing prompts
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(writingPrompts, id: \.self) { prompt in
                            Button(action: {
                                if text.isEmpty {
                                    text = prompt
                                } else {
                                    text += "\n\n" + prompt
                                }
                            }) {
                                Text(prompt)
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(
                                        Capsule()
                                            .fill(Color.blue.opacity(0.1))
                                    )
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)
                .background(.ultraThinMaterial)
                
                // Text editor
                TextEditor(text: $text)
                    .font(.body)
                    .padding()
                    .background(Color(.systemBackground))
            }
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        isPresented = false
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private var writingPrompts: [String] {
        [
            "What shaped me this week?",
            "What did I learn about myself?",
            "What breakthrough did I have?",
            "How did I grow?",
            "What challenged me?",
            "What am I grateful for?",
            "What would I do differently?",
            "What insight will I carry forward?"
        ]
    }
}

// MARK: - Placeholder Views for Media

/// Audio recording functionality integrated with VoiceRecordingView
struct AudioRecorderView: View {
    let week: WeekEntry
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        VoiceRecordingView(
            onVoiceNoteSaved: { voiceNote in
                // Save the voice note to the week entry
                saveVoiceNoteToWeek(voiceNote, week: week)
                dismiss()
            },
            onCancel: {
                dismiss()
            }
        )
    }

    private func saveVoiceNoteToWeek(_ voiceNote: VoiceNote, week: WeekEntry) {
        // Add the voice note to the week's content
        // This would typically involve updating the week's data model
        // For now, we'll just print the action
        print("Saving voice note '\(voiceNote.title)' to Week \(week.weekNumber)")

        // In a real implementation, you would:
        // 1. Update the week's voice notes array
        // 2. Save to Core Data or your persistence layer
        // 3. Trigger any necessary UI updates
    }
}

/// Placeholder for sketch pad functionality
struct SketchPadView: View {
    let week: WeekEntry
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Image(systemName: "pencil.tip.crop.circle")
                    .font(.system(size: 100))
                    .foregroundColor(.orange)

                Text("Sketch Pad")
                    .font(.title)
                    .fontWeight(.bold)

                Text("Draw or sketch your thoughts for Week \(week.weekNumber)")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)

                // TODO: Implement actual drawing canvas
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6))
                    .frame(height: 300)
                    .overlay(
                        Text("Drawing Canvas")
                            .foregroundColor(.secondary)
                    )
            }
            .padding()
            .navigationTitle("Sketch Pad")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Birth Date Setup View

/// Initial setup view for users to set their birth date
struct BirthDateSetupView: View {
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Binding var isPresented: Bool
    @State private var selectedBirthDate = Date()
    @State private var currentAge: Int = 25

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 16) {
                    Image(systemName: "calendar.circle.fill")
                        .font(.system(size: 80))
                        .foregroundColor(.blue)

                    Text("🌌 Welcome to Evolution Timeline")
                        .font(.title)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)

                    Text("Your life as a visual memoir of ~4000 weeks")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                // Birth date picker
                VStack(spacing: 20) {
                    Text("When were you born?")
                        .font(.headline)
                        .fontWeight(.semibold)

                    DatePicker(
                        "Birth Date",
                        selection: $selectedBirthDate,
                        in: ...Date(),
                        displayedComponents: .date
                    )
                    .datePickerStyle(.wheel)
                    .labelsHidden()

                    // Age display
                    VStack(spacing: 8) {
                        Text("Age: \(currentAge) years")
                            .font(.title2)
                            .fontWeight(.semibold)

                        Text("Week \(calculateCurrentWeek()) of ~4000")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.ultraThinMaterial)
                    )
                }

                Spacer()

                // Continue button
                Button(action: {
                    timelineManager.setBirthDate(selectedBirthDate)
                    isPresented = false
                }) {
                    Text("Start My Timeline")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue)
                        )
                }
                .padding(.horizontal)
            }
            .padding()
            .navigationBarHidden(true)
        }
        .interactiveDismissDisabled()
        .onAppear {
            // Set initial birth date to 25 years ago
            selectedBirthDate = Calendar.current.date(byAdding: .year, value: -25, to: Date()) ?? Date()
            updateAge()
        }
        .onChange(of: selectedBirthDate) { _, _ in
            updateAge()
        }
    }

    private func updateAge() {
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: selectedBirthDate, to: Date())
        currentAge = ageComponents.year ?? 0
    }

    private func calculateCurrentWeek() -> Int {
        return TimelineConfiguration.weekNumber(from: Date(), birthDate: selectedBirthDate)
    }
}

// MARK: - Week Jumper View

/// Quick navigation to jump to any specific week
struct WeekJumperView: View {
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Binding var isPresented: Bool
    @State private var targetWeek: String = ""
    @State private var targetAge: String = ""

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 16) {
                    Image(systemName: "arrow.up.arrow.down.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)

                    Text("Jump to Week")
                        .font(.title)
                        .fontWeight(.bold)

                    Text("Navigate to any point in your timeline")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                // Quick navigation buttons
                VStack(spacing: 16) {
                    Text("Quick Jump")
                        .font(.headline)
                        .fontWeight(.semibold)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        QuickJumpButton(title: "Birth", week: 1, action: jumpToWeek)
                        QuickJumpButton(title: "Age 18", week: calculateWeekForAge(18), action: jumpToWeek)
                        QuickJumpButton(title: "Age 25", week: calculateWeekForAge(25), action: jumpToWeek)
                        QuickJumpButton(title: "Age 30", week: calculateWeekForAge(30), action: jumpToWeek)
                        QuickJumpButton(title: "Age 40", week: calculateWeekForAge(40), action: jumpToWeek)
                        QuickJumpButton(title: "Current", week: timelineManager.currentWeekNumber, action: jumpToWeek)
                    }
                }

                // Manual input
                VStack(spacing: 16) {
                    Text("Or Enter Manually")
                        .font(.headline)
                        .fontWeight(.semibold)

                    HStack(spacing: 16) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Week Number")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            TextField("1-4000", text: $targetWeek)
                                .textFieldStyle(.roundedBorder)
                                .keyboardType(.numberPad)
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Or Age")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            TextField("0-77", text: $targetAge)
                                .textFieldStyle(.roundedBorder)
                                .keyboardType(.numberPad)
                                .onChange(of: targetAge) { _, newValue in
                                    if let age = Int(newValue) {
                                        targetWeek = "\(calculateWeekForAge(age))"
                                    }
                                }
                        }
                    }

                    Button(action: {
                        if let week = Int(targetWeek) {
                            jumpToWeek(week)
                        }
                    }) {
                        Text("Jump to Week")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.blue)
                            )
                    }
                    .disabled(targetWeek.isEmpty || Int(targetWeek) == nil)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("Navigate Timeline")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                }
            }
        }
        .onAppear {
            targetWeek = "\(timelineManager.selectedWeek?.weekNumber ?? timelineManager.currentWeekNumber)"
        }
    }

    private func calculateWeekForAge(_ age: Int) -> Int {
        return age * 52 + 1
    }

    private func jumpToWeek(_ week: Int) {
        let clampedWeek = max(1, min(TimelineConfiguration.totalLifeWeeks, week))
        timelineManager.navigateToWeek(clampedWeek)
        isPresented = false
    }
}

// MARK: - Quick Jump Button

struct QuickJumpButton: View {
    let title: String
    let week: Int
    let action: (Int) -> Void

    var body: some View {
        Button(action: { action(week) }) {
            VStack(spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text("Week \(week)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.ultraThinMaterial)
            )
        }
        .foregroundColor(.primary)
    }
}


