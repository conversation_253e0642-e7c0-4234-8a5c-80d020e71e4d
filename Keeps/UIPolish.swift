//
//  UIPolish.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI

// MARK: - Design System

/// Comprehensive design system for consistent UI polish with blur integration
struct KeepsDesignSystem {
    
    // MARK: - Colors
    struct Colors {
        // Primary colors
        static let primary = Color.blue
        static let secondary = Color(.systemGray)
        static let accent = Color.orange
        
        // Semantic colors
        static let success = Color.green
        static let warning = Color.orange
        static let error = Color.red
        static let info = Color.blue
        
        // Background colors
        static let background = Color(.systemBackground)
        static let secondaryBackground = Color(.secondarySystemBackground)
        static let groupedBackground = Color(.systemGroupedBackground)
        
        // Text colors
        static let primaryText = Color.primary
        static let secondaryText = Color.secondary
        static let tertiaryText = Color(.tertiaryLabel)
        
        // Relationship type colors
        static let family = Color.purple
        static let friend = Color.green
        static let colleague = Color.blue
        static let client = Color.orange
        static let mentor = Color.indigo
        static let other = Color.gray
    }
    
    // MARK: - Typography
    struct Typography {
        static let largeTitle = Font.largeTitle.weight(.bold)
        static let title = Font.title.weight(.semibold)
        static let title2 = Font.title2.weight(.semibold)
        static let title3 = Font.title3.weight(.medium)
        static let headline = Font.headline.weight(.semibold)
        static let subheadline = Font.subheadline.weight(.medium)
        static let body = Font.body
        static let callout = Font.callout
        static let caption = Font.caption
        static let caption2 = Font.caption2
        
        // Custom fonts
        static let displayLarge = Font.system(size: 32, weight: .bold, design: .rounded)
        static let displayMedium = Font.system(size: 24, weight: .semibold, design: .rounded)
        static let displaySmall = Font.system(size: 20, weight: .medium, design: .rounded)
    }
    
    // MARK: - Spacing
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        static let xxxl: CGFloat = 32
        
        // Semantic spacing
        static let cardPadding: CGFloat = 20
        static let sectionSpacing: CGFloat = 24
        static let itemSpacing: CGFloat = 12
    }
    
    // MARK: - Corner Radius
    struct CornerRadius {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        
        // Semantic radius
        static let card: CGFloat = 16
        static let button: CGFloat = 12
        static let badge: CGFloat = 8
    }
    
    // MARK: - Shadows
    struct Shadows {
        static let light = Shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        static let medium = Shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        static let heavy = Shadow(color: .black.opacity(0.15), radius: 16, x: 0, y: 8)
        
        struct Shadow {
            let color: Color
            let radius: CGFloat
            let x: CGFloat
            let y: CGFloat
        }
    }
    
    // MARK: - Blur Materials
    struct BlurMaterials {
        static let ultraThin = Material.ultraThinMaterial
        static let thin = Material.thinMaterial
        static let regular = Material.regularMaterial
        static let thick = Material.thickMaterial
        static let ultraThick = Material.ultraThickMaterial

        /// Enhanced blur with color tint - returns a view for background use
        static func tinted(_ material: Material, color: Color, opacity: Double = 0.1) -> some View {
            ZStack {
                Rectangle().fill(material)
                color.opacity(opacity)
            }
        }
    }

    // MARK: - Animations
    struct Animations {
        static let quick = Animation.easeInOut(duration: 0.2)
        static let standard = Animation.easeInOut(duration: 0.3)
        static let slow = Animation.easeInOut(duration: 0.5)

        static let spring = Animation.spring(response: 0.3, dampingFraction: 0.8)
        static let springBouncy = Animation.spring(response: 0.4, dampingFraction: 0.6)
        static let springGentle = Animation.spring(response: 0.6, dampingFraction: 0.9)
    }
}

// MARK: - Polished Components

/// Polished card container with consistent styling
struct PolishedCard<Content: View>: View {
    let content: Content
    let padding: CGFloat
    let cornerRadius: CGFloat
    let shadow: KeepsDesignSystem.Shadows.Shadow
    
    init(
        padding: CGFloat = KeepsDesignSystem.Spacing.cardPadding,
        cornerRadius: CGFloat = KeepsDesignSystem.CornerRadius.card,
        shadow: KeepsDesignSystem.Shadows.Shadow = KeepsDesignSystem.Shadows.light,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.padding = padding
        self.cornerRadius = cornerRadius
        self.shadow = shadow
    }
    
    var body: some View {
        content
            .padding(padding)
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(KeepsDesignSystem.Colors.background)
                    .shadow(
                        color: shadow.color,
                        radius: shadow.radius,
                        x: shadow.x,
                        y: shadow.y
                    )
            )
    }
}

/// Polished button with consistent styling and interactions
struct PolishedButton<Content: View>: View {
    let action: () -> Void
    let content: Content
    let style: ButtonStyle
    let accessibilityLabel: String
    let accessibilityHint: String?
    
    @State private var isPressed = false
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    
    enum ButtonStyle {
        case primary
        case secondary
        case tertiary
        case destructive
        
        var backgroundColor: Color {
            switch self {
            case .primary: return KeepsDesignSystem.Colors.primary
            case .secondary: return KeepsDesignSystem.Colors.secondaryBackground
            case .tertiary: return Color.clear
            case .destructive: return KeepsDesignSystem.Colors.error
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary, .destructive: return .white
            case .secondary, .tertiary: return KeepsDesignSystem.Colors.primaryText
            }
        }
        
        var borderColor: Color? {
            switch self {
            case .tertiary: return KeepsDesignSystem.Colors.secondary
            default: return nil
            }
        }
    }
    
    init(
        action: @escaping () -> Void,
        style: ButtonStyle = .primary,
        accessibilityLabel: String,
        accessibilityHint: String? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.action = action
        self.style = style
        self.accessibilityLabel = accessibilityLabel
        self.accessibilityHint = accessibilityHint
        self.content = content()
    }
    
    var body: some View {
        Button(action: {
            // Enhanced haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(
                style: accessibilityManager.isVoiceOverEnabled ? .medium : .light
            )
            impactFeedback.impactOccurred()
            
            action()
        }) {
            content
                .padding(.horizontal, KeepsDesignSystem.Spacing.lg)
                .padding(.vertical, KeepsDesignSystem.Spacing.md)
                .background(style.backgroundColor)
                .foregroundColor(style.foregroundColor)
                .overlay(
                    RoundedRectangle(cornerRadius: KeepsDesignSystem.CornerRadius.button)
                        .stroke(style.borderColor ?? Color.clear, lineWidth: 1)
                )
                .cornerRadius(KeepsDesignSystem.CornerRadius.button)
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .opacity(isPressed ? 0.8 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint ?? "")
        .accessibilityAddTraits(.isButton)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(accessibilityManager.springAnimation()) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

/// Polished badge with consistent styling
struct PolishedBadge: View {
    let text: String
    let color: Color
    let size: BadgeSize
    
    enum BadgeSize {
        case small
        case medium
        case large
        
        var font: Font {
            switch self {
            case .small: return KeepsDesignSystem.Typography.caption2
            case .medium: return KeepsDesignSystem.Typography.caption
            case .large: return KeepsDesignSystem.Typography.callout
            }
        }
        
        var padding: EdgeInsets {
            switch self {
            case .small: return EdgeInsets(top: 2, leading: 6, bottom: 2, trailing: 6)
            case .medium: return EdgeInsets(top: 4, leading: 8, bottom: 4, trailing: 8)
            case .large: return EdgeInsets(top: 6, leading: 12, bottom: 6, trailing: 12)
            }
        }
    }
    
    init(text: String, color: Color, size: BadgeSize = .medium) {
        self.text = text
        self.color = color
        self.size = size
    }
    
    var body: some View {
        Text(text)
            .font(size.font)
            .fontWeight(.medium)
            .foregroundColor(color)
            .padding(size.padding)
            .background(
                Capsule()
                    .fill(color.opacity(0.1))
            )
            .accessibilityLabel(text)
    }
}

/// Polished section header
struct PolishedSectionHeader: View {
    let title: String
    let subtitle: String?
    let action: (() -> Void)?
    let actionTitle: String?
    
    init(
        title: String,
        subtitle: String? = nil,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.title = title
        self.subtitle = subtitle
        self.actionTitle = actionTitle
        self.action = action
    }
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(KeepsDesignSystem.Typography.headline)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(KeepsDesignSystem.Typography.caption)
                        .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                }
            }
            
            Spacer()
            
            if let actionTitle = actionTitle, let action = action {
                Button(action: action) {
                    Text(actionTitle)
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .foregroundColor(KeepsDesignSystem.Colors.primary)
                }
            }
        }
        .accessibilityElement(children: .combine)
    }
}

// MARK: - View Extensions for Polish

extension View {
    /// Apply polished card styling
    func polishedCard(
        padding: CGFloat = KeepsDesignSystem.Spacing.cardPadding,
        cornerRadius: CGFloat = KeepsDesignSystem.CornerRadius.card,
        shadow: KeepsDesignSystem.Shadows.Shadow = KeepsDesignSystem.Shadows.light
    ) -> some View {
        PolishedCard(padding: padding, cornerRadius: cornerRadius, shadow: shadow) {
            self
        }
    }
    
    /// Apply polished animation
    func polishedAnimation<T: Equatable>(
        _ animation: Animation = KeepsDesignSystem.Animations.spring,
        value: T
    ) -> some View {
        self.animation(animation, value: value)
    }
    
    /// Apply polished shadow
    func polishedShadow(_ shadow: KeepsDesignSystem.Shadows.Shadow = KeepsDesignSystem.Shadows.light) -> some View {
        self.shadow(color: shadow.color, radius: shadow.radius, x: shadow.x, y: shadow.y)
    }

    /// Apply blur background with optional tint
    func blurBackground(
        _ material: Material = .ultraThinMaterial,
        tintColor: Color? = nil,
        tintOpacity: Double = 0.1,
        cornerRadius: CGFloat = 16
    ) -> some View {
        self.background(
            Group {
                if let tintColor = tintColor {
                    ZStack {
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(material)
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(tintColor.opacity(tintOpacity))
                    }
                } else {
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(material)
                }
            }
        )
    }

    /// Apply floating blur card effect
    func floatingBlurCard(
        material: Material = .regularMaterial,
        tintColor: Color? = nil,
        cornerRadius: CGFloat = 16,
        shadow: KeepsDesignSystem.Shadows.Shadow = KeepsDesignSystem.Shadows.medium
    ) -> some View {
        self
            .padding(16)
            .blurBackground(material, tintColor: tintColor, cornerRadius: cornerRadius)
            .polishedShadow(shadow)
    }

    /// Apply header blur effect
    func headerBlur(
        material: Material = .ultraThinMaterial,
        tintColor: Color? = nil,
        borderColor: Color? = nil
    ) -> some View {
        self
            .background(material, in: RoundedRectangle(cornerRadius: 0))
            .overlay(
                Rectangle()
                    .fill((borderColor ?? Color.blue).opacity(0.1))
                    .frame(height: 1),
                alignment: .bottom
            )
    }
}
