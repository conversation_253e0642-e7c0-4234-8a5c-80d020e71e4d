//
//  AppleWatchLayout.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Apple Watch home screen layout with physics-based interactions
struct AppleWatchLayout: View {
    let people: [Person]
    let peopleManager: PeopleManager
    @Binding var selectedPerson: Person?
    
    @State private var zoomLevel: CGFloat = 1.0
    @State private var panOffset: CGSize = .zero
    @State private var dragOffset: CGSize = .zero
    @State private var isDragging = false
    @State private var iconPositions: [CGPoint] = []
    @State private var deformedPositions: [CGPoint] = []
    @State private var dragLocation: CGPoint = .zero
    
    private let minZoom: CGFloat = 0.5
    private let maxZoom: CGFloat = 2.0
    private let iconSize: CGFloat = 60
    private let baseSpacing: CGFloat = 80
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                Color.black
                    .ignoresSafeArea()
                
                // Apple Watch icon grid
                ForEach(Array(people.enumerated()), id: \.element.id) { index, person in
                    AppleWatchIcon(
                        person: person,
                        isSelected: selectedPerson?.id == person.id,
                        zoomLevel: zoomLevel
                    )
                    .position(getIconPosition(for: index, in: geometry))
                    .scaleEffect(zoomLevel)
                    .animation(
                        .spring(response: 0.6, dampingFraction: 0.8),
                        value: zoomLevel
                    )
                    .animation(
                        .spring(response: 0.4, dampingFraction: 0.7),
                        value: isDragging ? deformedPositions : iconPositions
                    )
                    .onTapGesture {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            selectedPerson = selectedPerson?.id == person.id ? nil : person
                        }
                    }
                }
            }
            .offset(panOffset)
            .offset(dragOffset)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        handleDrag(value, in: geometry)
                    }
                    .onEnded { value in
                        handleDragEnd(value, in: geometry)
                    }
            )
            .gesture(
                MagnificationGesture()
                    .onChanged { value in
                        handleZoom(value)
                    }
                    .onEnded { value in
                        handleZoomEnd(value)
                    }
            )
            .onAppear {
                setupInitialPositions(in: geometry)
            }
            .onChange(of: geometry.size) { _, _ in
                setupInitialPositions(in: geometry)
            }
        }
    }
    
    private func getIconPosition(for index: Int, in geometry: GeometryProxy) -> CGPoint {
        if isDragging && index < deformedPositions.count {
            return deformedPositions[index]
        } else if index < iconPositions.count {
            return iconPositions[index]
        }
        return CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
    }
    
    private func setupInitialPositions(in geometry: GeometryProxy) {
        let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
        var positions: [CGPoint] = []
        
        // Apple Watch honeycomb pattern
        let spacing = baseSpacing * zoomLevel
        
        if people.isEmpty { return }
        
        // Center icon
        positions.append(center)
        
        if people.count == 1 {
            iconPositions = positions
            return
        }
        
        // Ring 1: 6 icons
        for i in 0..<min(6, people.count - 1) {
            let angle = Double(i) * .pi / 3
            let x = center.x + cos(angle) * spacing
            let y = center.y + sin(angle) * spacing
            positions.append(CGPoint(x: x, y: y))
        }
        
        if people.count <= 7 {
            iconPositions = positions
            return
        }
        
        // Ring 2: 12 icons
        for i in 0..<min(12, people.count - 7) {
            let angle = Double(i) * .pi / 6
            let x = center.x + cos(angle) * spacing * 2
            let y = center.y + sin(angle) * spacing * 2
            positions.append(CGPoint(x: x, y: y))
        }
        
        if people.count <= 19 {
            iconPositions = positions
            return
        }
        
        // Ring 3: 18 icons
        for i in 0..<min(18, people.count - 19) {
            let angle = Double(i) * .pi / 9
            let x = center.x + cos(angle) * spacing * 3
            let y = center.y + sin(angle) * spacing * 3
            positions.append(CGPoint(x: x, y: y))
        }
        
        iconPositions = positions
        deformedPositions = positions
    }
    
    private func handleDrag(_ value: DragGesture.Value, in geometry: GeometryProxy) {
        isDragging = true
        dragLocation = value.location
        
        // Create deformation effect
        deformedPositions = iconPositions.map { position in
            let distance = sqrt(pow(position.x - dragLocation.x, 2) + pow(position.y - dragLocation.y, 2))
            let maxDeformDistance: CGFloat = 120
            
            if distance < maxDeformDistance {
                let deformStrength = (maxDeformDistance - distance) / maxDeformDistance
                let pushDistance = deformStrength * 40
                
                let angle = atan2(position.y - dragLocation.y, position.x - dragLocation.x)
                let pushX = cos(angle) * pushDistance
                let pushY = sin(angle) * pushDistance
                
                return CGPoint(x: position.x + pushX, y: position.y + pushY)
            }
            return position
        }
        
        // Pan the entire grid
        dragOffset = CGSize(width: value.translation.width * 0.5, height: value.translation.height * 0.5)
    }
    
    private func handleDragEnd(_ value: DragGesture.Value, in geometry: GeometryProxy) {
        isDragging = false
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            deformedPositions = iconPositions
            
            // Add momentum to pan
            let velocity = CGSize(
                width: value.velocity.width * 0.1,
                height: value.velocity.height * 0.1
            )
            panOffset.width += velocity.width
            panOffset.height += velocity.height
            
            // Reset drag offset
            dragOffset = .zero
        }
        
        // Gradually return to center
        withAnimation(.spring(response: 1.5, dampingFraction: 0.8).delay(0.5)) {
            panOffset = .zero
        }
    }
    
    private func handleZoom(_ value: CGFloat) {
        let newZoom = max(minZoom, min(maxZoom, value))
        zoomLevel = newZoom
    }
    
    private func handleZoomEnd(_ value: CGFloat) {
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
            let finalZoom = max(minZoom, min(maxZoom, value))
            zoomLevel = finalZoom
            
            // Positions will be recalculated automatically on next render
        }
    }
}

/// Individual Apple Watch app icon
struct AppleWatchIcon: View {
    let person: Person
    let isSelected: Bool
    let zoomLevel: CGFloat
    
    private var iconSize: CGFloat {
        return 60 * zoomLevel
    }
    
    var body: some View {
        ZStack {
            // Apple Watch circular icon
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            person.relationshipType.color.opacity(0.9),
                            person.relationshipType.color,
                            person.relationshipType.color.opacity(0.7)
                        ],
                        center: UnitPoint(x: 0.3, y: 0.3),
                        startRadius: iconSize * 0.1,
                        endRadius: iconSize * 0.6
                    )
                )
                .frame(width: iconSize, height: iconSize)
                .overlay(
                    Circle()
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.5),
                                    Color.white.opacity(0.1),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
                .shadow(
                    color: Color.black.opacity(0.3),
                    radius: isSelected ? 8 : 4,
                    x: 0,
                    y: isSelected ? 4 : 2
                )
                .scaleEffect(isSelected ? 1.1 : 1.0)
            
            // Icon content
            Group {
                if person.avatarImageName.contains("person") {
                    Image(systemName: person.avatarImageName)
                        .font(.system(size: iconSize * 0.4, weight: .medium))
                        .foregroundColor(.white)
                } else {
                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: iconSize * 0.3, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                }
            }
            .shadow(color: .black.opacity(0.4), radius: 1, x: 0, y: 1)
            
            // Status indicators
            if person.isOnline {
                VStack {
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                            .overlay(
                                Circle()
                                    .stroke(Color.white, lineWidth: 1)
                            )
                    }
                    Spacer()
                }
                .frame(width: iconSize, height: iconSize)
                .padding(4)
            }
        }
    }
}
