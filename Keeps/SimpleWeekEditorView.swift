import SwiftUI

/// Simple, bulletproof week editor that actually works
/// No complex bindings, no mysterious nil issues - just works!
struct SimpleWeekEditorView: View {
    let weekNumber: Int
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Binding var isPresented: Bool
    
    // Local editing state
    @State private var title: String = ""
    @State private var selectedEmotionalTag: EmotionalTag = .growth
    @State private var insight: String = ""
    @State private var accomplishments: [String] = []
    @State private var newAccomplishment: String = ""
    
    // Get the week entry
    private var weekEntry: WeekEntry {
        return timelineManager.getWeekEntry(for: weekNumber)
    }
    
    var body: some View {
        NavigationView {
            Form {
                // Header section
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Week \(weekNumber)")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text(weekEntry.startDate, format: .dateTime.weekday(.wide).month(.wide).day().year())
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        if weekEntry.isCurrentWeek {
                            Text("This Week")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                // Title section
                Section("Week Title") {
                    TextField("What defined this week?", text: $title)
                        .textFieldStyle(.plain)
                }
                
                // Emotional theme section
                Section("Emotional Theme") {
                    Picker("Theme", selection: $selectedEmotionalTag) {
                        ForEach(EmotionalTag.allCases, id: \.self) { tag in
                            HStack {
                                Text(tag.emoji)
                                Text(tag.rawValue)
                            }
                            .tag(tag)
                        }
                    }
                    .pickerStyle(.menu)
                }
                
                // Insight section
                Section("Key Insight") {
                    TextEditor(text: $insight)
                        .frame(minHeight: 100)
                        .textFieldStyle(.plain)
                }
                .headerProminence(.increased)
                
                // Accomplishments section
                Section("Accomplishments") {
                    ForEach(Array(accomplishments.enumerated()), id: \.offset) { index, accomplishment in
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text(accomplishment)
                            Spacer()
                            Button("Remove") {
                                accomplishments.remove(at: index)
                            }
                            .foregroundColor(.red)
                            .font(.caption)
                        }
                    }
                    
                    HStack {
                        TextField("Add accomplishment...", text: $newAccomplishment)
                            .textFieldStyle(.plain)
                        
                        Button("Add") {
                            if !newAccomplishment.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                                accomplishments.append(newAccomplishment.trimmingCharacters(in: .whitespacesAndNewlines))
                                newAccomplishment = ""
                            }
                        }
                        .disabled(newAccomplishment.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                    }
                }
                
                // Stats section
                Section("Week Stats") {
                    HStack {
                        Image(systemName: "calendar")
                            .foregroundColor(.blue)
                        Text("Week \(weekNumber) of \(TimelineConfiguration.totalLifeWeeks)")
                        Spacer()
                        Text("\(Int((Double(weekNumber) / Double(TimelineConfiguration.totalLifeWeeks)) * 100))% complete")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if weekEntry.hasContent {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Week completed")
                            Spacer()
                        }
                    }
                }
            }
            .navigationTitle("Edit Week")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        isPresented = false
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveChanges()
                        isPresented = false
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            loadCurrentValues()
        }
    }
    
    /// Load current values from the week entry
    private func loadCurrentValues() {
        let week = weekEntry
        title = week.title
        selectedEmotionalTag = week.emotionalTag
        insight = week.insight
        accomplishments = week.accomplishments
    }
    
    /// Save changes back to the timeline manager
    private func saveChanges() {
        // Get the week entry and update its properties
        let week = weekEntry
        week.title = title.trimmingCharacters(in: .whitespacesAndNewlines)
        week.emotionalTag = selectedEmotionalTag
        week.insight = insight.trimmingCharacters(in: .whitespacesAndNewlines)
        week.accomplishments = accomplishments

        // Update through the timeline manager
        timelineManager.updateWeekEntry(week)
    }
}

// MARK: - Preview

#Preview {
    SimpleWeekEditorView(
        weekNumber: 1300,
        timelineManager: EvolutionTimelineManager(),
        isPresented: .constant(true)
    )
}
