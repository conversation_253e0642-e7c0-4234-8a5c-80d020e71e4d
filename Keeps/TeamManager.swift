//
//  TeamManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import Combine
import Foundation
import CoreData

/// Central manager for all team-related functionality and state management
/// Provides real-time updates, data persistence, and team operations
/// Enhanced with Core Data integration and project management capabilities
class TeamManager: ObservableObject {
    @Published var teams: [Team] = []
    @Published var selectedTeam: Team?
    @Published var searchText: String = ""
    @Published var selectedCategory: TeamCategory = .all
    @Published var notifications: [TeamNotification] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    // Core Data Integration
    private let context: NSManagedObjectContext
    private let teamEntityManager: TeamEntityManager

    // Project-People Integration
    @Published var projectPeopleIntegrationManager: ProjectPeopleIntegrationManager?

    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    private let teamsKey = "SavedTeams"

    init(context: NSManagedObjectContext = PersistenceController.shared.container.viewContext, peopleManager: PeopleManager? = nil) {
        self.context = context
        self.teamEntityManager = TeamEntityManager(context: context)
        loadTeams()
        setupRealTimeUpdates()
        loadSampleDataIfNeeded()
        migrateToCoreDateIfNeeded()

        // Initialize project-people integration if people manager is provided
        if let peopleManager = peopleManager {
            self.projectPeopleIntegrationManager = ProjectPeopleIntegrationManager(
                context: context,
                peopleManager: peopleManager,
                teamManager: self
            )
        }
    }
    
    // MARK: - Data Management
    
    /// Load teams from persistent storage (Core Data first, then UserDefaults fallback)
    private func loadTeams() {
        // Try loading from Core Data first
        loadTeamsFromCoreData()

        // If no Core Data teams, try UserDefaults (for migration)
        if teams.isEmpty {
            if let data = userDefaults.data(forKey: teamsKey),
               let savedTeams = try? JSONDecoder().decode([Team].self, from: data) {
                self.teams = savedTeams
            }
        }
    }

    /// Load teams from Core Data
    private func loadTeamsFromCoreData() {
        let request: NSFetchRequest<TeamEntity> = TeamEntity.fetchRequest()

        do {
            let entities = try context.fetch(request)
            self.teams = entities.map { teamEntityManager.createTeam(from: $0) }
        } catch {
            print("Error loading teams from Core Data: \(error)")
        }
    }

    /// Save teams to persistent storage (Core Data)
    func saveTeams() {
        saveTeamsToCoreData()

        // Keep UserDefaults as backup for now
        if let data = try? JSONEncoder().encode(teams) {
            userDefaults.set(data, forKey: teamsKey)
        }
    }

    /// Save teams to Core Data
    private func saveTeamsToCoreData() {
        // Clear existing entities
        let deleteRequest = NSBatchDeleteRequest(fetchRequest: TeamEntity.fetchRequest())
        try? context.execute(deleteRequest)

        // Create new entities
        for team in teams {
            _ = teamEntityManager.createTeamEntity(from: team)
        }

        // Save context
        do {
            try context.save()
        } catch {
            print("Error saving teams to Core Data: \(error)")
        }
    }

    /// Migrate from UserDefaults to Core Data if needed
    private func migrateToCoreDateIfNeeded() {
        // Check if we have UserDefaults data but no Core Data
        let request: NSFetchRequest<TeamEntity> = TeamEntity.fetchRequest()
        let coreDataCount = (try? context.count(for: request)) ?? 0

        if coreDataCount == 0 && !teams.isEmpty {
            print("Migrating teams from UserDefaults to Core Data...")
            saveTeamsToCoreData()
        }
    }
    
    /// Load sample data if no teams exist
    private func loadSampleDataIfNeeded() {
        if teams.isEmpty {
            teams = SampleData.createSampleTeams()
            saveTeams()
        }
    }

    /// Public method to reload sample data (for refresh functionality)
    func loadSampleData() {
        teams = SampleData.createSampleTeams()
        saveTeams()
    }
    
    // MARK: - Real-time Updates
    
    /// Setup real-time updates for team status and member activity
    private func setupRealTimeUpdates() {
        // Simulate real-time member status updates
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.updateMemberStatuses()
            }
            .store(in: &cancellables)
        
        // Auto-save when teams change
        $teams
            .debounce(for: .seconds(1), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.saveTeams()
            }
            .store(in: &cancellables)
    }
    
    /// Simulate real-time member status updates
    private func updateMemberStatuses() {
        for team in teams {
            for member in team.members {
                // Randomly update member online status
                if Bool.random() {
                    member.isOnline = Bool.random()
                    member.lastSeen = Date()
                    
                    // Update availability status occasionally
                    if Bool.random() && member.isOnline {
                        member.availability = TeamMember.AvailabilityStatus.allCases.randomElement() ?? .available
                    }
                }
            }
        }
    }
    
    // MARK: - Team Operations
    
    /// Create a new team
    func createTeam(name: String, description: String, category: TeamCategory, location: String) {
        let newTeam = Team(
            name: name,
            description: description,
            category: category,
            location: location,
            isActive: true,
            projectStatus: .planning
        )

        teams.append(newTeam)
        saveTeams()
        addGlobalNotification("New team '\(name)' created", type: .general)
    }
    
    /// Delete a team
    func deleteTeam(_ team: Team) {
        teams.removeAll { $0.id == team.id }
        saveTeams()
        addGlobalNotification("Team '\(team.name)' deleted", type: .general)

        if selectedTeam?.id == team.id {
            selectedTeam = nil
        }
    }
    
    /// Add member to team
    func addMemberToTeam(_ member: TeamMember, to team: Team) {
        team.addMember(member)
        addGlobalNotification("\(member.name) joined \(team.name)", type: .memberJoined)
    }
    
    /// Remove member from team
    func removeMemberFromTeam(_ member: TeamMember, from team: Team) {
        team.removeMember(member)
        addGlobalNotification("\(member.name) left \(team.name)", type: .memberLeft)
    }
    
    /// Add event to team
    func addEventToTeam(_ event: TeamEvent, to team: Team) {
        team.addEvent(event)
        addGlobalNotification("New event '\(event.title)' added to \(team.name)", type: .eventAdded)
    }
    
    /// Update team project status
    func updateTeamStatus(_ team: Team, status: Team.ProjectStatus) {
        team.updateProjectStatus(status)
        addGlobalNotification("\(team.name) status updated to \(status.rawValue)", type: .statusChanged)
    }
    
    // MARK: - Search and Filtering
    
    /// Get filtered teams based on search and category
    var filteredTeams: [Team] {
        var filtered = teams
        
        // Filter by category
        if selectedCategory != .all {
            filtered = filtered.filter { $0.category == selectedCategory }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            filtered = filtered.filter { team in
                team.name.localizedCaseInsensitiveContains(searchText) ||
                team.description.localizedCaseInsensitiveContains(searchText) ||
                team.members.contains { $0.name.localizedCaseInsensitiveContains(searchText) }
            }
        }
        
        return filtered.sorted { $0.lastActivity > $1.lastActivity }
    }
    
    // MARK: - Notifications
    
    /// Add a global notification
    private func addGlobalNotification(_ message: String, type: TeamNotification.NotificationType) {
        let notification = TeamNotification(message: message, timestamp: Date(), type: type)
        notifications.insert(notification, at: 0)
        
        // Keep only last 20 notifications
        if notifications.count > 20 {
            notifications = Array(notifications.prefix(20))
        }
    }
    
    /// Mark notification as read
    func markNotificationAsRead(_ notification: TeamNotification) {
        if let index = notifications.firstIndex(where: { $0.id == notification.id }) {
            notifications[index].isRead = true
        }
    }
    
    /// Clear all notifications
    func clearAllNotifications() {
        notifications.removeAll()
    }
    
    /// Get unread notification count
    var unreadNotificationCount: Int {
        notifications.filter { !$0.isRead }.count
    }
    
    // MARK: - Analytics
    
    /// Get team analytics
    func getTeamAnalytics() -> TeamAnalytics {
        let totalMembers = teams.reduce(0) { $0 + $1.memberCount }
        let onlineMembers = teams.reduce(0) { $0 + $1.onlineCount }
        let activeTeams = teams.filter { $0.isActive }.count
        let upcomingEvents = teams.reduce(0) { $0 + $1.upcomingEvents.count }
        
        return TeamAnalytics(
            totalTeams: teams.count,
            totalMembers: totalMembers,
            onlineMembers: onlineMembers,
            activeTeams: activeTeams,
            upcomingEvents: upcomingEvents
        )
    }
    
    // MARK: - Quick Actions
    
    /// Start a quick meeting for a team
    func startQuickMeeting(for team: Team) {
        let meeting = TeamEvent(
            title: "Quick Meeting",
            type: .meeting,
            date: Date(),
            time: "Now",
            platform: "Zoom",
            attendeeCount: team.onlineCount,
            attendees: team.members.filter { $0.isOnline }.map { $0.id.uuidString }
        )
        
        addEventToTeam(meeting, to: team)
    }
    
    /// Send team announcement
    func sendTeamAnnouncement(_ message: String, to team: Team) {
        team.addNotification("📢 Announcement: \(message)")
        addGlobalNotification("Announcement sent to \(team.name)", type: .general)
    }

    // MARK: - Enhanced Project Management

    /// Add project milestone to team
    func addProjectMilestone(to team: Team, title: String, description: String, targetDate: Date, priority: MilestonePriority = .medium) {
        teamEntityManager.addProjectMilestone(for: team.id, title: title, description: description, targetDate: targetDate, priority: priority)
        addGlobalNotification("New milestone '\(title)' added to \(team.name)", type: .general)
    }

    /// Complete project milestone
    func completeProjectMilestone(milestoneId: UUID, for team: Team) {
        teamEntityManager.completeProjectMilestone(milestoneId: milestoneId)
        addGlobalNotification("Milestone completed in \(team.name)", type: .general)
    }

    /// Add team goal
    func addTeamGoal(to team: Team, title: String, description: String, category: GoalCategory = .general, targetDate: Date? = nil) {
        teamEntityManager.addTeamGoal(for: team.id, title: title, description: description, targetDate: targetDate, category: category)
        addGlobalNotification("New goal '\(title)' set for \(team.name)", type: .general)
    }

    /// Add team resource
    func addTeamResource(to team: Team, name: String, type: ResourceType, description: String? = nil, url: String? = nil) {
        teamEntityManager.addTeamResource(for: team.id, name: name, type: type, description: description, url: url)
        addGlobalNotification("New resource '\(name)' added to \(team.name)", type: .general)
    }

    /// Assign role to team member
    func assignMemberRole(to team: Team, memberId: UUID, role: String, responsibilities: [String] = [], permissions: [String] = []) {
        teamEntityManager.assignMemberRole(for: team.id, memberId: memberId, role: role, responsibilities: responsibilities, permissions: permissions)

        if let member = team.members.first(where: { $0.id == memberId }) {
            addGlobalNotification("\(member.name) assigned role '\(role)' in \(team.name)", type: .general)
        }
    }

    /// Get team progress
    func getTeamProgress(for team: Team) -> Double {
        return teamEntityManager.calculateTeamProgress(for: team)
    }

    /// Get project milestones for team
    func getProjectMilestones(for team: Team) -> [ProjectMilestone] {
        return teamEntityManager.projectMilestones[team.id] ?? []
    }

    /// Get team goals
    func getTeamGoals(for team: Team) -> [TeamGoal] {
        return teamEntityManager.teamGoals[team.id] ?? []
    }

    /// Get team resources
    func getTeamResources(for team: Team) -> [TeamResource] {
        return teamEntityManager.teamResources[team.id] ?? []
    }

    /// Get member roles for team
    func getMemberRoles(for team: Team) -> [MemberRole] {
        return teamEntityManager.memberRoles[team.id] ?? []
    }

    /// Link team to timeline entry
    func linkTeamToTimelineEntry(team: Team, timelineEntryId: UUID) {
        teamEntityManager.linkTeamToTimelineEntry(teamId: team.id, timelineEntryId: timelineEntryId)
        addGlobalNotification("\(team.name) linked to timeline entry", type: .general)
    }

    /// Get timeline entries linked to team
    func getLinkedTimelineEntries(for team: Team) -> [TimelineEntryEntity] {
        return teamEntityManager.getLinkedTimelineEntries(for: team.id)
    }

    // MARK: - Smart Team Formation

    /// Setup project-people integration with people manager
    func setupProjectPeopleIntegration(with peopleManager: PeopleManager) {
        self.projectPeopleIntegrationManager = ProjectPeopleIntegrationManager(
            context: context,
            peopleManager: peopleManager,
            teamManager: self
        )
    }

    /// Generate smart team formation suggestions
    func generateTeamFormationSuggestions(for projectType: ProjectType, requiredSkills: [String], teamSize: Int = 5) -> [TeamFormationSuggestion] {
        guard let integrationManager = projectPeopleIntegrationManager else {
            print("Project-People integration not initialized")
            return []
        }

        return integrationManager.generateTeamFormationSuggestions(
            for: projectType,
            requiredSkills: requiredSkills,
            teamSize: teamSize
        )
    }

    /// Generate role assignments for existing team
    func generateRoleAssignments(for team: Team, projectType: ProjectType) -> [RoleAssignmentRecommendation] {
        guard let integrationManager = projectPeopleIntegrationManager else {
            print("Project-People integration not initialized")
            return []
        }

        return integrationManager.generateRoleAssignments(for: team, projectType: projectType)
    }

    /// Create collaborative goal
    func createCollaborativeGoal(title: String, description: String, participants: [Person], team: Team?, targetDate: Date) -> CollaborativeGoal? {
        guard let integrationManager = projectPeopleIntegrationManager else {
            print("Project-People integration not initialized")
            return nil
        }

        return integrationManager.createCollaborativeGoal(
            title: title,
            description: description,
            participants: participants,
            team: team,
            targetDate: targetDate
        )
    }

    /// Track individual contribution
    func trackContribution(personId: UUID, teamId: UUID, contribution: String, type: ContributionType, impact: ContributionImpact) {
        guard let integrationManager = projectPeopleIntegrationManager else {
            print("Project-People integration not initialized")
            return
        }

        integrationManager.trackContribution(
            personId: personId,
            teamId: teamId,
            contribution: contribution,
            type: type,
            impact: impact
        )
    }

    /// Get contribution summary for person in team
    func getContributionSummary(personId: UUID, teamId: UUID) -> ContributionSummary? {
        guard let integrationManager = projectPeopleIntegrationManager else {
            print("Project-People integration not initialized")
            return nil
        }

        return integrationManager.getContributionSummary(personId: personId, teamId: teamId)
    }
}

/// Analytics data structure
struct TeamAnalytics {
    let totalTeams: Int
    let totalMembers: Int
    let onlineMembers: Int
    let activeTeams: Int
    let upcomingEvents: Int
    
    var onlinePercentage: Double {
        guard totalMembers > 0 else { return 0 }
        return Double(onlineMembers) / Double(totalMembers) * 100
    }
    
    var activeTeamPercentage: Double {
        guard totalTeams > 0 else { return 0 }
        return Double(activeTeams) / Double(totalTeams) * 100
    }
}
