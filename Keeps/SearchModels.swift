//
//  SearchModels.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Search Core Models

/// Unified search result that can represent content from any section
struct SearchResult: Identifiable, Hashable {
    let id = UUID()
    let title: String
    let subtitle: String?
    let content: String
    let section: SearchSection
    let entityId: UUID
    let relevanceScore: Double
    let lastModified: Date
    let tags: [String]
    let metadata: SearchMetadata
    
    /// Quick access to the source entity
    var sourceEntity: SearchableEntity {
        switch section {
        case .people:
            return .person(entityId)
        case .teams:
            return .team(entityId)
        case .timeline:
            return .timelineEntry(entityId)
        case .all:
            return .unknown(entityId)
        }
    }
}

/// Sections available for searching
enum SearchSection: String, CaseIterable, Identifiable {
    case all = "All"
    case people = "People"
    case teams = "Teams"
    case timeline = "Timeline"
    
    var id: String { rawValue }
    
    var icon: String {
        switch self {
        case .all: return "magnifyingglass"
        case .people: return "person.crop.circle"
        case .teams: return "person.3"
        case .timeline: return "timeline.selection"
        }
    }
    
    var color: Color {
        switch self {
        case .all: return .blue
        case .people: return .green
        case .teams: return .orange
        case .timeline: return .purple
        }
    }
}

/// Searchable entity types for navigation
enum SearchableEntity {
    case person(UUID)
    case team(UUID)
    case timelineEntry(UUID)
    case unknown(UUID)
}

/// Additional metadata for search results
struct SearchMetadata: Hashable {
    let relationshipType: String?
    let teamRole: String?
    let timelineCategory: String?
    let priority: SearchPriority
    let hasAttachments: Bool
    let isRecent: Bool
    let isFavorite: Bool
}

/// Priority levels for search results
enum SearchPriority: Int, CaseIterable {
    case low = 1
    case medium = 2
    case high = 3
    case critical = 4
    
    var color: Color {
        switch self {
        case .low: return .gray
        case .medium: return .blue
        case .high: return .orange
        case .critical: return .red
        }
    }
}

// MARK: - Search Query Models

/// Comprehensive search query with filters and options
struct SearchQuery {
    var text: String
    var section: SearchSection
    var filters: SearchFilters
    var sortBy: SearchSortOption
    var limit: Int
    
    init(text: String = "", 
         section: SearchSection = .all,
         filters: SearchFilters = SearchFilters(),
         sortBy: SearchSortOption = .relevance,
         limit: Int = 50) {
        self.text = text
        self.section = section
        self.filters = filters
        self.sortBy = sortBy
        self.limit = limit
    }
    
    /// Check if query has any active filters
    var hasActiveFilters: Bool {
        !filters.isEmpty
    }
    
    /// Check if query is empty
    var isEmpty: Bool {
        text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
}

/// Advanced search filters
struct SearchFilters {
    var dateRange: DateRange?
    var relationshipTypes: Set<String>
    var teamRoles: Set<String>
    var timelineCategories: Set<String>
    var priorities: Set<SearchPriority>
    var tags: Set<String>
    var hasAttachments: Bool?
    var isFavorite: Bool?
    var isRecent: Bool?
    
    init() {
        self.relationshipTypes = []
        self.teamRoles = []
        self.timelineCategories = []
        self.priorities = []
        self.tags = []
    }
    
    /// Check if any filters are active
    var isEmpty: Bool {
        dateRange == nil &&
        relationshipTypes.isEmpty &&
        teamRoles.isEmpty &&
        timelineCategories.isEmpty &&
        priorities.isEmpty &&
        tags.isEmpty &&
        hasAttachments == nil &&
        isFavorite == nil &&
        isRecent == nil
    }
    
    /// Count of active filters
    var activeFilterCount: Int {
        var count = 0
        if dateRange != nil { count += 1 }
        if !relationshipTypes.isEmpty { count += 1 }
        if !teamRoles.isEmpty { count += 1 }
        if !timelineCategories.isEmpty { count += 1 }
        if !priorities.isEmpty { count += 1 }
        if !tags.isEmpty { count += 1 }
        if hasAttachments != nil { count += 1 }
        if isFavorite != nil { count += 1 }
        if isRecent != nil { count += 1 }
        return count
    }
}

/// Date range filter options
struct DateRange {
    let start: Date
    let end: Date
    
    static let today = DateRange(
        start: Calendar.current.startOfDay(for: Date()),
        end: Date()
    )
    
    static let thisWeek = DateRange(
        start: Calendar.current.dateInterval(of: .weekOfYear, for: Date())?.start ?? Date(),
        end: Date()
    )
    
    static let thisMonth = DateRange(
        start: Calendar.current.dateInterval(of: .month, for: Date())?.start ?? Date(),
        end: Date()
    )
    
    static let thisYear = DateRange(
        start: Calendar.current.dateInterval(of: .year, for: Date())?.start ?? Date(),
        end: Date()
    )
}

/// Search sorting options
enum SearchSortOption: String, CaseIterable, Identifiable {
    case relevance = "Relevance"
    case dateModified = "Date Modified"
    case dateCreated = "Date Created"
    case alphabetical = "Alphabetical"
    case priority = "Priority"
    
    var id: String { rawValue }
    
    var icon: String {
        switch self {
        case .relevance: return "star.fill"
        case .dateModified: return "clock.fill"
        case .dateCreated: return "calendar"
        case .alphabetical: return "textformat.abc"
        case .priority: return "exclamationmark.triangle.fill"
        }
    }
}

// MARK: - Search History Models

/// Search history entry
struct SearchHistoryEntry: Identifiable, Codable {
    var id = UUID()
    let query: String
    let section: String
    let timestamp: Date
    let resultCount: Int

    init(query: String, section: SearchSection, resultCount: Int) {
        self.id = UUID()
        self.query = query
        self.section = section.rawValue
        self.timestamp = Date()
        self.resultCount = resultCount
    }
}

/// Search suggestions for auto-complete
struct SearchSuggestion: Identifiable, Hashable {
    let id = UUID()
    let text: String
    let section: SearchSection
    let type: SuggestionType
    let frequency: Int
    
    enum SuggestionType {
        case recent
        case popular
        case smart
        case entity
    }
}

// MARK: - Search Analytics Models

/// Search analytics for improving user experience
struct SearchAnalytics {
    var totalSearches: Int
    var averageResultsPerSearch: Double
    var mostSearchedTerms: [String: Int]
    var sectionUsage: [SearchSection: Int]
    var averageSearchTime: TimeInterval
    var clickThroughRate: Double
    
    init() {
        self.totalSearches = 0
        self.averageResultsPerSearch = 0.0
        self.mostSearchedTerms = [:]
        self.sectionUsage = [:]
        self.averageSearchTime = 0.0
        self.clickThroughRate = 0.0
    }
}

/// Search performance metrics
struct SearchPerformanceMetrics {
    let searchTime: TimeInterval
    let resultCount: Int
    let query: String
    let timestamp: Date
    
    init(searchTime: TimeInterval, resultCount: Int, query: String) {
        self.searchTime = searchTime
        self.resultCount = resultCount
        self.query = query
        self.timestamp = Date()
    }
}
