//
//  TimelineEnhancedModels.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Timeline Accomplishment

/// Represents a specific accomplishment within a week entry
/// Provides categorization, impact tracking, and connection to people/teams
struct TimelineAccomplishment: Identifiable, Codable {
    let id: UUID
    let weekEntryId: UUID
    var title: String
    var description: String?
    var category: AccomplishmentCategory
    var impact: ImpactLevel
    var linkedPeople: [UUID] // People who contributed or were involved
    var linkedTeams: [UUID] // Teams associated with this accomplishment
    var tags: [String] // Custom tags for filtering and organization
    var evidence: [EvidenceItem] // Photos, documents, links as proof
    var createdDate: Date
    var lastModified: Date
    
    init(id: UUID = UUID(), weekEntryId: UUID, title: String, description: String? = nil, category: AccomplishmentCategory, impact: ImpactLevel = .medium, linkedPeople: [UUID] = [], linkedTeams: [UUID] = [], tags: [String] = [], evidence: [EvidenceItem] = []) {
        self.id = id
        self.weekEntryId = weekEntryId
        self.title = title
        self.description = description
        self.category = category
        self.impact = impact
        self.linkedPeople = linkedPeople
        self.linkedTeams = linkedTeams
        self.tags = tags
        self.evidence = evidence
        self.createdDate = Date()
        self.lastModified = Date()
    }
}

// MARK: - Accomplishment Category

enum AccomplishmentCategory: String, CaseIterable, Codable {
    case career = "career"
    case health = "health"
    case relationships = "relationships"
    case learning = "learning"
    case creativity = "creativity"
    case finance = "finance"
    case travel = "travel"
    case personal = "personal"
    case family = "family"
    case community = "community"
    case spiritual = "spiritual"
    case hobby = "hobby"
    
    var displayName: String {
        switch self {
        case .career: return "Career"
        case .health: return "Health & Fitness"
        case .relationships: return "Relationships"
        case .learning: return "Learning & Growth"
        case .creativity: return "Creativity"
        case .finance: return "Finance"
        case .travel: return "Travel"
        case .personal: return "Personal Development"
        case .family: return "Family"
        case .community: return "Community"
        case .spiritual: return "Spiritual"
        case .hobby: return "Hobbies"
        }
    }
    
    var color: Color {
        switch self {
        case .career: return .blue
        case .health: return .green
        case .relationships: return .pink
        case .learning: return .purple
        case .creativity: return .orange
        case .finance: return .yellow
        case .travel: return .cyan
        case .personal: return .indigo
        case .family: return .red
        case .community: return .brown
        case .spiritual: return .mint
        case .hobby: return .teal
        }
    }
    
    var icon: String {
        switch self {
        case .career: return "briefcase.fill"
        case .health: return "heart.fill"
        case .relationships: return "person.2.fill"
        case .learning: return "book.fill"
        case .creativity: return "paintbrush.fill"
        case .finance: return "dollarsign.circle.fill"
        case .travel: return "airplane"
        case .personal: return "person.fill"
        case .family: return "house.fill"
        case .community: return "building.2.fill"
        case .spiritual: return "leaf.fill"
        case .hobby: return "gamecontroller.fill"
        }
    }
}

// MARK: - Impact Level

enum ImpactLevel: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case transformative = "transformative"
    
    var displayName: String {
        switch self {
        case .low: return "Small Win"
        case .medium: return "Good Progress"
        case .high: return "Major Achievement"
        case .transformative: return "Life Changing"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .gray
        case .medium: return .blue
        case .high: return .orange
        case .transformative: return .red
        }
    }
    
    var weight: Double {
        switch self {
        case .low: return 1.0
        case .medium: return 2.0
        case .high: return 4.0
        case .transformative: return 8.0
        }
    }
}

// MARK: - Timeline Reflection

/// Represents deep reflection and learning capture for a week
struct TimelineReflection: Identifiable, Codable {
    let id: UUID
    let weekEntryId: UUID
    var content: String
    var type: ReflectionType
    var mood: EmotionalTag?
    var learnings: [String] // Key learnings from the week
    var gratitude: [String] // Things to be grateful for
    var challenges: [String] // Challenges faced and how they were handled
    var improvements: [String] // Areas for improvement next week
    var createdDate: Date
    var lastModified: Date
    
    init(id: UUID = UUID(), weekEntryId: UUID, content: String, type: ReflectionType, mood: EmotionalTag? = nil, learnings: [String] = [], gratitude: [String] = [], challenges: [String] = [], improvements: [String] = []) {
        self.id = id
        self.weekEntryId = weekEntryId
        self.content = content
        self.type = type
        self.mood = mood
        self.learnings = learnings
        self.gratitude = gratitude
        self.challenges = challenges
        self.improvements = improvements
        self.createdDate = Date()
        self.lastModified = Date()
    }
}

// MARK: - Reflection Type

enum ReflectionType: String, CaseIterable, Codable {
    case general = "general"
    case learning = "learning"
    case gratitude = "gratitude"
    case challenge = "challenge"
    case goal = "goal"
    case relationship = "relationship"
    case career = "career"
    case health = "health"
    
    var displayName: String {
        switch self {
        case .general: return "General Reflection"
        case .learning: return "Learning & Growth"
        case .gratitude: return "Gratitude"
        case .challenge: return "Challenges & Solutions"
        case .goal: return "Goal Progress"
        case .relationship: return "Relationships"
        case .career: return "Career Development"
        case .health: return "Health & Wellness"
        }
    }
    
    var icon: String {
        switch self {
        case .general: return "text.bubble"
        case .learning: return "lightbulb"
        case .gratitude: return "heart"
        case .challenge: return "mountain.2"
        case .goal: return "target"
        case .relationship: return "person.2"
        case .career: return "briefcase"
        case .health: return "heart.text.square"
        }
    }
}

// MARK: - Streak Data

/// Tracks streaks and patterns for accomplishments and habits
struct StreakData: Codable {
    let weekEntryId: UUID
    var accomplishmentStreaks: [AccomplishmentCategory: Int] = [:]
    var longestStreaks: [AccomplishmentCategory: Int] = [:]
    var weeklyScore: Double = 0.0 // Calculated based on accomplishments and impact
    var consistencyRating: ConsistencyRating = .developing
    var createdDate: Date
    var lastUpdated: Date
    
    init(weekEntryId: UUID) {
        self.weekEntryId = weekEntryId
        self.createdDate = Date()
        self.lastUpdated = Date()
    }
    
    mutating func addAccomplishment(_ accomplishment: TimelineAccomplishment) {
        // Update streak for this category
        accomplishmentStreaks[accomplishment.category, default: 0] += 1
        
        // Update longest streak if necessary
        let currentStreak = accomplishmentStreaks[accomplishment.category] ?? 0
        if currentStreak > (longestStreaks[accomplishment.category] ?? 0) {
            longestStreaks[accomplishment.category] = currentStreak
        }
        
        // Update weekly score based on impact
        weeklyScore += accomplishment.impact.weight
        
        // Update consistency rating
        updateConsistencyRating()
        
        lastUpdated = Date()
    }
    
    private mutating func updateConsistencyRating() {
        let totalCategories = AccomplishmentCategory.allCases.count
        let activeCategories = accomplishmentStreaks.keys.count
        let ratio = Double(activeCategories) / Double(totalCategories)
        
        switch ratio {
        case 0.0..<0.2:
            consistencyRating = .starting
        case 0.2..<0.4:
            consistencyRating = .developing
        case 0.4..<0.6:
            consistencyRating = .consistent
        case 0.6..<0.8:
            consistencyRating = .strong
        default:
            consistencyRating = .exceptional
        }
    }
}

// MARK: - Consistency Rating

enum ConsistencyRating: String, CaseIterable, Codable {
    case starting = "starting"
    case developing = "developing"
    case consistent = "consistent"
    case strong = "strong"
    case exceptional = "exceptional"
    
    var displayName: String {
        switch self {
        case .starting: return "Getting Started"
        case .developing: return "Building Momentum"
        case .consistent: return "Consistent Progress"
        case .strong: return "Strong Performance"
        case .exceptional: return "Exceptional Achievement"
        }
    }
    
    var color: Color {
        switch self {
        case .starting: return .gray
        case .developing: return .blue
        case .consistent: return .green
        case .strong: return .orange
        case .exceptional: return .purple
        }
    }
}

// MARK: - Life Phase

/// Represents different phases of life for contextual timeline organization
struct LifePhase: Identifiable, Codable {
    let id = UUID()
    var name: String
    var startAge: Double
    var endAge: Double
    var colorName: String // Store color as string for Codable compliance
    var description: String
    var milestones: [String] = [] // Typical milestones for this phase

    init(name: String, startAge: Double, endAge: Double, color: Color, description: String, milestones: [String] = []) {
        self.name = name
        self.startAge = startAge
        self.endAge = endAge
        self.colorName = colorToString(color)
        self.description = description
        self.milestones = milestones
    }

    /// Get SwiftUI Color from stored color name
    var color: Color {
        return stringToColor(colorName)
    }
}

// MARK: - Color Conversion Helpers

private func colorToString(_ color: Color) -> String {
    // Map common SwiftUI colors to string representations
    switch color {
    case .blue: return "blue"
    case .green: return "green"
    case .orange: return "orange"
    case .purple: return "purple"
    case .red: return "red"
    case .brown: return "brown"
    case .gray: return "gray"
    case .indigo: return "indigo"
    default: return "blue" // Default fallback
    }
}

private func stringToColor(_ colorName: String) -> Color {
    switch colorName {
    case "blue": return .blue
    case "green": return .green
    case "orange": return .orange
    case "purple": return .purple
    case "red": return .red
    case "brown": return .brown
    case "gray": return .gray
    case "indigo": return .indigo
    default: return .blue // Default fallback
    }
}

// MARK: - Milestone Connection

/// Connects timeline entries to team milestones and personal goals
struct MilestoneConnection: Identifiable, Codable {
    let id: UUID
    let weekEntryId: UUID
    var teamId: UUID?
    var milestoneId: UUID
    var connectionType: MilestoneConnectionType
    var description: String?
    var createdDate: Date
    
    init(id: UUID = UUID(), weekEntryId: UUID, teamId: UUID? = nil, milestoneId: UUID, connectionType: MilestoneConnectionType, description: String? = nil) {
        self.id = id
        self.weekEntryId = weekEntryId
        self.teamId = teamId
        self.milestoneId = milestoneId
        self.connectionType = connectionType
        self.description = description
        self.createdDate = Date()
    }
}

// MARK: - Milestone Connection Type

enum MilestoneConnectionType: String, CaseIterable, Codable {
    case achieved = "achieved"
    case progressed = "progressed"
    case started = "started"
    case blocked = "blocked"
    case pivoted = "pivoted"
    
    var displayName: String {
        switch self {
        case .achieved: return "Milestone Achieved"
        case .progressed: return "Made Progress"
        case .started: return "Started Working"
        case .blocked: return "Encountered Blocker"
        case .pivoted: return "Changed Direction"
        }
    }
    
    var color: Color {
        switch self {
        case .achieved: return .green
        case .progressed: return .blue
        case .started: return .orange
        case .blocked: return .red
        case .pivoted: return .purple
        }
    }
}

// MARK: - Evidence Item

/// Represents evidence/proof for accomplishments (photos, documents, links)
struct EvidenceItem: Identifiable, Codable {
    let id: UUID
    var type: EvidenceType
    var title: String
    var url: String? // For links or file paths
    var data: Data? // For embedded content
    var description: String?
    var createdDate: Date
    
    init(id: UUID = UUID(), type: EvidenceType, title: String, url: String? = nil, data: Data? = nil, description: String? = nil) {
        self.id = id
        self.type = type
        self.title = title
        self.url = url
        self.data = data
        self.description = description
        self.createdDate = Date()
    }
}

// MARK: - Evidence Type

enum EvidenceType: String, CaseIterable, Codable {
    case photo = "photo"
    case document = "document"
    case link = "link"
    case video = "video"
    case audio = "audio"
    case note = "note"
    
    var displayName: String {
        switch self {
        case .photo: return "Photo"
        case .document: return "Document"
        case .link: return "Link"
        case .video: return "Video"
        case .audio: return "Audio"
        case .note: return "Note"
        }
    }
    
    var icon: String {
        switch self {
        case .photo: return "photo"
        case .document: return "doc"
        case .link: return "link"
        case .video: return "video"
        case .audio: return "waveform"
        case .note: return "note.text"
        }
    }
}
