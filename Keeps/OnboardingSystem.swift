//
//  OnboardingSystem.swift
//  Keeps
//
//  Revolutionary onboarding system for Keeps app
//  Introduces unified People ↔ Teams ↔ Timeline concept with Apple-style animations
//

import SwiftUI
import CoreData

// MARK: - Onboarding Manager

/// Manages the onboarding flow state and progression
class OnboardingManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isOnboardingComplete = false
    @Published var currentStep: OnboardingStep = .welcome
    @Published var progress: Double = 0.0
    @Published var isAnimating = false
    
    // MARK: - User Setup Data
    
    @Published var userName = ""
    @Published var userBirthDate = Date()
    @Published var selectedGoals: Set<LifeGoal> = []
    @Published var initialPeople: [OnboardingPerson] = []
    @Published var firstTeamName = ""
    @Published var firstTeamGoal = ""
    
    // MARK: - Private Properties

    private let userDefaults = UserDefaults.standard
    private let onboardingKey = "hasCompletedOnboarding"
    private let context = PersistenceController.shared.container.viewContext
    
    // MARK: - Initialization
    
    init() {
        checkOnboardingStatus()
    }
    
    // MARK: - Public Methods
    
    /// Check if user has completed onboarding
    func checkOnboardingStatus() {
        isOnboardingComplete = userDefaults.bool(forKey: onboardingKey)
    }
    
    /// Move to next onboarding step
    func nextStep() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            isAnimating = true
            
            switch currentStep {
            case .welcome:
                currentStep = .concept
                progress = 0.2
            case .concept:
                currentStep = .personalInfo
                progress = 0.4
            case .personalInfo:
                currentStep = .relationships
                progress = 0.6
            case .relationships:
                currentStep = .teams
                progress = 0.8
            case .teams:
                currentStep = .timeline
                progress = 1.0
            case .timeline:
                completeOnboarding()
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.isAnimating = false
            }
        }
    }
    
    /// Go back to previous step
    func previousStep() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            isAnimating = true
            
            switch currentStep {
            case .welcome:
                break
            case .concept:
                currentStep = .welcome
                progress = 0.0
            case .personalInfo:
                currentStep = .concept
                progress = 0.2
            case .relationships:
                currentStep = .personalInfo
                progress = 0.4
            case .teams:
                currentStep = .relationships
                progress = 0.6
            case .timeline:
                currentStep = .teams
                progress = 0.8
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.isAnimating = false
            }
        }
    }
    
    /// Complete onboarding and save user data
    func completeOnboarding() {
        userDefaults.set(true, forKey: onboardingKey)
        isOnboardingComplete = true
        
        // Save user setup data to Core Data
        saveOnboardingData()
    }
    
    /// Skip onboarding (for development/testing)
    func skipOnboarding() {
        userDefaults.set(true, forKey: onboardingKey)
        isOnboardingComplete = true
    }
    
    // MARK: - Private Methods
    
    private func saveOnboardingData() {
        print("💾 Saving onboarding data...")

        // Save user profile data to UserDefaults
        saveUserProfile()

        // Save people to Core Data
        savePeopleData()

        // Save team data to Core Data
        saveTeamData()

        // Save timeline setup
        saveTimelineData()

        print("✅ Onboarding data saved successfully!")
    }

    private func saveUserProfile() {
        // Save basic user profile information
        userDefaults.set(userName, forKey: "userName")
        userDefaults.set(userBirthDate, forKey: "userBirthDate")

        // Save selected goals
        let goalStrings = selectedGoals.map { $0.rawValue }
        userDefaults.set(goalStrings, forKey: "userGoals")

        print("✅ User profile saved")
    }

    private func savePeopleData() {
        guard !initialPeople.isEmpty else { return }

        // Convert OnboardingPerson to Person objects and save to Core Data
        for onboardingPerson in initialPeople {
            let person = Person(
                name: onboardingPerson.name,
                relationshipType: mapRelationshipType(onboardingPerson.relationship),
                interactionFrequency: .weekly // Default for onboarding people
            )

            // Create PersonEntity in Core Data
            let personEntity = PersonEntity(context: context)
            personEntity.id = person.id
            personEntity.name = person.name
            personEntity.relationshipType = person.relationshipType.rawValue
            personEntity.interactionFrequency = person.interactionFrequency.rawValue
            personEntity.isOnline = false
            personEntity.lastSeen = Date()
            personEntity.availability = "offline"
            personEntity.isFavorite = onboardingPerson.importance >= 4
        }

        // Save context
        do {
            try context.save()
            print("✅ People data saved to Core Data")
        } catch {
            print("❌ Error saving people data: \(error)")
        }
    }

    private func saveTeamData() {
        guard !firstTeamName.isEmpty else { return }

        // Create team entity in Core Data
        let teamEntity = TeamEntity(context: context)
        teamEntity.id = UUID()
        teamEntity.name = firstTeamName
        teamEntity.teamDescription = firstTeamGoal
        teamEntity.category = "personal"
        teamEntity.projectStatus = "active"
        teamEntity.isActive = true
        teamEntity.lastActivity = Date()
        teamEntity.createdDate = Date()
        teamEntity.progress = 0.0

        // Save context
        do {
            try context.save()
            print("✅ Team data saved to Core Data")
        } catch {
            print("❌ Error saving team data: \(error)")
        }
    }

    private func saveTimelineData() {
        // Calculate current week number based on birth date
        let calendar = Calendar.current
        let ageInDays = calendar.dateComponents([.day], from: userBirthDate, to: Date()).day ?? 0
        let currentWeek = Int(ageInDays / 7) + 1

        // Create initial timeline entry for current week
        let timelineEntity = TimelineEntryEntity(context: context)
        timelineEntity.id = UUID()
        timelineEntity.weekNumber = Int32(currentWeek)
        timelineEntity.startDate = Date()
        timelineEntity.endDate = calendar.date(byAdding: .day, value: 6, to: Date()) ?? Date()
        timelineEntity.title = "Welcome to Keeps!"
        timelineEntity.insight = "Started my journey with Keeps - tracking \(initialPeople.count) people and \(firstTeamName.isEmpty ? 0 : 1) team."
        timelineEntity.emotionalTag = "excited"
        timelineEntity.isCompleted = false
        timelineEntity.isCurrentWeek = true
        timelineEntity.createdDate = Date()
        timelineEntity.lastModified = Date()

        // Save context
        do {
            try context.save()
            print("✅ Timeline data saved to Core Data")
        } catch {
            print("❌ Error saving timeline data: \(error)")
        }
    }

    private func mapRelationshipType(_ relationship: String) -> Person.RelationshipType {
        switch relationship.lowercased() {
        case "family": return .family
        case "friend": return .friend
        case "colleague": return .colleague
        case "mentor": return .mentor
        case "partner": return .family // Map partner to family as closest match
        default: return .other
        }
    }
}

// MARK: - Onboarding Models

/// Represents different steps in the onboarding flow
enum OnboardingStep: CaseIterable {
    case welcome
    case concept
    case personalInfo
    case relationships
    case teams
    case timeline
    
    var title: String {
        switch self {
        case .welcome: return "Welcome to Keeps"
        case .concept: return "Your Life, Connected"
        case .personalInfo: return "About You"
        case .relationships: return "Your People"
        case .teams: return "Your Teams"
        case .timeline: return "Your Timeline"
        }
    }
    
    var subtitle: String {
        switch self {
        case .welcome: return "Clarity and control of your life"
        case .concept: return "Understanding the connections"
        case .personalInfo: return "Let's get to know you"
        case .relationships: return "The people who matter"
        case .teams: return "Collaborate and achieve"
        case .timeline: return "Track your journey"
        }
    }
}

/// Life goals for user selection during onboarding
enum LifeGoal: String, CaseIterable {
    case career = "Career Growth"
    case relationships = "Stronger Relationships"
    case health = "Health & Wellness"
    case learning = "Continuous Learning"
    case creativity = "Creative Projects"
    case travel = "Travel & Adventure"
    case family = "Family Time"
    case finance = "Financial Goals"
    
    var icon: String {
        switch self {
        case .career: return "briefcase.fill"
        case .relationships: return "heart.fill"
        case .health: return "heart.text.square.fill"
        case .learning: return "book.fill"
        case .creativity: return "paintbrush.fill"
        case .travel: return "airplane"
        case .family: return "house.fill"
        case .finance: return "dollarsign.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .career: return .blue
        case .relationships: return .pink
        case .health: return .green
        case .learning: return .orange
        case .creativity: return .purple
        case .travel: return .cyan
        case .family: return .yellow
        case .finance: return .mint
        }
    }
}

/// Temporary person model for onboarding
struct OnboardingPerson: Identifiable {
    let id = UUID()
    var name: String
    var relationship: String
    var importance: Int // 1-5 scale
}

// MARK: - Onboarding View Components

/// Progress indicator with Apple-style design
struct OnboardingProgressBar: View {
    let progress: Double
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                ForEach(0..<6, id: \.self) { index in
                    Circle()
                        .fill(progress >= Double(index) / 5.0 ? Color.blue : Color.gray.opacity(0.3))
                        .frame(width: 8, height: 8)
                        .scaleEffect(progress >= Double(index) / 5.0 ? 1.2 : 1.0)
                        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: progress)
                }
            }
            
            Text("\(Int(progress * 100))% Complete")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

/// Animated background with floating particles
struct OnboardingBackground: View {
    @State private var animateParticles = false
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color.blue.opacity(0.1),
                    Color.purple.opacity(0.1),
                    Color.pink.opacity(0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // Floating particles
            ForEach(0..<20, id: \.self) { index in
                Circle()
                    .fill(Color.blue.opacity(0.3))
                    .frame(width: CGFloat.random(in: 4...12))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
                    .offset(
                        x: animateParticles ? CGFloat.random(in: -50...50) : 0,
                        y: animateParticles ? CGFloat.random(in: -50...50) : 0
                    )
                    .animation(
                        .easeInOut(duration: Double.random(in: 3...6))
                        .repeatForever(autoreverses: true)
                        .delay(Double.random(in: 0...2)),
                        value: animateParticles
                    )
            }
        }
        .onAppear {
            animateParticles = true
        }
    }
}
