//
//  SearchManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import Foundation
import SwiftUI
import Combine

/// Central search manager coordinating search across all sections
@MainActor
class SearchManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentQuery: SearchQuery = SearchQuery()
    @Published var searchResults: [SearchResult] = []
    @Published var isSearching: Bool = false
    @Published var searchHistory: [SearchHistoryEntry] = []
    @Published var suggestions: [SearchSuggestion] = []
    @Published var analytics: SearchAnalytics = SearchAnalytics()
    @Published var showingFilters: Bool = false
    @Published var showingHistory: Bool = false
    
    // MARK: - Dependencies
    
    private let peopleManager: PeopleManager
    private let teamManager: TeamManager
    private let timelineManager: EvolutionTimelineManager
    private let navigationCoordinator: NavigationCoordinator
    
    // MARK: - Private Properties
    
    private var searchCancellable: AnyCancellable?
    private let searchDebounceTime: TimeInterval = 0.3
    private let maxHistoryEntries = 50
    private let maxSuggestions = 10
    
    // MARK: - Initialization
    
    init(peopleManager: PeopleManager, 
         teamManager: TeamManager, 
         timelineManager: EvolutionTimelineManager,
         navigationCoordinator: NavigationCoordinator) {
        self.peopleManager = peopleManager
        self.teamManager = teamManager
        self.timelineManager = timelineManager
        self.navigationCoordinator = navigationCoordinator
        
        setupSearchDebouncing()
        loadSearchHistory()
        generateInitialSuggestions()
    }
    
    // MARK: - Public Search Methods
    
    /// Perform search with current query
    func search() {
        guard !currentQuery.isEmpty else {
            clearResults()
            return
        }
        
        Task {
            await performSearch(query: currentQuery)
        }
    }
    
    /// Update search query and trigger search
    func updateQuery(_ text: String) {
        currentQuery.text = text
        generateSuggestions(for: text)
    }
    
    /// Update search section filter
    func updateSection(_ section: SearchSection) {
        currentQuery.section = section
        if !currentQuery.isEmpty {
            search()
        }
    }
    
    /// Apply search filters
    func applyFilters(_ filters: SearchFilters) {
        currentQuery.filters = filters
        if !currentQuery.isEmpty {
            search()
        }
    }
    
    /// Clear search results and query
    func clearSearch() {
        currentQuery = SearchQuery()
        clearResults()
        suggestions.removeAll()
    }
    
    /// Navigate to search result
    func navigateToResult(_ result: SearchResult) {
        addToHistory(query: currentQuery.text, section: currentQuery.section, resultCount: searchResults.count)
        updateAnalytics(clickedResult: result)
        
        // Navigate using the navigation coordinator
        switch result.sourceEntity {
        case .person(let id):
            navigationCoordinator.navigateToRelated(.personTeams(id))
        case .team(let id):
            navigationCoordinator.navigateToRelated(.teamMembers(id))
        case .timelineEntry(let id):
            navigationCoordinator.navigateToRelated(.personTimeline(id))
        case .unknown:
            break
        }
    }
    
    // MARK: - Search History Management
    
    /// Add search to history
    func addToHistory(query: String, section: SearchSection, resultCount: Int) {
        let entry = SearchHistoryEntry(query: query, section: section, resultCount: resultCount)
        
        // Remove duplicate if exists
        searchHistory.removeAll { $0.query == query && $0.section == section.rawValue }
        
        // Add to beginning
        searchHistory.insert(entry, at: 0)
        
        // Limit history size
        if searchHistory.count > maxHistoryEntries {
            searchHistory = Array(searchHistory.prefix(maxHistoryEntries))
        }
        
        saveSearchHistory()
    }
    
    /// Execute search from history
    func searchFromHistory(_ entry: SearchHistoryEntry) {
        currentQuery.text = entry.query
        currentQuery.section = SearchSection(rawValue: entry.section) ?? .all
        search()
    }
    
    /// Clear search history
    func clearHistory() {
        searchHistory.removeAll()
        saveSearchHistory()
    }
    
    // MARK: - Suggestions Management
    
    /// Generate suggestions based on input
    func generateSuggestions(for text: String) {
        guard !text.isEmpty else {
            suggestions = getRecentSuggestions()
            return
        }
        
        var newSuggestions: [SearchSuggestion] = []
        
        // Add smart suggestions from entities
        newSuggestions.append(contentsOf: getEntitySuggestions(for: text))
        
        // Add popular search suggestions
        newSuggestions.append(contentsOf: getPopularSuggestions(for: text))
        
        // Add recent search suggestions
        newSuggestions.append(contentsOf: getRecentSuggestions(for: text))
        
        // Limit and sort suggestions
        suggestions = Array(newSuggestions.prefix(maxSuggestions))
    }
    
    // MARK: - Private Search Implementation
    
    private func setupSearchDebouncing() {
        searchCancellable = $currentQuery
            .debounce(for: .seconds(searchDebounceTime), scheduler: RunLoop.main)
            .sink { [weak self] query in
                if !query.isEmpty {
                    self?.search()
                }
            }
    }
    
    private func performSearch(query: SearchQuery) async {
        let startTime = Date()
        isSearching = true
        
        var results: [SearchResult] = []
        
        // Search across sections based on query
        switch query.section {
        case .all:
            results.append(contentsOf: await searchPeople(query: query))
            results.append(contentsOf: await searchTeams(query: query))
            results.append(contentsOf: await searchTimeline(query: query))
        case .people:
            results = await searchPeople(query: query)
        case .teams:
            results = await searchTeams(query: query)
        case .timeline:
            results = await searchTimeline(query: query)
        }
        
        // Apply filters
        results = applyFilters(to: results, filters: query.filters)
        
        // Sort results
        results = sortResults(results, by: query.sortBy)
        
        // Limit results
        results = Array(results.prefix(query.limit))
        
        // Update UI
        await MainActor.run {
            self.searchResults = results
            self.isSearching = false
            
            // Record performance metrics
            let searchTime = Date().timeIntervalSince(startTime)
            recordPerformanceMetrics(searchTime: searchTime, resultCount: results.count, query: query.text)
        }
    }
    
    private func searchPeople(query: SearchQuery) async -> [SearchResult] {
        return peopleManager.people.compactMap { person -> SearchResult? in
            let relevance = calculateRelevance(for: person, query: query.text)
            guard relevance > 0 else { return nil }

            return SearchResult(
                title: person.name,
                subtitle: "\(person.role) at \(person.company)",
                content: "\(person.name) \(person.role) \(person.company) \(person.email) \(person.notes)",
                section: .people,
                entityId: person.id,
                relevanceScore: relevance,
                lastModified: person.lastSeen,
                tags: [person.relationshipType.rawValue, person.availability.rawValue],
                metadata: SearchMetadata(
                    relationshipType: person.relationshipType.rawValue,
                    teamRole: nil,
                    timelineCategory: nil,
                    priority: person.isFavorite ? .high : .medium,
                    hasAttachments: !person.notes.isEmpty,
                    isRecent: Calendar.current.isDateInToday(person.lastSeen),
                    isFavorite: person.isFavorite
                )
            )
        }
    }
    
    private func searchTeams(query: SearchQuery) async -> [SearchResult] {
        return teamManager.teams.compactMap { team -> SearchResult? in
            let relevance = calculateRelevance(for: team, query: query.text)
            guard relevance > 0 else { return nil }
            
            return SearchResult(
                title: team.name,
                subtitle: "\(team.category.rawValue) • \(team.members.count) members",
                content: "\(team.name) \(team.description) \(team.category.rawValue)",
                section: .teams,
                entityId: team.id,
                relevanceScore: relevance,
                lastModified: team.lastActivity,
                tags: [team.category.rawValue, team.projectStatus.rawValue],
                metadata: SearchMetadata(
                    relationshipType: nil,
                    teamRole: nil,
                    timelineCategory: team.category.rawValue,
                    priority: team.isActive ? .high : .medium,
                    hasAttachments: !team.description.isEmpty,
                    isRecent: Calendar.current.isDateInToday(team.lastActivity),
                    isFavorite: false
                )
            )
        }
    }
    
    private func searchTimeline(query: SearchQuery) async -> [SearchResult] {
        return timelineManager.weekEntries.compactMap { entry -> SearchResult? in
            let relevance = calculateRelevance(for: entry, query: query.text)
            guard relevance > 0 else { return nil }

            return SearchResult(
                title: entry.title,
                subtitle: "Week \(entry.weekNumber) • \(entry.emotionalTag.rawValue)",
                content: "\(entry.title) \(entry.insight) \(entry.emotionalTag.rawValue)",
                section: .timeline,
                entityId: entry.id,
                relevanceScore: relevance,
                lastModified: entry.lastModified,
                tags: [entry.emotionalTag.rawValue],
                metadata: SearchMetadata(
                    relationshipType: nil,
                    teamRole: nil,
                    timelineCategory: entry.emotionalTag.rawValue,
                    priority: entry.isCompleted ? .high : .medium,
                    hasAttachments: !entry.accomplishments.isEmpty,
                    isRecent: Calendar.current.isDateInToday(entry.lastModified),
                    isFavorite: entry.isCompleted
                )
            )
        }
    }
    
    // MARK: - Helper Methods
    
    private func calculateRelevance(for person: Person, query: String) -> Double {
        let searchText = query.lowercased()
        var score = 0.0
        
        if person.name.lowercased().contains(searchText) { score += 1.0 }
        if person.role.lowercased().contains(searchText) { score += 0.8 }
        if person.company.lowercased().contains(searchText) { score += 0.6 }
        if person.email.lowercased().contains(searchText) { score += 0.4 }
        if person.notes.lowercased().contains(searchText) { score += 0.3 }
        
        // Boost for favorites and recent contacts
        if person.isFavorite { score *= 1.2 }
        if Calendar.current.isDateInToday(person.lastSeen) { score *= 1.1 }
        
        return score
    }
    
    private func calculateRelevance(for team: Team, query: String) -> Double {
        let searchText = query.lowercased()
        var score = 0.0
        
        if team.name.lowercased().contains(searchText) { score += 1.0 }
        if team.description.lowercased().contains(searchText) { score += 0.6 }
        if team.category.rawValue.lowercased().contains(searchText) { score += 0.4 }
        
        // Boost for active teams and recent activity
        if team.isActive { score *= 1.2 }
        if Calendar.current.isDateInToday(team.lastActivity) { score *= 1.1 }
        
        return score
    }
    
    private func calculateRelevance(for entry: WeekEntry, query: String) -> Double {
        let searchText = query.lowercased()
        var score = 0.0

        if entry.title.lowercased().contains(searchText) { score += 1.0 }
        if entry.insight.lowercased().contains(searchText) { score += 0.8 }
        if entry.emotionalTag.rawValue.lowercased().contains(searchText) { score += 0.4 }

        // Check accomplishments
        for accomplishment in entry.accomplishments {
            if accomplishment.lowercased().contains(searchText) { score += 0.6 }
        }

        // Boost for completed entries and recent entries
        if entry.isCompleted { score *= 1.2 }
        if Calendar.current.isDateInToday(entry.lastModified) { score *= 1.1 }

        return score
    }
    
    private func clearResults() {
        searchResults.removeAll()
        isSearching = false
    }
    
    private func applyFilters(to results: [SearchResult], filters: SearchFilters) -> [SearchResult] {
        return results.filter { result in
            // Date range filter
            if let dateRange = filters.dateRange {
                if result.lastModified < dateRange.start || result.lastModified > dateRange.end {
                    return false
                }
            }

            // Relationship type filter
            if !filters.relationshipTypes.isEmpty {
                if let relationshipType = result.metadata.relationshipType,
                   !filters.relationshipTypes.contains(relationshipType) {
                    return false
                }
            }

            // Priority filter
            if !filters.priorities.isEmpty {
                if !filters.priorities.contains(result.metadata.priority) {
                    return false
                }
            }

            // Favorite filter
            if let isFavorite = filters.isFavorite {
                if result.metadata.isFavorite != isFavorite {
                    return false
                }
            }

            // Recent filter
            if let isRecent = filters.isRecent {
                if result.metadata.isRecent != isRecent {
                    return false
                }
            }

            // Attachments filter
            if let hasAttachments = filters.hasAttachments {
                if result.metadata.hasAttachments != hasAttachments {
                    return false
                }
            }

            return true
        }
    }

    private func sortResults(_ results: [SearchResult], by sortOption: SearchSortOption) -> [SearchResult] {
        switch sortOption {
        case .relevance:
            return results.sorted { $0.relevanceScore > $1.relevanceScore }
        case .dateModified:
            return results.sorted { $0.lastModified > $1.lastModified }
        case .dateCreated:
            return results.sorted { $0.lastModified < $1.lastModified }
        case .alphabetical:
            return results.sorted { $0.title < $1.title }
        case .priority:
            return results.sorted { $0.metadata.priority.rawValue > $1.metadata.priority.rawValue }
        }
    }

    private func getEntitySuggestions(for text: String) -> [SearchSuggestion] {
        var suggestions: [SearchSuggestion] = []
        let searchText = text.lowercased()

        // People suggestions
        for person in peopleManager.people.prefix(3) {
            if person.name.lowercased().contains(searchText) {
                suggestions.append(SearchSuggestion(
                    text: person.name,
                    section: .people,
                    type: .entity,
                    frequency: 1
                ))
            }
        }

        // Team suggestions
        for team in teamManager.teams.prefix(3) {
            if team.name.lowercased().contains(searchText) {
                suggestions.append(SearchSuggestion(
                    text: team.name,
                    section: .teams,
                    type: .entity,
                    frequency: 1
                ))
            }
        }

        return suggestions
    }

    private func getPopularSuggestions(for text: String) -> [SearchSuggestion] {
        return analytics.mostSearchedTerms
            .filter { $0.key.lowercased().contains(text.lowercased()) }
            .sorted { $0.value > $1.value }
            .prefix(3)
            .map { SearchSuggestion(text: $0.key, section: .all, type: .popular, frequency: $0.value) }
    }

    private func getRecentSuggestions(for text: String = "") -> [SearchSuggestion] {
        return searchHistory
            .filter { text.isEmpty || $0.query.lowercased().contains(text.lowercased()) }
            .prefix(3)
            .map { SearchSuggestion(
                text: $0.query,
                section: SearchSection(rawValue: $0.section) ?? .all,
                type: .recent,
                frequency: 1
            )}
    }

    private func recordPerformanceMetrics(searchTime: TimeInterval, resultCount: Int, query: String) {
        // Create metrics for potential future use
        _ = SearchPerformanceMetrics(searchTime: searchTime, resultCount: resultCount, query: query)

        // Update analytics
        analytics.totalSearches += 1
        analytics.averageResultsPerSearch = (analytics.averageResultsPerSearch * Double(analytics.totalSearches - 1) + Double(resultCount)) / Double(analytics.totalSearches)
        analytics.averageSearchTime = (analytics.averageSearchTime * Double(analytics.totalSearches - 1) + searchTime) / Double(analytics.totalSearches)

        // Update most searched terms
        analytics.mostSearchedTerms[query, default: 0] += 1

        // Update section usage
        analytics.sectionUsage[currentQuery.section, default: 0] += 1
    }

    private func updateAnalytics(clickedResult: SearchResult) {
        // Update click-through rate
        let totalClicks = analytics.totalSearches
        analytics.clickThroughRate = (analytics.clickThroughRate * Double(totalClicks - 1) + 1.0) / Double(totalClicks)
    }

    private func loadSearchHistory() {
        // Load from UserDefaults or Core Data
        if let data = UserDefaults.standard.data(forKey: "SearchHistory"),
           let history = try? JSONDecoder().decode([SearchHistoryEntry].self, from: data) {
            searchHistory = history
        }
    }

    private func saveSearchHistory() {
        if let data = try? JSONEncoder().encode(searchHistory) {
            UserDefaults.standard.set(data, forKey: "SearchHistory")
        }
    }

    private func generateInitialSuggestions() {
        // Generate some initial suggestions based on existing data
        var initialSuggestions: [SearchSuggestion] = []

        // Add popular search terms
        let popularTerms = ["family", "work", "friends", "projects", "goals"]
        for term in popularTerms {
            initialSuggestions.append(SearchSuggestion(
                text: term,
                section: .all,
                type: .popular,
                frequency: 1
            ))
        }

        suggestions = initialSuggestions
    }
}
