//
//  PeopleFilterView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI

/// Filter controls for people view with smooth animations
/// Provides relationship type, status, and favorites filtering
struct PeopleFilterView: View {
    @ObservedObject var peopleManager: PeopleManager
    
    var body: some View {
        VStack(spacing: 16) {
            // Quick filter toggles
            HStack(spacing: 12) {
                // Note: Removed "Online Only" filter as online status tracking was removed
                
                FilterToggleButton(
                    title: "Favorites",
                    icon: "star.fill",
                    isSelected: peopleManager.showFavoritesOnly,
                    color: .yellow
                ) {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        peopleManager.showFavoritesOnly.toggle()
                    }
                }
                
                Spacer()
                
                // Clear filters button
                if hasActiveFilters {
                    Button("Clear All") {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            peopleManager.clearFilters()
                        }
                    }
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.blue)
                }
            }
            
            // Relationship type filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(Person.RelationshipType.allCases, id: \.self) { relationshipType in
                        RelationshipFilterChip(
                            relationshipType: relationshipType,
                            isSelected: peopleManager.selectedRelationshipType == relationshipType
                        ) {
                            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                                if peopleManager.selectedRelationshipType == relationshipType {
                                    peopleManager.selectedRelationshipType = nil
                                } else {
                                    peopleManager.selectedRelationshipType = relationshipType
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .padding(.horizontal)
    }
    
    /// Check if any filters are active
    private var hasActiveFilters: Bool {
        !peopleManager.searchText.isEmpty ||
        peopleManager.selectedRelationshipType != nil ||
        peopleManager.showFavoritesOnly
    }
}

/// Toggle button for quick filters
struct FilterToggleButton: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(isSelected ? .white : color)
                
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? color : Color(.systemGray6))
            )
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isSelected)
        }
    }
}

/// Relationship type filter chip
struct RelationshipFilterChip: View {
    let relationshipType: Person.RelationshipType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: relationshipType.icon)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(isSelected ? .white : relationshipType.color)
                
                Text(relationshipType.rawValue)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? relationshipType.color : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(relationshipType.color.opacity(0.3), lineWidth: 1)
                    )
            )
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isSelected)
        }
    }
}

/// Analytics card for people overview
struct PeopleAnalyticsCardView: View {
    let analytics: PeopleAnalytics
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Your Network")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Overview of your contacts")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Quick stats
                HStack(spacing: 16) {
                    StatBadge(
                        value: analytics.totalPeople,
                        label: "Total",
                        color: .blue
                    )
                    
                    StatBadge(
                        value: analytics.onlinePeople,
                        label: "Online",
                        color: .green
                    )
                    
                    StatBadge(
                        value: analytics.favoritesPeople,
                        label: "Favorites",
                        color: .yellow
                    )
                }
            }
            
            // Relationship breakdown
            if !analytics.relationshipBreakdown.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Relationship Types")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                        ForEach(Array(analytics.relationshipBreakdown.keys.sorted(by: { $0.rawValue < $1.rawValue })), id: \.self) { type in
                            if let count = analytics.relationshipBreakdown[type], count > 0 {
                                RelationshipTypeBadge(
                                    type: type,
                                    count: count
                                )
                            }
                        }
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
}

/// Stat badge for analytics
struct StatBadge: View {
    let value: Int
    let label: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 2) {
            Text("\(value)")
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(color)
            
            Text(label)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(.secondary)
        }
        .frame(minWidth: 40)
    }
}

/// Relationship type badge for analytics
struct RelationshipTypeBadge: View {
    let type: Person.RelationshipType
    let count: Int
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: type.icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(type.color)
            
            Text("\(count)")
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(.primary)
            
            Text(type.rawValue)
                .font(.system(size: 8, weight: .medium))
                .foregroundColor(.secondary)
                .lineLimit(1)
                .minimumScaleFactor(0.8)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(type.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(type.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct PeopleFilterView_Previews: PreviewProvider {
    static var previews: some View {
        let peopleManager = PeopleManager()

        return VStack {
            PeopleFilterView(peopleManager: peopleManager)

            Spacer()

            PeopleAnalyticsCardView(analytics: PeopleAnalytics(
                totalPeople: 25,
                onlinePeople: 8,
                favoritesPeople: 5,
                relationshipBreakdown: [
                    .colleague: 12,
                    .friend: 6,
                    .family: 3,
                    .client: 4
                ]
            ))
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
}
