//
//  Models.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Team Models

/// Represents a team member with their profile information and real-time status
class TeamMember: Identifiable, Codable, ObservableObject {
    let id = UUID()
    let name: String
    let role: String
    let avatarImageName: String
    @Published var isOnline: Bool
    let timezone: String
    @Published var lastSeen: Date
    @Published var currentTask: String?
    @Published var availability: AvailabilityStatus

    enum AvailabilityStatus: String, CaseIterable, Codable {
        case available = "Available"
        case busy = "Busy"
        case away = "Away"
        case doNotDisturb = "Do Not Disturb"
        case offline = "Offline"

        var color: Color {
            switch self {
            case .available: return .green
            case .busy: return .orange
            case .away: return .yellow
            case .doNotDisturb: return .red
            case .offline: return .gray
            }
        }
    }

    init(name: String, role: String, avatarImageName: String, isOnline: Bool = false, timezone: String = "UTC", availability: AvailabilityStatus = .offline) {
        self.name = name
        self.role = role
        self.avatarImageName = avatarImageName
        self.isOnline = isOnline
        self.timezone = timezone
        self.lastSeen = Date()
        self.availability = availability
        self.currentTask = nil
    }

    // Required for Codable with @Published properties
    enum CodingKeys: String, CodingKey {
        case id, name, role, avatarImageName, isOnline, timezone, lastSeen, currentTask, availability
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        role = try container.decode(String.self, forKey: .role)
        avatarImageName = try container.decode(String.self, forKey: .avatarImageName)
        isOnline = try container.decode(Bool.self, forKey: .isOnline)
        timezone = try container.decode(String.self, forKey: .timezone)
        lastSeen = try container.decode(Date.self, forKey: .lastSeen)
        currentTask = try container.decodeIfPresent(String.self, forKey: .currentTask)
        availability = try container.decode(AvailabilityStatus.self, forKey: .availability)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(role, forKey: .role)
        try container.encode(avatarImageName, forKey: .avatarImageName)
        try container.encode(isOnline, forKey: .isOnline)
        try container.encode(timezone, forKey: .timezone)
        try container.encode(lastSeen, forKey: .lastSeen)
        try container.encodeIfPresent(currentTask, forKey: .currentTask)
        try container.encode(availability, forKey: .availability)
    }
}

/// Represents a team with all its properties and members - now with real functionality
class Team: Identifiable, Codable, ObservableObject {
    let id = UUID()
    @Published var name: String
    @Published var description: String
    let category: TeamCategory
    @Published var location: String
    @Published var members: [TeamMember]
    @Published var upcomingEvents: [TeamEvent]
    @Published var isActive: Bool
    @Published var lastActivity: Date
    @Published var projectStatus: ProjectStatus
    @Published var notifications: [TeamNotification]

    enum ProjectStatus: String, CaseIterable, Codable {
        case planning = "Planning"
        case inProgress = "In Progress"
        case review = "In Review"
        case completed = "Completed"
        case onHold = "On Hold"

        var color: Color {
            switch self {
            case .planning: return .blue
            case .inProgress: return .green
            case .review: return .orange
            case .completed: return .purple
            case .onHold: return .gray
            }
        }
    }

    /// Computed property for member count
    var memberCount: Int {
        members.count
    }

    /// Get featured members (first 3) for display in cards
    var featuredMembers: [TeamMember] {
        Array(members.prefix(3))
    }

    /// Get online members count
    var onlineCount: Int {
        members.filter { $0.isOnline }.count
    }

    /// Get timezone range
    var timezoneRange: String {
        let timezones = Set(members.map { $0.timezone })
        if timezones.count <= 1 {
            return timezones.first ?? "UTC"
        }
        return "\(timezones.min() ?? "UTC") to \(timezones.max() ?? "UTC")"
    }

    /// Get next sync time
    var nextSyncTime: String {
        if let nextEvent = upcomingEvents.first {
            let formatter = RelativeDateTimeFormatter()
            return formatter.localizedString(for: nextEvent.date, relativeTo: Date())
        }
        return "No upcoming events"
    }

    /// Generate activity heatmap data
    var heatmapData: [Double] {
        // Simulate activity data based on team activity
        let baseActivity = isActive ? 0.6 : 0.2
        return (0..<24).map { hour in
            let variation = Double.random(in: -0.3...0.3)
            return max(0, min(1, baseActivity + variation))
        }
    }

    init(name: String, description: String, category: TeamCategory, location: String, members: [TeamMember] = [], isActive: Bool = true, projectStatus: ProjectStatus = .planning) {
        self.name = name
        self.description = description
        self.category = category
        self.location = location
        self.members = members
        self.upcomingEvents = []
        self.isActive = isActive
        self.lastActivity = Date()
        self.projectStatus = projectStatus
        self.notifications = []
    }

    // MARK: - Team Actions

    func addMember(_ member: TeamMember) {
        members.append(member)
        addNotification("New member \(member.name) joined the team")
    }

    func removeMember(_ member: TeamMember) {
        members.removeAll { $0.id == member.id }
        addNotification("\(member.name) left the team")
    }

    func addEvent(_ event: TeamEvent) {
        upcomingEvents.append(event)
        upcomingEvents.sort { $0.date < $1.date }
        addNotification("New event: \(event.title)")
    }

    func updateProjectStatus(_ status: ProjectStatus) {
        projectStatus = status
        lastActivity = Date()
        addNotification("Project status updated to \(status.rawValue)")
    }

    func addNotification(_ message: String) {
        let notification = TeamNotification(message: message, timestamp: Date())
        notifications.insert(notification, at: 0)
        // Keep only last 10 notifications
        if notifications.count > 10 {
            notifications = Array(notifications.prefix(10))
        }
    }

    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, name, description, category, location, members, upcomingEvents, isActive, lastActivity, projectStatus, notifications
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)
        category = try container.decode(TeamCategory.self, forKey: .category)
        location = try container.decode(String.self, forKey: .location)
        members = try container.decode([TeamMember].self, forKey: .members)
        upcomingEvents = try container.decode([TeamEvent].self, forKey: .upcomingEvents)
        isActive = try container.decode(Bool.self, forKey: .isActive)
        lastActivity = try container.decode(Date.self, forKey: .lastActivity)
        projectStatus = try container.decode(ProjectStatus.self, forKey: .projectStatus)
        notifications = try container.decode([TeamNotification].self, forKey: .notifications)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(description, forKey: .description)
        try container.encode(category, forKey: .category)
        try container.encode(location, forKey: .location)
        try container.encode(members, forKey: .members)
        try container.encode(upcomingEvents, forKey: .upcomingEvents)
        try container.encode(isActive, forKey: .isActive)
        try container.encode(lastActivity, forKey: .lastActivity)
        try container.encode(projectStatus, forKey: .projectStatus)
        try container.encode(notifications, forKey: .notifications)
    }
}

/// Team categories for filtering
enum TeamCategory: String, CaseIterable, Codable {
    case all = "All"
    case creative = "Creative"
    case production = "Production"
    case engineering = "Engineering"
    
    var displayName: String {
        rawValue
    }
    
    var color: Color {
        switch self {
        case .all:
            return .blue
        case .creative:
            return .purple
        case .production:
            return .orange
        case .engineering:
            return .green
        }
    }
}

/// Represents an upcoming team event with interactive functionality
struct TeamEvent: Identifiable, Codable {
    let id = UUID()
    var title: String
    var type: EventType
    var date: Date
    var time: String
    var platform: String?
    var attendeeCount: Int
    var description: String?
    var isRecurring: Bool
    var attendees: [String] // Member IDs

    enum EventType: String, CaseIterable, Codable {
        case meeting = "meeting"
        case review = "review"
        case shoot = "shoot"
        case standup = "standup"
        case deadline = "deadline"
        case workshop = "workshop"

        var color: Color {
            switch self {
            case .meeting: return .blue
            case .review: return .green
            case .shoot: return .orange
            case .standup: return .purple
            case .deadline: return .red
            case .workshop: return .cyan
            }
        }

        var icon: String {
            switch self {
            case .meeting: return "video"
            case .review: return "checkmark.circle"
            case .shoot: return "camera"
            case .standup: return "person.3"
            case .deadline: return "clock.badge.exclamationmark"
            case .workshop: return "graduationcap"
            }
        }
    }

    init(title: String, type: EventType, date: Date, time: String, platform: String? = nil, attendeeCount: Int = 0, description: String? = nil, isRecurring: Bool = false, attendees: [String] = []) {
        self.title = title
        self.type = type
        self.date = date
        self.time = time
        self.platform = platform
        self.attendeeCount = attendeeCount
        self.description = description
        self.isRecurring = isRecurring
        self.attendees = attendees
    }
}

/// Represents a team notification
struct TeamNotification: Identifiable, Codable {
    let id = UUID()
    let message: String
    let timestamp: Date
    var isRead: Bool = false
    let type: NotificationType

    enum NotificationType: String, Codable {
        case memberJoined = "member_joined"
        case memberLeft = "member_left"
        case eventAdded = "event_added"
        case statusChanged = "status_changed"
        case general = "general"

        var icon: String {
            switch self {
            case .memberJoined: return "person.badge.plus"
            case .memberLeft: return "person.badge.minus"
            case .eventAdded: return "calendar.badge.plus"
            case .statusChanged: return "arrow.triangle.2.circlepath"
            case .general: return "bell"
            }
        }

        var color: Color {
            switch self {
            case .memberJoined: return .green
            case .memberLeft: return .orange
            case .eventAdded: return .blue
            case .statusChanged: return .purple
            case .general: return .gray
            }
        }
    }

    init(message: String, timestamp: Date, type: NotificationType = .general) {
        self.message = message
        self.timestamp = timestamp
        self.type = type
    }
}

// MARK: - Sample Data

/// Provides sample data for development and testing with real functionality
struct SampleData {

    /// Create sample team members with diverse roles and realistic status
    static func createSampleMembers() -> [TeamMember] {
        return [
            TeamMember(name: "Maya Bennett", role: "Assistant Producer", avatarImageName: "person.1", isOnline: true, timezone: "PST", availability: .available),
            TeamMember(name: "Jamal Rivers", role: "Camera Operator", avatarImageName: "person.2", isOnline: false, timezone: "EST", availability: .offline),
            TeamMember(name: "Sienna Cho", role: "Director of Photography", avatarImageName: "person.3", isOnline: true, timezone: "PST", availability: .busy),
            TeamMember(name: "Alex Thompson", role: "Lead Developer", avatarImageName: "person.4", isOnline: true, timezone: "GMT", availability: .available),
            TeamMember(name: "Sarah Kim", role: "UI Designer", avatarImageName: "person.5", isOnline: false, timezone: "JST", availability: .away),
            TeamMember(name: "Marcus Johnson", role: "Sound Engineer", avatarImageName: "person.6", isOnline: true, timezone: "EST", availability: .available)
        ]
    }

    /// Create sample events for teams
    static func createSampleEvents() -> [TeamEvent] {
        return [
            TeamEvent(
                title: "Weekly Review",
                type: .review,
                date: Date().addingTimeInterval(86400),
                time: "Wed 14:00",
                platform: "Zoom",
                attendeeCount: 4,
                description: "Weekly team progress review and planning",
                isRecurring: true
            ),
            TeamEvent(
                title: "Client Shoot",
                type: .shoot,
                date: Date().addingTimeInterval(172800),
                time: "Fri 09:00 (Berlin)",
                platform: nil,
                attendeeCount: 8,
                description: "On-location client video shoot",
                isRecurring: false
            ),
            TeamEvent(
                title: "Daily Standup",
                type: .standup,
                date: Date().addingTimeInterval(3600),
                time: "Tomorrow 09:00",
                platform: "Teams",
                attendeeCount: 6,
                description: "Daily team sync and blockers discussion",
                isRecurring: true
            )
        ]
    }

    /// Create sample teams with realistic data and functionality
    static func createSampleTeams() -> [Team] {
        let members = createSampleMembers()
        let events = createSampleEvents()

        let team1 = Team(
            name: "Video Crew",
            description: "Rolling 24/7 (literally)",
            category: .production,
            location: "Barcelona",
            members: Array(members.prefix(3)),
            isActive: true,
            projectStatus: .inProgress
        )
        team1.upcomingEvents = [events[0], events[1]]

        let team2 = Team(
            name: "Post Studio",
            description: "Where the edits never sleep",
            category: .creative,
            location: "Distributed team",
            members: Array(members.suffix(3)),
            isActive: true,
            projectStatus: .review
        )
        team2.upcomingEvents = [events[2]]

        let team3 = Team(
            name: "Sound Design",
            description: "Beats, bass, brilliance",
            category: .creative,
            location: "Los Angeles",
            members: [members[5], members[0], members[3]],
            isActive: false,
            projectStatus: .onHold
        )
        team3.upcomingEvents = events

        return [team1, team2, team3]
    }
}
