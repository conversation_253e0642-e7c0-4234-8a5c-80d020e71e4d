//
//  KeepsApp.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI

@main
struct KeepsApp: App {
    let persistenceController = PersistenceController.shared
    @StateObject private var onboardingManager = OnboardingManager()

    var body: some Scene {
        WindowGroup {
            Group {
                if onboardingManager.isOnboardingComplete {
                    ContentView()
                        .environment(\.managedObjectContext, persistenceController.container.viewContext)
                } else {
                    OnboardingView()
                        .environment(\.managedObjectContext, persistenceController.container.viewContext)
                        .environmentObject(onboardingManager)
                }
            }
            .onAppear {
                onboardingManager.checkOnboardingStatus()
            }
        }
    }
}
