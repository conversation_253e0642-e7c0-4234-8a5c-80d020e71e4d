//
//  TimelineMarkerViews.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Timeline Marker Views
/// Visual markers for people interactions, team achievements, and cross-section milestones

// MARK: - Interaction Marker View
struct InteractionMarkerView: View {
    let interaction: PeopleInteractionMarker
    let sliderHeight: CGFloat
    let totalWeeks: Int
    
    @State private var isAnimating = false
    
    var body: some View {
        HStack {
            Spacer()
            
            Circle()
                .fill(interaction.interactionType.color)
                .frame(width: markerSize, height: markerSize)
                .overlay(
                    Image(systemName: interaction.interactionType.icon)
                        .font(.system(size: iconSize, weight: .medium))
                        .foregroundColor(.white)
                )
                .overlay(
                    Circle()
                        .stroke(interaction.emotionalContext.color, lineWidth: 2)
                        .scaleEffect(isAnimating ? 1.3 : 1.0)
                        .opacity(isAnimating ? 0.0 : 0.8)
                )
                .scaleEffect(interaction.isSignificant ? 1.2 : 1.0)
                .shadow(color: interaction.interactionType.color.opacity(0.3), radius: 4, x: 0, y: 2)
        }
        .offset(y: yOffset)
        .onAppear {
            if interaction.isSignificant {
                withAnimation(
                    Animation.easeInOut(duration: 2.0)
                        .repeatForever(autoreverses: true)
                ) {
                    isAnimating = true
                }
            }
        }
    }
    
    private var markerSize: CGFloat {
        interaction.isSignificant ? 16 : 12
    }
    
    private var iconSize: CGFloat {
        interaction.isSignificant ? 8 : 6
    }
    
    private var yOffset: CGFloat {
        let position = CGFloat(interaction.weekNumber) / CGFloat(totalWeeks)
        return (sliderHeight * position) - (sliderHeight / 2)
    }
}

// MARK: - Team Achievement Marker View
struct TeamAchievementMarkerView: View {
    let achievement: TeamAchievementMilestone
    let sliderHeight: CGFloat
    let totalWeeks: Int
    
    @State private var celebrationAnimation = false
    @State private var showCelebration = false
    
    var body: some View {
        HStack {
            Diamond()
                .fill(achievement.achievementType.color)
                .frame(width: markerSize, height: markerSize)
                .overlay(
                    Image(systemName: achievement.achievementType.icon)
                        .font(.system(size: iconSize, weight: .bold))
                        .foregroundColor(.white)
                )
                .overlay(
                    celebrationOverlay
                )
                .scaleEffect(celebrationAnimation ? 1.3 : 1.0)
                .shadow(color: achievement.achievementType.color.opacity(0.4), radius: 6, x: 0, y: 3)
            
            Spacer()
        }
        .offset(y: yOffset)
        .onAppear {
            startCelebrationAnimation()
        }
        .onTapGesture {
            triggerCelebration()
        }
    }
    
    private var markerSize: CGFloat {
        switch achievement.impact {
        case .personal: return 14
        case .team: return 16
        case .organizational: return 18
        case .industry: return 20
        }
    }
    
    private var iconSize: CGFloat {
        markerSize * 0.5
    }
    
    private var yOffset: CGFloat {
        let position = CGFloat(achievement.weekNumber) / CGFloat(totalWeeks)
        return (sliderHeight * position) - (sliderHeight / 2)
    }
    
    @ViewBuilder
    private var celebrationOverlay: some View {
        if showCelebration {
            switch achievement.celebrationStyle {
            case .confetti:
                ConfettiView()
            case .fireworks:
                FireworksView()
            case .sparkles:
                SparklesView()
            case .glow:
                Circle()
                    .stroke(achievement.achievementType.color, lineWidth: 3)
                    .scaleEffect(celebrationAnimation ? 2.0 : 1.0)
                    .opacity(celebrationAnimation ? 0.0 : 1.0)
            case .pulse:
                Circle()
                    .fill(achievement.achievementType.color.opacity(0.3))
                    .scaleEffect(celebrationAnimation ? 1.5 : 1.0)
                    .opacity(celebrationAnimation ? 0.0 : 0.6)
            case .none:
                EmptyView()
            }
        }
    }
    
    private func startCelebrationAnimation() {
        if achievement.celebrationStyle != .none {
            withAnimation(
                Animation.easeInOut(duration: 1.5)
                    .repeatForever(autoreverses: true)
            ) {
                celebrationAnimation = true
            }
        }
    }
    
    private func triggerCelebration() {
        showCelebration = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + achievement.celebrationStyle.animationDuration) {
            showCelebration = false
        }
    }
}

// MARK: - Cross-Section Milestone Marker View
struct CrossSectionMilestoneMarkerView: View {
    let milestone: CrossSectionMilestone
    let sliderHeight: CGFloat
    let totalWeeks: Int
    
    @State private var glowAnimation = false
    
    var body: some View {
        HStack {
            Star()
                .fill(milestone.category.color)
                .frame(width: markerSize, height: markerSize)
                .overlay(
                    Image(systemName: milestone.category.icon)
                        .font(.system(size: iconSize, weight: .semibold))
                        .foregroundColor(.white)
                )
                .overlay(
                    glowOverlay
                )
                .scaleEffect(milestone.significance.scale)
                .shadow(color: milestone.category.color.opacity(0.5), radius: 8, x: 0, y: 4)
            
            Spacer()
        }
        .offset(y: yOffset)
        .onAppear {
            if milestone.visualStyle == .highlighted || milestone.visualStyle == .celebration {
                withAnimation(
                    Animation.easeInOut(duration: 2.5)
                        .repeatForever(autoreverses: true)
                ) {
                    glowAnimation = true
                }
            }
        }
    }
    
    private var markerSize: CGFloat {
        switch milestone.significance {
        case .minor: return 12
        case .moderate: return 14
        case .major: return 16
        case .life_changing: return 20
        }
    }
    
    private var iconSize: CGFloat {
        markerSize * 0.4
    }
    
    private var yOffset: CGFloat {
        let position = CGFloat(milestone.weekNumber) / CGFloat(totalWeeks)
        return (sliderHeight * position) - (sliderHeight / 2)
    }
    
    @ViewBuilder
    private var glowOverlay: some View {
        if milestone.visualStyle == .highlighted || milestone.visualStyle == .celebration {
            Star()
                .stroke(milestone.category.color, lineWidth: 2)
                .scaleEffect(glowAnimation ? 1.8 : 1.2)
                .opacity(glowAnimation ? 0.0 : 0.8)
        }
    }
}

// MARK: - Custom Shapes
struct Diamond: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2
        
        path.move(to: CGPoint(x: center.x, y: center.y - radius))
        path.addLine(to: CGPoint(x: center.x + radius, y: center.y))
        path.addLine(to: CGPoint(x: center.x, y: center.y + radius))
        path.addLine(to: CGPoint(x: center.x - radius, y: center.y))
        path.closeSubpath()
        
        return path
    }
}

struct Star: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2
        let innerRadius = radius * 0.4
        
        for i in 0..<10 {
            let angle = Double(i) * .pi / 5
            let currentRadius = i % 2 == 0 ? radius : innerRadius
            let x = center.x + CGFloat(cos(angle - .pi / 2)) * currentRadius
            let y = center.y + CGFloat(sin(angle - .pi / 2)) * currentRadius
            
            if i == 0 {
                path.move(to: CGPoint(x: x, y: y))
            } else {
                path.addLine(to: CGPoint(x: x, y: y))
            }
        }
        
        path.closeSubpath()
        return path
    }
}

// MARK: - Celebration Effect Views
struct ConfettiView: View {
    @State private var animate = false
    
    var body: some View {
        ZStack {
            ForEach(0..<20, id: \.self) { _ in
                Rectangle()
                    .fill(Color.random)
                    .frame(width: 4, height: 4)
                    .offset(
                        x: animate ? CGFloat.random(in: -50...50) : 0,
                        y: animate ? CGFloat.random(in: -50...50) : 0
                    )
                    .rotationEffect(.degrees(animate ? Double.random(in: 0...360) : 0))
                    .opacity(animate ? 0 : 1)
            }
        }
        .onAppear {
            withAnimation(.easeOut(duration: 2.0)) {
                animate = true
            }
        }
    }
}

struct FireworksView: View {
    @State private var animate = false
    
    var body: some View {
        ZStack {
            ForEach(0..<8, id: \.self) { i in
                Circle()
                    .fill(Color.random)
                    .frame(width: 3, height: 3)
                    .offset(
                        x: animate ? CGFloat(cos(Double(i) * .pi / 4)) * 40 : 0,
                        y: animate ? CGFloat(sin(Double(i) * .pi / 4)) * 40 : 0
                    )
                    .opacity(animate ? 0 : 1)
            }
        }
        .onAppear {
            withAnimation(.easeOut(duration: 1.5)) {
                animate = true
            }
        }
    }
}

struct SparklesView: View {
    @State private var animate = false
    
    var body: some View {
        ZStack {
            ForEach(0..<12, id: \.self) { _ in
                Image(systemName: "sparkle")
                    .font(.system(size: 8))
                    .foregroundColor(.yellow)
                    .offset(
                        x: animate ? CGFloat.random(in: -30...30) : 0,
                        y: animate ? CGFloat.random(in: -30...30) : 0
                    )
                    .scaleEffect(animate ? 0 : 1)
                    .opacity(animate ? 0 : 1)
            }
        }
        .onAppear {
            withAnimation(.easeOut(duration: 2.0)) {
                animate = true
            }
        }
    }
}

// MARK: - Color Extension
extension Color {
    static var random: Color {
        return Color(
            red: Double.random(in: 0...1),
            green: Double.random(in: 0...1),
            blue: Double.random(in: 0...1)
        )
    }
}
