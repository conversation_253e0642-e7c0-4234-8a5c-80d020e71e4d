//
//  DataMigrationManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import Foundation
import CoreData
import SwiftUI

/// Handles migration from UserDefaults-based timeline data to Core Data
/// Ensures seamless transition without data loss
class DataMigrationManager: ObservableObject {
    
    // MARK: - Properties
    
    private let context: NSManagedObjectContext
    private let userDefaults = UserDefaults.standard
    
    // UserDefaults keys from EvolutionTimelineManager
    private let dataKey = "EvolutionTimelineData"
    private let milestonesKey = "TimelineMilestones"
    private let birthDateKey = "UserBirthDate"
    
    @Published var migrationProgress: Double = 0.0
    @Published var migrationStatus: MigrationStatus = .notStarted
    @Published var migrationMessage: String = ""
    
    enum MigrationStatus {
        case notStarted
        case inProgress
        case completed
        case failed(Error)
    }
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // MARK: - Migration Methods
    
    /// Check if migration is needed
    func needsMigration() -> Bool {
        // Check if UserDefaults has timeline data
        let hasUserDefaultsData = userDefaults.data(forKey: dataKey) != nil
        
        // Check if Core Data is empty
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        let coreDataCount = (try? context.count(for: request)) ?? 0
        
        return hasUserDefaultsData && coreDataCount == 0
    }
    
    /// Perform complete migration from UserDefaults to Core Data
    func performMigration() async {
        await MainActor.run {
            migrationStatus = .inProgress
            migrationProgress = 0.0
            migrationMessage = "Starting migration..."
        }
        
        do {
            // Step 1: Migrate user settings and birth date
            await updateProgress(0.1, "Migrating user settings...")
            try await migrateUserSettings()
            
            // Step 2: Migrate timeline entries
            await updateProgress(0.3, "Migrating timeline entries...")
            try await migrateTimelineEntries()
            
            // Step 3: Migrate milestones
            await updateProgress(0.7, "Migrating milestones...")
            try await migrateMilestones()
            
            // Step 4: Save context
            await updateProgress(0.9, "Saving data...")
            try context.save()
            
            // Step 5: Clean up UserDefaults (optional - keep for backup)
            await updateProgress(1.0, "Migration completed successfully!")
            
            await MainActor.run {
                migrationStatus = .completed
            }
            
        } catch {
            await MainActor.run {
                migrationStatus = .failed(error)
                migrationMessage = "Migration failed: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Private Migration Methods
    
    private func migrateUserSettings() async throws {
        // Create or update user settings entity
        let request: NSFetchRequest<UserSettingsEntity> = UserSettingsEntity.fetchRequest()
        let existingSettings = try context.fetch(request).first
        
        let userSettings = existingSettings ?? UserSettingsEntity(context: context)
        userSettings.id = userSettings.id ?? UUID()
        userSettings.createdDate = userSettings.createdDate ?? Date()
        userSettings.lastModified = Date()
        
        // Migrate birth date
        if let birthDateData = userDefaults.data(forKey: birthDateKey),
           let birthDate = try? JSONDecoder().decode(Date.self, from: birthDateData) {
            userSettings.birthDate = birthDate
            userSettings.needsBirthDateSetup = false
        } else {
            userSettings.needsBirthDateSetup = true
        }
        
        // Calculate current week number
        if !userSettings.needsBirthDateSetup, let birthDate = userSettings.birthDate {
            let calendar = Calendar.current
            let daysSinceBirth = calendar.dateComponents([.day], from: birthDate, to: Date()).day ?? 0
            let weekNumber = max(1, daysSinceBirth / 7)
            userSettings.currentWeekNumber = Int32(weekNumber)
        }
    }
    
    private func migrateTimelineEntries() async throws {
        guard let weekData = userDefaults.data(forKey: dataKey),
              let weekEntries = try? JSONDecoder().decode([WeekEntry].self, from: weekData) else {
            return // No data to migrate
        }
        
        let totalEntries = weekEntries.count
        
        for (index, weekEntry) in weekEntries.enumerated() {
            // Create Core Data entity
            let timelineEntity = TimelineEntryEntity(context: context)
            
            // Basic information
            timelineEntity.id = weekEntry.id
            timelineEntity.weekNumber = Int32(weekEntry.weekNumber)
            timelineEntity.startDate = weekEntry.startDate
            timelineEntity.endDate = weekEntry.endDate
            
            // Content
            timelineEntity.title = weekEntry.title.isEmpty ? nil : weekEntry.title
            timelineEntity.insight = weekEntry.insight.isEmpty ? nil : weekEntry.insight
            timelineEntity.emotionalTag = weekEntry.emotionalTag.rawValue
            
            // Convert accomplishments array to JSON string
            if !weekEntry.accomplishments.isEmpty {
                let accomplishmentsData = try JSONEncoder().encode(weekEntry.accomplishments)
                timelineEntity.accomplishments = String(data: accomplishmentsData, encoding: .utf8)
            }
            
            // Media
            timelineEntity.audioRecordingURL = weekEntry.audioRecordingURL
            timelineEntity.sketchData = weekEntry.sketchData
            
            // Status
            timelineEntity.isCompleted = weekEntry.isCompleted
            timelineEntity.isCurrentWeek = weekEntry.isCurrentWeek
            timelineEntity.createdDate = weekEntry.createdDate
            timelineEntity.lastModified = weekEntry.lastModified
            
            // Update progress
            let progress = 0.3 + (Double(index) / Double(totalEntries)) * 0.4
            await updateProgress(progress, "Migrating timeline entry \(index + 1) of \(totalEntries)")
        }
    }
    
    private func migrateMilestones() async throws {
        // For now, skip milestone migration since TimelineMilestoneEntity is not in the current model
        // This can be added later when we implement the full milestone system
        return
    }
    
    private func updateProgress(_ progress: Double, _ message: String) async {
        await MainActor.run {
            migrationProgress = progress
            migrationMessage = message
        }
    }
    
    // MARK: - Cleanup Methods
    
    /// Remove UserDefaults data after successful migration (optional)
    func cleanupUserDefaults() {
        userDefaults.removeObject(forKey: dataKey)
        userDefaults.removeObject(forKey: milestonesKey)
        userDefaults.removeObject(forKey: birthDateKey)
    }
    
    /// Restore UserDefaults data from Core Data (rollback)
    func restoreToUserDefaults() async throws {
        // This method can be used to rollback if needed
        // Implementation would reverse the migration process
    }
}

// MARK: - Migration UI Component

/// Simple migration progress view
struct MigrationProgressView: View {
    @ObservedObject var migrationManager: DataMigrationManager
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Migrating Data")
                .font(.title2)
                .fontWeight(.semibold)
            
            ProgressView(value: migrationManager.migrationProgress)
                .progressViewStyle(LinearProgressViewStyle())
            
            Text(migrationManager.migrationMessage)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            if case .failed(let error) = migrationManager.migrationStatus {
                Text("Error: \(error.localizedDescription)")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding()
    }
}
