//
//  AutomationSupportComponents.swift
//  Keeps
//
//  Supporting components for the automation system
//  Includes notification manager, workflow trigger, and smart reminders
//

import SwiftUI
import UserNotifications
import Combine

// MARK: - Notification Manager

/// Manages intelligent notifications and alerts
class NotificationManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var notificationPermissionGranted = false
    @Published var pendingNotifications: [PendingNotification] = []
    
    // MARK: - Initialization
    
    init() {
        requestNotificationPermission()
    }
    
    // MARK: - Public Methods
    
    /// Show an immediate notification
    func showNotification(title: String, body: String, category: String = "general") async {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.categoryIdentifier = category
        content.sound = .default
        
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: nil // Immediate
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            print("📱 Notification sent: \(title)")
        } catch {
            print("❌ Failed to send notification: \(error)")
        }
    }
    
    /// Schedule a notification for later
    func scheduleNotification(title: String, body: String, date: Date, category: String = "reminder") async {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.categoryIdentifier = category
        content.sound = .default
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: trigger
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            
            await MainActor.run {
                pendingNotifications.append(PendingNotification(
                    title: title,
                    body: body,
                    scheduledDate: date,
                    category: category
                ))
            }
            
            print("⏰ Notification scheduled: \(title) for \(date)")
        } catch {
            print("❌ Failed to schedule notification: \(error)")
        }
    }
    
    /// Cancel all pending notifications
    func cancelAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        pendingNotifications.removeAll()
    }
    
    // MARK: - Private Methods
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                self.notificationPermissionGranted = granted
            }
            
            if let error = error {
                print("❌ Notification permission error: \(error)")
            }
        }
    }
}

// MARK: - Workflow Trigger

/// Triggers workflows automatically based on automation rules
class WorkflowTrigger: ObservableObject {
    
    // MARK: - Private Properties
    
    private var workflowManager: CrossSectionWorkflowManager?
    
    // MARK: - Public Methods
    
    /// Connect with workflow manager
    func connect(with workflowManager: CrossSectionWorkflowManager) {
        self.workflowManager = workflowManager
    }
    
    /// Start a workflow automatically
    func startWorkflow(_ workflowType: WorkflowType, context: WorkflowContext? = nil) async {
        await MainActor.run {
            workflowManager?.startWorkflow(workflowType, context: context)
            print("🚀 Auto-started workflow: \(workflowType.rawValue)")
        }
    }
    
    /// Suggest a workflow without starting it
    func suggestWorkflow(_ workflowType: WorkflowType, reason: String) async {
        // Send suggestion to smart suggestions engine
        NotificationCenter.default.post(
            name: .workflowSuggested,
            object: nil,
            userInfo: [
                "workflowType": workflowType,
                "reason": reason
            ]
        )
        
        print("💡 Suggested workflow: \(workflowType.rawValue) - \(reason)")
    }
}

// MARK: - Smart Reminder System

/// Intelligent reminder system that learns from user behavior
class SmartReminderSystem: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var activeReminders: [SmartReminder] = []
    @Published var reminderHistory: [CompletedReminder] = []
    
    // MARK: - Private Properties
    
    private let notificationManager = NotificationManager()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init() {
        setupReminderObservation()
    }
    
    // MARK: - Public Methods
    
    /// Create a smart reminder
    func createReminder(title: String, date: Date, category: String = "general", personId: UUID? = nil) async {
        let reminder = SmartReminder(
            title: title,
            scheduledDate: date,
            category: category,
            personId: personId
        )
        
        await MainActor.run {
            activeReminders.append(reminder)
        }
        
        // Schedule notification
        await notificationManager.scheduleNotification(
            title: title,
            body: "Reminder: \(title)",
            date: date,
            category: "reminder"
        )
        
        print("⏰ Created smart reminder: \(title)")
    }
    
    /// Complete a reminder
    func completeReminder(_ reminderId: UUID) {
        if let index = activeReminders.firstIndex(where: { $0.id == reminderId }) {
            let reminder = activeReminders[index]
            
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                activeReminders.remove(at: index)
                
                reminderHistory.append(CompletedReminder(
                    originalReminder: reminder,
                    completedAt: Date()
                ))
            }
            
            print("✅ Completed reminder: \(reminder.title)")
        }
    }
    
    /// Snooze a reminder
    func snoozeReminder(_ reminderId: UUID, for duration: TimeInterval) async {
        if let index = activeReminders.firstIndex(where: { $0.id == reminderId }) {
            let newDate = Date().addingTimeInterval(duration)
            activeReminders[index].scheduledDate = newDate
            
            // Reschedule notification
            await notificationManager.scheduleNotification(
                title: activeReminders[index].title,
                body: "Reminder: \(activeReminders[index].title)",
                date: newDate,
                category: "reminder"
            )
            
            print("😴 Snoozed reminder: \(activeReminders[index].title)")
        }
    }
    
    /// Generate smart reminder suggestions based on patterns
    func generateReminderSuggestions(patterns: UserBehaviorPatterns) -> [ReminderSuggestion] {
        var suggestions: [ReminderSuggestion] = []
        
        // Suggest reminders for frequent collaborators
        for collaborator in patterns.frequentCollaborators.prefix(3) {
            let daysSinceLastInteraction = Date().timeIntervalSince(collaborator.lastInteraction) / 86400
            
            if daysSinceLastInteraction > 7 {
                suggestions.append(ReminderSuggestion(
                    title: "Connect with frequent collaborator",
                    description: "It's been \(Int(daysSinceLastInteraction)) days since your last interaction",
                    suggestedDate: Date().addingTimeInterval(3600), // 1 hour from now
                    personId: collaborator.personId,
                    confidence: min(0.9, collaborator.relationshipStrength)
                ))
            }
        }
        
        // Suggest goal review reminders
        if patterns.goalCompletionRate > 0.5 {
            suggestions.append(ReminderSuggestion(
                title: "Weekly goal review",
                description: "Review your progress and plan ahead",
                suggestedDate: getNextWeeklyReviewDate(),
                confidence: patterns.goalCompletionRate
            ))
        }
        
        // Suggest team check-in reminders
        for teamPattern in patterns.teamFormationPatterns {
            if teamPattern.activityLevel > 0.7 {
                suggestions.append(ReminderSuggestion(
                    title: "Team check-in",
                    description: "Connect with your active team",
                    suggestedDate: Date().addingTimeInterval(86400), // Tomorrow
                    teamId: teamPattern.teamId,
                    confidence: teamPattern.activityLevel
                ))
            }
        }
        
        return suggestions.sorted { $0.confidence > $1.confidence }
    }
    
    // MARK: - Private Methods
    
    private func setupReminderObservation() {
        // Check for due reminders every minute
        Timer.publish(every: 60, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.checkDueReminders()
            }
            .store(in: &cancellables)
    }
    
    private func checkDueReminders() {
        let now = Date()
        let dueReminders = activeReminders.filter { reminder in
            reminder.scheduledDate <= now && !reminder.isCompleted
        }
        
        for reminder in dueReminders {
            // Mark as due and potentially show in-app notification
            if let index = activeReminders.firstIndex(where: { $0.id == reminder.id }) {
                activeReminders[index].isDue = true
            }
        }
    }
    
    private func getNextWeeklyReviewDate() -> Date {
        let calendar = Calendar.current
        let now = Date()
        
        // Find next Friday at 5 PM
        var components = calendar.dateComponents([.year, .month, .day], from: now)
        components.weekday = 6 // Friday
        components.hour = 17
        components.minute = 0
        
        if let nextFriday = calendar.nextDate(after: now, matching: components, matchingPolicy: .nextTime) {
            return nextFriday
        }
        
        return now.addingTimeInterval(604800) // 1 week from now
    }
}

// MARK: - Pattern Recognizer

/// Recognizes patterns in user behavior for intelligent suggestions
class PatternRecognizer: ObservableObject {
    
    // MARK: - Public Methods
    
    /// Analyze connection opportunities between people
    func analyzeConnectionOpportunities(userPatterns: UserBehaviorPatterns) async -> [ConnectionSuggestion] {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                var suggestions: [ConnectionSuggestion] = []
                
                // Analyze collaboration patterns
                let collaborators = userPatterns.frequentCollaborators
                
                for i in 0..<collaborators.count {
                    for j in (i+1)..<collaborators.count {
                        let person1 = collaborators[i]
                        let person2 = collaborators[j]
                        
                        // Suggest connection if both are frequent collaborators
                        if person1.relationshipStrength > 0.6 && person2.relationshipStrength > 0.6 {
                            suggestions.append(ConnectionSuggestion(
                                fromPersonId: person1.personId,
                                toPersonId: person2.personId,
                                teamId: nil,
                                connectionType: .collaboration,
                                reason: "Both are frequent collaborators with similar interests",
                                confidence: (person1.relationshipStrength + person2.relationshipStrength) / 2.0
                            ))
                        }
                    }
                }
                
                // Suggest team memberships
                for collaborator in collaborators.prefix(5) {
                    if collaborator.relationshipStrength > 0.7 {
                        // Find suitable teams (placeholder logic)
                        for teamPattern in userPatterns.teamFormationPatterns {
                            if teamPattern.activityLevel > 0.6 {
                                suggestions.append(ConnectionSuggestion(
                                    fromPersonId: collaborator.personId,
                                    toPersonId: nil,
                                    teamId: teamPattern.teamId,
                                    connectionType: .teamMembership,
                                    reason: "Strong collaborator would fit well in active team",
                                    confidence: min(0.9, collaborator.relationshipStrength * teamPattern.activityLevel)
                                ))
                            }
                        }
                    }
                }
                
                continuation.resume(returning: suggestions.sorted { $0.confidence > $1.confidence })
            }
        }
    }
}

// MARK: - Supporting Models

/// Represents a pending notification
struct PendingNotification: Identifiable {
    let id = UUID()
    let title: String
    let body: String
    let scheduledDate: Date
    let category: String
}

/// Represents a smart reminder
struct SmartReminder: Identifiable {
    let id = UUID()
    let title: String
    var scheduledDate: Date
    let category: String
    let personId: UUID?
    let teamId: UUID?
    var isCompleted = false
    var isDue = false
    let createdAt = Date()
    
    init(title: String, scheduledDate: Date, category: String = "general", personId: UUID? = nil, teamId: UUID? = nil) {
        self.title = title
        self.scheduledDate = scheduledDate
        self.category = category
        self.personId = personId
        self.teamId = teamId
    }
}

/// Represents a completed reminder for analytics
struct CompletedReminder: Identifiable {
    let id = UUID()
    let originalReminder: SmartReminder
    let completedAt: Date
    var wasOnTime: Bool {
        completedAt <= originalReminder.scheduledDate.addingTimeInterval(3600) // Within 1 hour
    }
}

/// Represents a suggestion for creating a reminder
struct ReminderSuggestion: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let suggestedDate: Date
    let personId: UUID?
    let teamId: UUID?
    let confidence: Double
    
    init(title: String, description: String, suggestedDate: Date, personId: UUID? = nil, teamId: UUID? = nil, confidence: Double) {
        self.title = title
        self.description = description
        self.suggestedDate = suggestedDate
        self.personId = personId
        self.teamId = teamId
        self.confidence = confidence
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let workflowSuggested = Notification.Name("workflowSuggested")
}
