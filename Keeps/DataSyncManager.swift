//
//  DataSyncManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import Foundation
import SwiftUI
import Combine
import CoreData
import Network

/// Central coordinator for all data synchronization operations
/// Manages real-time sync, conflict resolution, offline support, and backup/restore
class DataSyncManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var syncStatus = SyncStatus()
    @Published var pendingOperations: [SyncOperation] = []
    @Published var activeConflicts: [DataConflict] = []
    @Published var syncAnalytics = SyncAnalytics()
    @Published var configuration = SyncConfiguration.default
    @Published var backupSnapshots: [BackupSnapshot] = []
    @Published var isPerformingBackup = false
    @Published var isRestoring = false
    
    // MARK: - Private Properties
    
    private let context: NSManagedObjectContext
    private let conflictResolver: ConflictResolver
    private let offlineQueue: OfflineSyncQueue
    private let dataValidator: DataValidator
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")
    
    private var cancellables = Set<AnyCancellable>()
    private var syncTimer: Timer?
    private var backupTimer: Timer?
    
    // UserDefaults keys
    private let syncOperationsKey = "SyncOperations"
    private let syncAnalyticsKey = "SyncAnalytics"
    private let syncConfigurationKey = "SyncConfiguration"
    private let backupSnapshotsKey = "BackupSnapshots"
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext = PersistenceController.shared.container.viewContext) {
        self.context = context
        self.conflictResolver = ConflictResolver()
        self.offlineQueue = OfflineSyncQueue()
        self.dataValidator = DataValidator(context: context)
        
        loadConfiguration()
        loadPendingOperations()
        loadAnalytics()
        loadBackupSnapshots()
        setupNetworkMonitoring()
        setupAutoSync()
        setupAutoBackup()
    }
    
    deinit {
        syncTimer?.invalidate()
        backupTimer?.invalidate()
        networkMonitor.cancel()
    }
    
    // MARK: - Public Sync Methods
    
    /// Queue a sync operation
    func queueOperation(_ operation: SyncOperation) {
        pendingOperations.append(operation)
        savePendingOperations()
        
        if syncStatus.isOnline && configuration.isAutoSyncEnabled {
            Task {
                await performSync()
            }
        }
    }
    
    /// Perform immediate sync of all pending operations
    @MainActor
    func performSync() async {
        guard !pendingOperations.isEmpty else { return }
        
        syncStatus.currentOperation = "Syncing \(pendingOperations.count) operations..."
        syncStatus.syncProgress = 0.0
        
        let startTime = Date()
        var completedOperations = 0
        var failedOperations = 0
        
        // Process operations in batches
        let batches = pendingOperations.chunked(into: configuration.batchSize)
        
        for (batchIndex, batch) in batches.enumerated() {
            await processBatch(batch)
            
            completedOperations += batch.count
            syncStatus.syncProgress = Double(completedOperations) / Double(pendingOperations.count)
            
            // Update UI
            syncStatus.currentOperation = "Processed batch \(batchIndex + 1) of \(batches.count)"
        }
        
        // Update analytics
        let syncTime = Date().timeIntervalSince(startTime)
        updateAnalytics(syncTime: syncTime, successful: completedOperations - failedOperations, failed: failedOperations)
        
        // Clean up completed operations
        pendingOperations.removeAll { $0.status == .completed }
        savePendingOperations()
        
        syncStatus.lastSyncTime = Date()
        syncStatus.currentOperation = nil
        syncStatus.syncProgress = 1.0
        
        // Reset progress after a delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.syncStatus.syncProgress = 0.0
        }
    }
    
    /// Force sync all data
    @MainActor
    func forceSyncAll() async {
        // Create operations for all entities
        await createSyncOperationsForAllEntities()
        await performSync()
    }
    
    /// Resolve a conflict with the specified strategy
    func resolveConflict(_ conflict: DataConflict, strategy: DataConflict.ResolutionStrategy) async {
        let resolvedConflict = await conflictResolver.resolve(conflict, strategy: strategy)
        
        if let index = activeConflicts.firstIndex(where: { $0.id == conflict.id }) {
            activeConflicts[index] = resolvedConflict
            
            if resolvedConflict.isResolved {
                activeConflicts.remove(at: index)
                syncAnalytics.conflictsResolved += 1
                saveAnalytics()
            }
        }
    }
    
    // MARK: - Backup & Restore
    
    /// Create a backup snapshot
    @MainActor
    func createBackup(isAutomatic: Bool = true) async {
        guard !isPerformingBackup else { return }
        
        isPerformingBackup = true
        
        do {
            let snapshot = try await createBackupSnapshot(isAutomatic: isAutomatic)
            backupSnapshots.insert(snapshot, at: 0)
            
            // Maintain max backup count
            if backupSnapshots.count > configuration.maxBackupCount {
                backupSnapshots = Array(backupSnapshots.prefix(configuration.maxBackupCount))
            }
            
            saveBackupSnapshots()
        } catch {
            print("❌ Backup failed: \(error)")
        }
        
        isPerformingBackup = false
    }
    
    /// Restore from a backup snapshot
    @MainActor
    func restoreFromBackup(_ snapshot: BackupSnapshot) async {
        guard !isRestoring else { return }
        
        isRestoring = true
        
        do {
            try await performRestore(from: snapshot)
            
            // Create a pre-restore backup
            await createBackup(isAutomatic: false)
            
            // Force sync after restore
            await forceSyncAll()
            
        } catch {
            print("❌ Restore failed: \(error)")
        }
        
        isRestoring = false
    }
    
    // MARK: - Configuration
    
    /// Update sync configuration
    func updateConfiguration(_ newConfig: SyncConfiguration) {
        configuration = newConfig
        saveConfiguration()
        
        // Restart timers with new intervals
        setupAutoSync()
        setupAutoBackup()
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.syncStatus.isOnline = path.status == .satisfied
                
                // Trigger sync when coming back online
                if path.status == .satisfied && self?.configuration.isAutoSyncEnabled == true {
                    Task {
                        await self?.performSync()
                    }
                }
            }
        }
        networkMonitor.start(queue: networkQueue)
    }
    
    private func setupAutoSync() {
        syncTimer?.invalidate()
        
        guard configuration.isAutoSyncEnabled && configuration.syncInterval > 0 else { return }
        
        syncTimer = Timer.scheduledTimer(withTimeInterval: configuration.syncInterval, repeats: true) { [weak self] _ in
            Task {
                await self?.performSync()
            }
        }
    }
    
    private func setupAutoBackup() {
        backupTimer?.invalidate()
        
        guard let interval = configuration.backupFrequency.interval, interval > 0 else { return }
        
        backupTimer = Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { [weak self] _ in
            Task {
                await self?.createBackup()
            }
        }
    }
    
    private func processBatch(_ batch: [SyncOperation]) async {
        for operation in batch {
            await processOperation(operation)
        }
    }
    
    private func processOperation(_ operation: SyncOperation) async {
        var updatedOperation = operation
        updatedOperation.status = .inProgress
        updatedOperation.lastAttempt = Date()
        
        do {
            // Validate data before processing
            let isValid = await dataValidator.validate(operation)
            guard isValid else {
                updatedOperation.status = .failed
                updatedOperation.errorMessage = "Data validation failed"
                return
            }
            
            // Process the operation based on type
            switch operation.operationType {
            case .create:
                try await processCreateOperation(operation)
            case .update:
                try await processUpdateOperation(operation)
            case .delete:
                try await processDeleteOperation(operation)
            case .merge:
                try await processMergeOperation(operation)
            case .restore:
                try await processRestoreOperation(operation)
            }
            
            updatedOperation.status = .completed
            
        } catch {
            updatedOperation.status = .failed
            updatedOperation.errorMessage = error.localizedDescription
            updatedOperation.retryCount += 1
            
            // Queue for retry if under max attempts
            if updatedOperation.retryCount < configuration.maxRetryAttempts {
                updatedOperation.status = .pending
            }
        }
        
        // Update operation in array
        if let index = pendingOperations.firstIndex(where: { $0.id == operation.id }) {
            pendingOperations[index] = updatedOperation
        }
    }
    
    // MARK: - Operation Processing
    
    private func processCreateOperation(_ operation: SyncOperation) async throws {
        // Implementation for create operations
        print("Processing create operation for \(operation.entityType)")
    }
    
    private func processUpdateOperation(_ operation: SyncOperation) async throws {
        // Implementation for update operations
        print("Processing update operation for \(operation.entityType)")
    }
    
    private func processDeleteOperation(_ operation: SyncOperation) async throws {
        // Implementation for delete operations
        print("Processing delete operation for \(operation.entityType)")
    }
    
    private func processMergeOperation(_ operation: SyncOperation) async throws {
        // Implementation for merge operations
        print("Processing merge operation for \(operation.entityType)")
    }
    
    private func processRestoreOperation(_ operation: SyncOperation) async throws {
        // Implementation for restore operations
        print("Processing restore operation for \(operation.entityType)")
    }

    // MARK: - Data Persistence

    private func loadConfiguration() {
        if let data = UserDefaults.standard.data(forKey: syncConfigurationKey),
           let config = try? JSONDecoder().decode(SyncConfiguration.self, from: data) {
            configuration = config
        }
    }

    private func saveConfiguration() {
        if let data = try? JSONEncoder().encode(configuration) {
            UserDefaults.standard.set(data, forKey: syncConfigurationKey)
        }
    }

    private func loadPendingOperations() {
        if let data = UserDefaults.standard.data(forKey: syncOperationsKey),
           let operations = try? JSONDecoder().decode([SyncOperation].self, from: data) {
            pendingOperations = operations
        }
    }

    private func savePendingOperations() {
        if let data = try? JSONEncoder().encode(pendingOperations) {
            UserDefaults.standard.set(data, forKey: syncOperationsKey)
        }
    }

    private func loadAnalytics() {
        if let data = UserDefaults.standard.data(forKey: syncAnalyticsKey),
           let analytics = try? JSONDecoder().decode(SyncAnalytics.self, from: data) {
            syncAnalytics = analytics
        }
    }

    private func saveAnalytics() {
        if let data = try? JSONEncoder().encode(syncAnalytics) {
            UserDefaults.standard.set(data, forKey: syncAnalyticsKey)
        }
    }

    private func loadBackupSnapshots() {
        if let data = UserDefaults.standard.data(forKey: backupSnapshotsKey),
           let snapshots = try? JSONDecoder().decode([BackupSnapshot].self, from: data) {
            backupSnapshots = snapshots
        }
    }

    private func saveBackupSnapshots() {
        if let data = try? JSONEncoder().encode(backupSnapshots) {
            UserDefaults.standard.set(data, forKey: backupSnapshotsKey)
        }
    }

    private func updateAnalytics(syncTime: TimeInterval, successful: Int, failed: Int) {
        syncAnalytics.totalSyncs += 1
        syncAnalytics.successfulSyncs += successful
        syncAnalytics.failedSyncs += failed

        // Update average sync time
        let totalTime = syncAnalytics.averageSyncTime * Double(syncAnalytics.totalSyncs - 1) + syncTime
        syncAnalytics.averageSyncTime = totalTime / Double(syncAnalytics.totalSyncs)

        saveAnalytics()
    }

    private func createSyncOperationsForAllEntities() async {
        // Create operations for all people
        let peopleRequest: NSFetchRequest<PersonEntity> = PersonEntity.fetchRequest()
        if let people = try? context.fetch(peopleRequest) {
            for person in people {
                if let data = try? JSONEncoder().encode(person.id) {
                    let operation = SyncOperation(
                        entityType: .person,
                        entityId: person.id ?? UUID(),
                        operationType: .update,
                        data: data
                    )
                    pendingOperations.append(operation)
                }
            }
        }

        // Create operations for all teams
        let teamsRequest: NSFetchRequest<TeamEntity> = TeamEntity.fetchRequest()
        if let teams = try? context.fetch(teamsRequest) {
            for team in teams {
                if let data = try? JSONEncoder().encode(team.id) {
                    let operation = SyncOperation(
                        entityType: .team,
                        entityId: team.id ?? UUID(),
                        operationType: .update,
                        data: data
                    )
                    pendingOperations.append(operation)
                }
            }
        }

        // Create operations for all timeline entries
        let timelineRequest: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        if let entries = try? context.fetch(timelineRequest) {
            for entry in entries {
                if let data = try? JSONEncoder().encode(entry.id) {
                    let operation = SyncOperation(
                        entityType: .weekEntry,
                        entityId: entry.id ?? UUID(),
                        operationType: .update,
                        data: data
                    )
                    pendingOperations.append(operation)
                }
            }
        }

        savePendingOperations()
    }

    private func createBackupSnapshot(isAutomatic: Bool) async throws -> BackupSnapshot {
        // Calculate data size and entity counts
        var entityCounts: [String: Int] = [:]
        var totalSize: Int64 = 0

        // Count people
        let peopleRequest: NSFetchRequest<PersonEntity> = PersonEntity.fetchRequest()
        let peopleCount = try context.count(for: peopleRequest)
        entityCounts["people"] = peopleCount

        // Count teams
        let teamsRequest: NSFetchRequest<TeamEntity> = TeamEntity.fetchRequest()
        let teamsCount = try context.count(for: teamsRequest)
        entityCounts["teams"] = teamsCount

        // Count timeline entries
        let timelineRequest: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        let timelineCount = try context.count(for: timelineRequest)
        entityCounts["timeline"] = timelineCount

        // Estimate data size (simplified calculation)
        totalSize = Int64((peopleCount * 1024) + (teamsCount * 512) + (timelineCount * 256))

        // Generate checksum (simplified)
        let checksumData = "\(peopleCount)-\(teamsCount)-\(timelineCount)-\(Date().timeIntervalSince1970)"
        let checksum = String(checksumData.hashValue)

        return BackupSnapshot(
            version: "1.0",
            dataSize: totalSize,
            entityCounts: entityCounts,
            checksum: checksum,
            isAutomatic: isAutomatic
        )
    }

    private func performRestore(from snapshot: BackupSnapshot) async throws {
        // Implementation for restore functionality
        print("Restoring from backup: \(snapshot.formattedDate)")

        // This would involve:
        // 1. Validating the backup
        // 2. Clearing current data
        // 3. Restoring from backup
        // 4. Updating all managers

        throw NSError(domain: "RestoreError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Restore functionality not yet implemented"])
    }
}

// MARK: - Array Extension

extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}
