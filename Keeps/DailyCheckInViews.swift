//
//  DailyCheckInViews.swift
//  Keeps
//
//  Daily check-in UI components with Apple-style animations
//

import SwiftUI

// MARK: - Daily Check-in Main View

/// Main daily check-in view with step navigation
struct DailyCheckInView: View {
    @ObservedObject var checkInManager: DailyCheckInManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color.blue.opacity(0.1),
                    Color.purple.opacity(0.1),
                    Color.pink.opacity(0.05)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // Header with progress
                checkInHeader
                
                // Current step content
                currentStepView
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                
                // Navigation buttons
                navigationButtons
            }
        }
        .preferredColorScheme(.light)
    }
    
    private var checkInHeader: some View {
        VStack(spacing: 16) {
            HStack {
                Button("Skip") {
                    dismiss()
                }
                .foregroundColor(.secondary)
                
                Spacer()
                
                Text("Daily Check-in")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Close") {
                    dismiss()
                }
                .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            // Progress bar
            ProgressView(value: checkInManager.checkInProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .scaleEffect(y: 2)
                .padding(.horizontal, 40)
        }
    }
    
    @ViewBuilder
    private var currentStepView: some View {
        switch checkInManager.currentCheckInStep {
        case .welcome:
            WelcomeCheckInStep()
        case .mood:
            MoodCheckInStep(checkInManager: checkInManager)
        case .goals:
            GoalsCheckInStep(checkInManager: checkInManager)
        case .people:
            PeopleCheckInStep(checkInManager: checkInManager)
        case .teams:
            TeamsCheckInStep(checkInManager: checkInManager)
        case .reflection:
            ReflectionCheckInStep(checkInManager: checkInManager)
        }
    }
    
    private var navigationButtons: some View {
        HStack(spacing: 20) {
            Spacer()
            
            Button(action: checkInManager.nextCheckInStep) {
                HStack {
                    Text(checkInManager.currentCheckInStep == .reflection ? "Complete" : "Continue")
                    if checkInManager.currentCheckInStep != .reflection {
                        Image(systemName: "chevron.right")
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, 30)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(25)
            }
            .disabled(checkInManager.isAnimating)
        }
        .padding(.horizontal, 40)
        .padding(.bottom, 40)
    }
}

// MARK: - Welcome Step

struct WelcomeCheckInStep: View {
    @State private var animateIcon = false
    @State private var animateText = false
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            VStack(spacing: 20) {
                Image(systemName: "sun.max.fill")
                    .font(.system(size: 80))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.orange, .yellow],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .scaleEffect(animateIcon ? 1.0 : 0.8)
                    .rotationEffect(.degrees(animateIcon ? 0 : -10))
                    .animation(.spring(response: 0.8, dampingFraction: 0.6), value: animateIcon)
                
                VStack(spacing: 16) {
                    Text("Good Morning!")
                        .font(.title)
                        .fontWeight(.semibold)
                        .opacity(animateText ? 1 : 0)
                        .offset(y: animateText ? 0 : 20)
                        .animation(.easeOut(duration: 0.6).delay(0.3), value: animateText)
                    
                    Text("Let's plan your day together and make it meaningful")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .opacity(animateText ? 1 : 0)
                        .offset(y: animateText ? 0 : 20)
                        .animation(.easeOut(duration: 0.6).delay(0.5), value: animateText)
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, 40)
        .onAppear {
            animateIcon = true
            animateText = true
        }
    }
}

// MARK: - Mood Step

struct MoodCheckInStep: View {
    @ObservedObject var checkInManager: DailyCheckInManager
    @State private var animateContent = false
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            VStack(spacing: 16) {
                Text("How are you feeling?")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("Check in with yourself")
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 40)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(DailyMood.allCases, id: \.self) { mood in
                    MoodCard(
                        mood: mood,
                        isSelected: checkInManager.todaysMood == mood
                    ) {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            checkInManager.todaysMood = mood
                        }
                    }
                }
            }
            .padding(.horizontal, 40)
            .opacity(animateContent ? 1 : 0)
            .offset(y: animateContent ? 0 : 30)
            .animation(.easeOut(duration: 0.8).delay(0.3), value: animateContent)
            
            Spacer()
        }
        .onAppear {
            animateContent = true
        }
    }
}

// MARK: - Goals Step

struct GoalsCheckInStep: View {
    @ObservedObject var checkInManager: DailyCheckInManager
    @State private var newGoalTitle = ""
    @State private var selectedCategory: GoalCategory = .general
    @State private var animateContent = false
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            VStack(spacing: 16) {
                Text("Today's Focus")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("What matters most today?")
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 40)
            
            // Add goal form
            VStack(spacing: 16) {
                HStack(spacing: 12) {
                    TextField("Add a goal for today", text: $newGoalTitle)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Picker("Category", selection: $selectedCategory) {
                        ForEach(GoalCategory.allCases, id: \.self) { category in
                            Label(category.rawValue, systemImage: category.icon)
                                .tag(category)
                        }
                    }
                    .pickerStyle(.menu)
                    
                    Button(action: addGoal) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                    .disabled(newGoalTitle.isEmpty)
                }
                .padding(.horizontal, 40)
            }
            
            // Goals list
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(checkInManager.todaysGoals) { goal in
                        GoalRow(goal: goal) {
                            removeGoal(goal)
                        }
                    }
                }
                .padding(.horizontal, 40)
            }
            .frame(maxHeight: 200)
            .opacity(animateContent ? 1 : 0)
            .offset(y: animateContent ? 0 : 30)
            .animation(.easeOut(duration: 0.8).delay(0.3), value: animateContent)
            
            if checkInManager.todaysGoals.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "target")
                        .font(.system(size: 50))
                        .foregroundColor(.blue.opacity(0.6))
                    
                    Text("Add 2-3 goals for today")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 40)
            }
            
            Spacer()
        }
        .onAppear {
            animateContent = true
        }
    }
    
    private func addGoal() {
        guard !newGoalTitle.isEmpty else { return }
        
        let goal = DailyGoal(
            title: newGoalTitle,
            category: selectedCategory,
            estimatedTime: 60 // Default 1 hour
        )
        
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            checkInManager.todaysGoals.append(goal)
        }
        
        newGoalTitle = ""
    }
    
    private func removeGoal(_ goal: DailyGoal) {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            checkInManager.todaysGoals.removeAll { $0.id == goal.id }
        }
    }
}

// MARK: - Supporting Views

struct MoodCard: View {
    let mood: DailyMood
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Text(mood.emoji)
                    .font(.system(size: 40))
                
                Text(mood.rawValue)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .frame(height: 100)
            .frame(maxWidth: .infinity)
            .background(isSelected ? mood.color : Color.gray.opacity(0.1))
            .cornerRadius(16)
            .scaleEffect(isSelected ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct GoalRow: View {
    let goal: DailyGoal
    let onRemove: () -> Void

    var body: some View {
        HStack {
            Circle()
                .fill(goalCategoryColor(goal.category).opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: goal.category.icon)
                        .font(.headline)
                        .foregroundColor(goalCategoryColor(goal.category))
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(goal.title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(goal.category.rawValue)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Button(action: onRemove) {
                Image(systemName: "minus.circle.fill")
                    .foregroundColor(.red)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }

    private func goalCategoryColor(_ category: GoalCategory) -> Color {
        switch category {
        case .general: return .blue
        case .performance: return .green
        case .learning: return .orange
        case .collaboration: return .pink
        case .innovation: return .purple
        }
    }
}

// MARK: - Placeholder Steps

struct PeopleCheckInStep: View {
    @ObservedObject var checkInManager: DailyCheckInManager
    
    var body: some View {
        VStack {
            Spacer()
            Text("People Connections")
                .font(.title)
                .fontWeight(.semibold)
            Text("Who will you connect with today?")
                .font(.body)
                .foregroundColor(.secondary)
                .padding(.top, 8)
            Spacer()
        }
    }
}

struct TeamsCheckInStep: View {
    @ObservedObject var checkInManager: DailyCheckInManager
    
    var body: some View {
        VStack {
            Spacer()
            Text("Team Updates")
                .font(.title)
                .fontWeight(.semibold)
            Text("Any team progress to share?")
                .font(.body)
                .foregroundColor(.secondary)
                .padding(.top, 8)
            Spacer()
        }
    }
}

struct ReflectionCheckInStep: View {
    @ObservedObject var checkInManager: DailyCheckInManager
    
    var body: some View {
        VStack {
            Spacer()
            Text("Reflection")
                .font(.title)
                .fontWeight(.semibold)
            Text("Gratitude and insights")
                .font(.body)
                .foregroundColor(.secondary)
                .padding(.top, 8)
            Spacer()
        }
    }
}
