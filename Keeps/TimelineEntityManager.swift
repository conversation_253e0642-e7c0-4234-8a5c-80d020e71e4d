//
//  TimelineEntityManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import Foundation
import CoreData
import SwiftUI

/// Manages the bridge between WeekEntry class and TimelineEntryEntity Core Data model
/// Provides enhanced timeline capabilities including accomplishment tracking, milestone integration, and life reflection
class TimelineEntityManager: ObservableObject {
    
    // MARK: - Properties
    
    private let context: NSManagedObjectContext
    
    @Published var accomplishments: [UUID: [TimelineAccomplishment]] = [:]
    @Published var reflections: [UUID: [TimelineReflection]] = [:]
    @Published var streakData: [UUID: StreakData] = [:]
    @Published var lifePhases: [LifePhase] = []
    @Published var milestoneConnections: [UUID: [MilestoneConnection]] = [:]
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext) {
        self.context = context
        loadLifePhases()
        fixExistingTimelineEntries()
    }

    /// Fix existing timeline entries that might have nil weeklyScore values
    private func fixExistingTimelineEntries() {
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()

        do {
            let entities = try context.fetch(request)
            var needsSave = false

            for entity in entities {
                // Fix weeklyScore if it's somehow nil (shouldn't happen with default value, but just in case)
                if entity.weeklyScore == 0.0 && entity.accomplishments?.isEmpty == false {
                    // Calculate a basic score based on accomplishments
                    let accomplishmentCount = decodeFromJSON([String].self, from: entity.accomplishments ?? "[]")?.count ?? 0
                    entity.weeklyScore = Double(accomplishmentCount) * 0.5 // Basic scoring
                    needsSave = true
                }

                // Ensure consistency rating is set
                if entity.consistencyRating == nil {
                    entity.consistencyRating = ConsistencyRating.developing.rawValue
                    needsSave = true
                }
            }

            if needsSave {
                try context.save()
                print("✅ Fixed existing timeline entries with missing weeklyScore values")
            }
        } catch {
            print("❌ Failed to fix existing timeline entries: \(error)")
        }
    }
    
    // MARK: - Core Data Bridge Methods
    
    /// Convert WeekEntry to TimelineEntryEntity
    func createTimelineEntity(from weekEntry: WeekEntry) -> TimelineEntryEntity {
        let entity = TimelineEntryEntity(context: context)
        updateTimelineEntity(entity, from: weekEntry)
        return entity
    }
    
    /// Update TimelineEntryEntity from WeekEntry
    func updateTimelineEntity(_ entity: TimelineEntryEntity, from weekEntry: WeekEntry) {
        // Basic Information
        entity.id = weekEntry.id
        entity.weekNumber = Int32(weekEntry.weekNumber)
        entity.startDate = weekEntry.startDate
        entity.endDate = weekEntry.endDate
        entity.title = weekEntry.title.isEmpty ? nil : weekEntry.title
        entity.insight = weekEntry.insight.isEmpty ? nil : weekEntry.insight
        entity.emotionalTag = weekEntry.emotionalTag.rawValue

        // Accomplishments and Content
        entity.accomplishments = encodeToJSON(weekEntry.accomplishments)
        entity.audioRecordingURL = weekEntry.audioRecordingURL
        entity.sketchData = weekEntry.sketchData

        // Status & Metadata
        entity.isCompleted = weekEntry.isCompleted
        entity.isCurrentWeek = weekEntry.isCurrentWeek
        entity.createdDate = entity.createdDate ?? weekEntry.createdDate
        entity.lastModified = weekEntry.lastModified

        // Enhanced Timeline Data
        entity.accomplishmentsData = encodeToJSON(accomplishments[weekEntry.id] ?? [])
        entity.reflectionsData = encodeToJSON(reflections[weekEntry.id] ?? [])
        entity.streakData = encodeToJSON(streakData[weekEntry.id])
        entity.milestoneConnectionsData = encodeToJSON(milestoneConnections[weekEntry.id] ?? [])

        // Analytics & Insights - Calculate weeklyScore from streak data
        if let weekStreakData = streakData[weekEntry.id] {
            entity.weeklyScore = weekStreakData.weeklyScore
            entity.consistencyRating = weekStreakData.consistencyRating.rawValue
        } else {
            // Ensure weeklyScore is never nil - use default value
            entity.weeklyScore = 0.0
            entity.consistencyRating = ConsistencyRating.developing.rawValue
        }

        // Linked People and Teams
        entity.linkedPeopleIDs = encodeToJSON(weekEntry.linkedPeople)
        entity.linkedTeamIDs = encodeToJSON(weekEntry.linkedProjects.compactMap { UUID(uuidString: $0) })
    }
    
    /// Convert TimelineEntryEntity to WeekEntry
    func createWeekEntry(from entity: TimelineEntryEntity) -> WeekEntry {
        let weekEntry = WeekEntry(
            weekNumber: Int(entity.weekNumber),
            startDate: entity.startDate ?? Date()
        )
        
        // Basic Information
        weekEntry.title = entity.title ?? ""
        weekEntry.insight = entity.insight ?? ""
        weekEntry.emotionalTag = EmotionalTag(rawValue: entity.emotionalTag ?? "neutral") ?? .neutral
        weekEntry.accomplishments = decodeFromJSON([String].self, from: entity.accomplishments ?? "[]") ?? []
        weekEntry.isCompleted = entity.isCompleted
        weekEntry.createdDate = entity.createdDate ?? Date()
        weekEntry.lastModified = entity.lastModified ?? Date()
        
        // Audio and Sketch Data
        weekEntry.audioRecordingURL = entity.audioRecordingURL
        weekEntry.sketchData = entity.sketchData
        
        // Enhanced Timeline Data
        if let accomplishmentsData = entity.accomplishmentsData {
            accomplishments[weekEntry.id] = decodeFromJSON([TimelineAccomplishment].self, from: accomplishmentsData) ?? []
        }
        
        if let reflectionsData = entity.reflectionsData {
            reflections[weekEntry.id] = decodeFromJSON([TimelineReflection].self, from: reflectionsData) ?? []
        }
        
        if let streakDataJSON = entity.streakData {
            streakData[weekEntry.id] = decodeFromJSON(StreakData.self, from: streakDataJSON)
        }
        
        if let milestonesData = entity.milestoneConnectionsData {
            milestoneConnections[weekEntry.id] = decodeFromJSON([MilestoneConnection].self, from: milestonesData) ?? []
        }
        
        // Linked People and Teams
        weekEntry.linkedPeople = decodeFromJSON([UUID].self, from: entity.linkedPeopleIDs ?? "[]") ?? []
        let linkedTeamUUIDs = decodeFromJSON([UUID].self, from: entity.linkedTeamIDs ?? "[]") ?? []
        weekEntry.linkedProjects = linkedTeamUUIDs.map { $0.uuidString }
        
        return weekEntry
    }
    
    // MARK: - Enhanced Timeline Capabilities
    
    /// Add accomplishment to a week entry
    func addAccomplishment(to weekEntryId: UUID, title: String, description: String? = nil, category: AccomplishmentCategory, impact: ImpactLevel = .medium, linkedPeople: [UUID] = [], linkedTeams: [UUID] = []) {
        let accomplishment = TimelineAccomplishment(
            weekEntryId: weekEntryId,
            title: title,
            description: description,
            category: category,
            impact: impact,
            linkedPeople: linkedPeople,
            linkedTeams: linkedTeams
        )
        
        if accomplishments[weekEntryId] == nil {
            accomplishments[weekEntryId] = []
        }
        accomplishments[weekEntryId]?.append(accomplishment)
        
        // Update streak data
        updateStreakData(for: weekEntryId, with: accomplishment)
    }
    
    /// Add reflection to a week entry
    func addReflection(to weekEntryId: UUID, content: String, type: ReflectionType, mood: EmotionalTag? = nil, learnings: [String] = [], gratitude: [String] = []) {
        let reflection = TimelineReflection(
            weekEntryId: weekEntryId,
            content: content,
            type: type,
            mood: mood,
            learnings: learnings,
            gratitude: gratitude
        )
        
        if reflections[weekEntryId] == nil {
            reflections[weekEntryId] = []
        }
        reflections[weekEntryId]?.append(reflection)
    }
    
    /// Update streak data for accomplishments
    private func updateStreakData(for weekEntryId: UUID, with accomplishment: TimelineAccomplishment) {
        if streakData[weekEntryId] == nil {
            streakData[weekEntryId] = StreakData(weekEntryId: weekEntryId)
        }
        
        streakData[weekEntryId]?.addAccomplishment(accomplishment)
    }
    
    /// Calculate life phase for a given week
    func getLifePhase(for weekNumber: Int, birthDate: Date) -> LifePhase? {
        let age = Double(weekNumber) / 52.0 // Approximate age in years
        return lifePhases.first { phase in
            age >= phase.startAge && age < phase.endAge
        }
    }
    
    /// Load default life phases
    private func loadLifePhases() {
        lifePhases = [
            LifePhase(name: "Early Childhood", startAge: 0, endAge: 5, color: .blue, description: "Foundation years"),
            LifePhase(name: "Childhood", startAge: 5, endAge: 12, color: .green, description: "Learning and growth"),
            LifePhase(name: "Adolescence", startAge: 12, endAge: 18, color: .orange, description: "Identity formation"),
            LifePhase(name: "Young Adult", startAge: 18, endAge: 25, color: .purple, description: "Independence and exploration"),
            LifePhase(name: "Adult", startAge: 25, endAge: 40, color: .red, description: "Career and relationships"),
            LifePhase(name: "Middle Age", startAge: 40, endAge: 60, color: .brown, description: "Mastery and contribution"),
            LifePhase(name: "Later Life", startAge: 60, endAge: 80, color: .gray, description: "Wisdom and reflection"),
            LifePhase(name: "Elder Years", startAge: 80, endAge: 120, color: .indigo, description: "Legacy and peace")
        ]
    }
    
    // MARK: - Milestone Integration
    
    /// Connect timeline entry to team milestone
    func connectToTeamMilestone(weekEntryId: UUID, teamId: UUID, milestoneId: UUID, connectionType: MilestoneConnectionType) {
        let connection = MilestoneConnection(
            weekEntryId: weekEntryId,
            teamId: teamId,
            milestoneId: milestoneId,
            connectionType: connectionType
        )
        
        if milestoneConnections[weekEntryId] == nil {
            milestoneConnections[weekEntryId] = []
        }
        milestoneConnections[weekEntryId]?.append(connection)
    }
    
    /// Get all timeline entries connected to a specific team
    func getTimelineEntriesForTeam(_ teamId: UUID) -> [TimelineEntryEntity] {
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        
        do {
            let allEntries = try context.fetch(request)
            return allEntries.filter { entry in
                guard let linkedTeamIDsJSON = entry.linkedTeamIDs else { return false }
                let linkedTeamIDs = decodeFromJSON([UUID].self, from: linkedTeamIDsJSON) ?? []
                return linkedTeamIDs.contains(teamId)
            }
        } catch {
            print("Error fetching timeline entries for team: \(error)")
            return []
        }
    }
    
    /// Get all timeline entries connected to a specific person
    func getTimelineEntriesForPerson(_ personId: UUID) -> [TimelineEntryEntity] {
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        
        do {
            let allEntries = try context.fetch(request)
            return allEntries.filter { entry in
                guard let linkedPeopleIDsJSON = entry.linkedPeopleIDs else { return false }
                let linkedPeopleIDs = decodeFromJSON([UUID].self, from: linkedPeopleIDsJSON) ?? []
                return linkedPeopleIDs.contains(personId)
            }
        } catch {
            print("Error fetching timeline entries for person: \(error)")
            return []
        }
    }
    
    // MARK: - Analytics and Insights
    
    /// Calculate accomplishment streak for a category
    func calculateAccomplishmentStreak(category: AccomplishmentCategory, endingWeek: Int) -> Int {
        // Implementation for calculating streaks
        var streak = 0
        var currentWeek = endingWeek
        
        while currentWeek > 0 {
            let weekEntryId = getWeekEntryId(for: currentWeek)
            let weekAccomplishments = accomplishments[weekEntryId] ?? []
            
            if weekAccomplishments.contains(where: { $0.category == category }) {
                streak += 1
                currentWeek -= 1
            } else {
                break
            }
        }
        
        return streak
    }
    
    /// Get accomplishment distribution by category
    func getAccomplishmentDistribution() -> [AccomplishmentCategory: Int] {
        var distribution: [AccomplishmentCategory: Int] = [:]
        
        for accomplishmentList in accomplishments.values {
            for accomplishment in accomplishmentList {
                distribution[accomplishment.category, default: 0] += 1
            }
        }
        
        return distribution
    }
    
    // MARK: - Helper Methods
    
    private func getWeekEntryId(for weekNumber: Int) -> UUID {
        // This would need to be implemented to find the UUID for a given week number
        // For now, returning a placeholder
        return UUID()
    }
    
    private func encodeToJSON<T: Codable>(_ object: T) -> String? {
        guard let data = try? JSONEncoder().encode(object) else { return nil }
        return String(data: data, encoding: .utf8)
    }
    
    private func decodeFromJSON<T: Codable>(_ type: T.Type, from jsonString: String) -> T? {
        guard let data = jsonString.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }
}
