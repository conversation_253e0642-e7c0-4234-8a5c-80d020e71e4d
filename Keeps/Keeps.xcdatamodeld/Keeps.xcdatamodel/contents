<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="false" userDefinedModelVersionIdentifier="">

    <!-- Person Entity - Enhanced for relationship tracking -->
    <entity name="PersonEntity" representedClassName="PersonEntity" syncable="YES" codeGenerationType="class">
        <!-- Basic Information -->
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="role" optional="YES" attributeType="String"/>
        <attribute name="company" optional="YES" attributeType="String"/>
        <attribute name="email" optional="YES" attributeType="String"/>
        <attribute name="phone" optional="YES" attributeType="String"/>
        <attribute name="avatarImageName" optional="YES" attributeType="String"/>
        <attribute name="location" optional="YES" attributeType="String"/>
        <attribute name="timezone" optional="YES" attributeType="String"/>
        <attribute name="birthday" optional="YES" attributeType="Date" usesScalarValueType="NO"/>

        <!-- Status & Availability -->
        <attribute name="isOnline" attributeType="Boolean" defaultValue="NO" usesScalarValueType="YES"/>
        <attribute name="lastSeen" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="availability" attributeType="String" defaultValue="offline"/>
        <attribute name="isFavorite" attributeType="Boolean" defaultValue="NO" usesScalarValueType="YES"/>

        <!-- Relationship Intelligence -->
        <attribute name="relationshipType" attributeType="String" defaultValue="other"/>
        <attribute name="interactionFrequency" attributeType="String" defaultValue="rarely"/>
        <attribute name="relationshipValue" optional="YES" attributeType="String"/>
        <attribute name="relationshipStrength" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="lastInteractionDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="totalInteractions" attributeType="Integer 32" defaultValue="0" usesScalarValueType="YES"/>

        <!-- Notes & Content -->
        <attribute name="notes" optional="YES" attributeType="String"/>
        <attribute name="notesData" optional="YES" attributeType="Binary"/>
        <attribute name="tags" optional="YES" attributeType="String"/>
        <attribute name="socialLinks" optional="YES" attributeType="String"/>
    </entity>

    <!-- Team Entity - Enhanced for project management -->
    <entity name="TeamEntity" representedClassName="TeamEntity" syncable="YES" codeGenerationType="class">
        <!-- Basic Information -->
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="teamDescription" attributeType="String"/>
        <attribute name="category" attributeType="String"/>
        <attribute name="location" optional="YES" attributeType="String"/>

        <!-- Project Management -->
        <attribute name="projectStatus" attributeType="String" defaultValue="planning"/>
        <attribute name="isActive" attributeType="Boolean" defaultValue="YES" usesScalarValueType="YES"/>
        <attribute name="lastActivity" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="createdDate" attributeType="Date" usesScalarValueType="NO"/>

        <!-- Goals & Planning -->
        <attribute name="goals" optional="YES" attributeType="String"/>
        <attribute name="objectives" optional="YES" attributeType="String"/>
        <attribute name="resources" optional="YES" attributeType="String"/>
        <attribute name="timeline" optional="YES" attributeType="String"/>
        <attribute name="progress" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
    </entity>

    <!-- Timeline Entry Entity - Migrated from UserDefaults -->
    <entity name="TimelineEntryEntity" representedClassName="TimelineEntryEntity" syncable="YES" codeGenerationType="class">
        <!-- Basic Information -->
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="weekNumber" attributeType="Integer 32" usesScalarValueType="YES"/>
        <attribute name="startDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="endDate" attributeType="Date" usesScalarValueType="NO"/>

        <!-- Content -->
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="insight" optional="YES" attributeType="String"/>
        <attribute name="accomplishments" optional="YES" attributeType="String"/>
        <attribute name="emotionalTag" attributeType="String" defaultValue="neutral"/>

        <!-- Media & Attachments -->
        <attribute name="audioRecordingURL" optional="YES" attributeType="URI"/>
        <attribute name="sketchData" optional="YES" attributeType="Binary"/>

        <!-- Status & Metadata -->
        <attribute name="isCompleted" attributeType="Boolean" defaultValue="NO" usesScalarValueType="YES"/>
        <attribute name="isCurrentWeek" attributeType="Boolean" defaultValue="NO" usesScalarValueType="YES"/>
        <attribute name="createdDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastModified" attributeType="Date" usesScalarValueType="NO"/>

        <!-- Enhanced Timeline Data (stored as JSON) -->
        <attribute name="accomplishmentsData" optional="YES" attributeType="String"/>
        <attribute name="reflectionsData" optional="YES" attributeType="String"/>
        <attribute name="streakData" optional="YES" attributeType="String"/>
        <attribute name="milestoneConnectionsData" optional="YES" attributeType="String"/>
        <attribute name="lifePhaseName" optional="YES" attributeType="String"/>

        <!-- Analytics & Insights -->
        <attribute name="weeklyScore" attributeType="Double" defaultValue="0.0" usesScalarValueType="YES"/>
        <attribute name="consistencyRating" optional="YES" attributeType="String"/>
        <attribute name="impactLevel" optional="YES" attributeType="String"/>

        <!-- Celebration & Motivation -->
        <attribute name="celebrationData" optional="YES" attributeType="String"/>
        <attribute name="motivationalNotes" optional="YES" attributeType="String"/>

        <!-- Linked People and Teams (stored as JSON strings) -->
        <attribute name="linkedPeopleIDs" optional="YES" attributeType="String"/>
        <attribute name="linkedTeamIDs" optional="YES" attributeType="String"/>
    </entity>

    <!-- User Settings Entity - Store user preferences and birth date -->
    <entity name="UserSettingsEntity" representedClassName="UserSettingsEntity" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="birthDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="currentWeekNumber" attributeType="Integer 32" usesScalarValueType="YES"/>
        <attribute name="needsBirthDateSetup" attributeType="Boolean" defaultValue="YES" usesScalarValueType="YES"/>
        <attribute name="lastTimelineSync" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="preferences" optional="YES" attributeType="String"/>
        <attribute name="createdDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="lastModified" attributeType="Date" usesScalarValueType="NO"/>
    </entity>

    <elements>
        <element name="PersonEntity" positionX="-200" positionY="100" width="200" height="400"/>
        <element name="TeamEntity" positionX="100" positionY="100" width="200" height="300"/>
        <element name="TimelineEntryEntity" positionX="-50" positionY="-200" width="200" height="450"/>
        <element name="UserSettingsEntity" positionX="-300" positionY="-100" width="200" height="160"/>
    </elements>
</model>