//
//  PeopleListView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Revolutionary emotional contacts interface that creates deep connections
/// Features innovative card-based design with emotional intelligence and cross-section navigation
struct PeopleListView: View {
    @StateObject private var peopleManager = PeopleManager()
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @State private var showingPersonDetail = false
    @State private var showingAddPerson = false
    @State private var showingSearch = false
    @State private var showingFilters = false
    @State private var showingDemoMode = false
    @State private var selectedViewMode: ViewMode = .cards
    @State private var celebrationTriggered = false
    @State private var showingKeyboardHelp = false

    enum ViewMode: String, CaseIterable {
        case cards = "Cards"
        case list = "List"
        case grid = "Grid"

        var icon: String {
            switch self {
            case .cards: return "rectangle.stack.fill"
            case .list: return "list.bullet"
            case .grid: return "square.grid.2x2"
            }
        }
    }

    var body: some View {
        NavigationView {
            EmotionalContactsInterface(
                peopleManager: people<PERSON>ana<PERSON>,
                navigationCoordinator: navigationCoordinator,
                selectedViewMode: $selectedViewMode,
                showingSearch: $showingSearch,
                showingFilters: $showingFilters,
                showingAddPerson: $showingAddPerson,
                showingDemoMode: $showingDemoMode,
                celebrationTriggered: celebrationTriggered
            )
        }
        .fullScreenCover(isPresented: $showingPersonDetail) {
            if let person = peopleManager.selectedPerson {
                PersonDetailView(person: person, peopleManager: peopleManager)
            }
        }
        .sheet(isPresented: $showingAddPerson) {
            AddPersonView(peopleManager: peopleManager)
        }
        .sheet(isPresented: $showingDemoMode) {
            EmotionalDemoMode()
        }
        .onChange(of: peopleManager.selectedPerson) { _, person in
            showingPersonDetail = person != nil
        }
        .keyboardShortcuts(
            peopleManager: peopleManager,
            showingSearch: $showingSearch,
            showingFilters: $showingFilters,
            showingAddPerson: $showingAddPerson,
            selectedViewMode: $selectedViewMode
        )
        .overlay {
            if showingKeyboardHelp {
                KeyboardShortcutsHelp(isPresented: $showingKeyboardHelp)
                    .transition(.opacity.combined(with: .scale))
            }
        }
    }
}

/// Revolutionary emotional contacts interface
struct EmotionalContactsInterface: View {
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @Binding var selectedViewMode: PeopleListView.ViewMode
    @Binding var showingSearch: Bool
    @Binding var showingFilters: Bool
    @Binding var showingAddPerson: Bool
    @Binding var showingDemoMode: Bool
    let celebrationTriggered: Bool

    var body: some View {
        ZStack {
            // Dynamic emotional background
            EmotionalBackgroundView(people: peopleManager.filteredPeople)
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Revolutionary header with blur background
                EmotionalHeaderView(
                    peopleManager: peopleManager,
                    selectedViewMode: $selectedViewMode,
                    showingSearch: $showingSearch,
                    showingFilters: $showingFilters,
                    showingDemoMode: $showingDemoMode
                )
                .padding(.horizontal)
                .padding(.top, 8)
                .padding(.bottom, 12)
                .background(
                    .ultraThinMaterial,
                    in: RoundedRectangle(cornerRadius: 0)
                )
                .overlay(
                    Rectangle()
                        .fill(Color.blue.opacity(0.08))
                        .frame(height: 1),
                    alignment: .bottom
                )

                // Filter controls with emotional styling
                if showingFilters {
                    EmotionalFilterView(peopleManager: peopleManager)
                        .transition(.move(edge: .top).combined(with: .opacity))
                }



                // Main contacts area with emotional design
                EmotionalContactsArea(
                    peopleManager: peopleManager,
                    navigationCoordinator: navigationCoordinator,
                    viewMode: selectedViewMode,
                    celebrationTriggered: celebrationTriggered
                )
                .padding(.top, 20)
            }

            // Floating emotional actions
            VStack {
                Spacer()
                EmotionalFloatingActions(
                    peopleManager: peopleManager,
                    showingAddPerson: $showingAddPerson
                )
                .padding(.bottom, 34)
            }
        }
    }
}

/// Dynamic emotional background that responds to relationships
struct EmotionalBackgroundView: View {
    let people: [Person]
    @State private var animationOffset: CGFloat = 0

    var dominantColor: Color {
        guard !people.isEmpty else { return .blue }

        let relationshipCounts = Dictionary(grouping: people) { $0.relationshipType }
        let mostCommon = relationshipCounts.max { $0.value.count < $1.value.count }
        return mostCommon?.key.color ?? .blue
    }

    var body: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(.systemBackground),
                    dominantColor.opacity(0.05),
                    dominantColor.opacity(0.1)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Animated emotional particles
            ForEach(0..<20, id: \.self) { index in
                Circle()
                    .fill(dominantColor.opacity(0.1))
                    .frame(width: CGFloat.random(in: 4...12))
                    .offset(
                        x: CGFloat.random(in: -200...200) + animationOffset,
                        y: CGFloat.random(in: -300...300)
                    )
                    .animation(
                        .linear(duration: Double.random(in: 10...20))
                        .repeatForever(autoreverses: false),
                        value: animationOffset
                    )
            }
        }
        .onAppear {
            animationOffset = 400
        }
    }
}

/// Revolutionary emotional header with intelligent controls
struct EmotionalHeaderView: View {
    @ObservedObject var peopleManager: PeopleManager
    @Binding var selectedViewMode: PeopleListView.ViewMode
    @Binding var showingSearch: Bool
    @Binding var showingFilters: Bool
    @Binding var showingDemoMode: Bool

    var body: some View {
        VStack(spacing: 16) {
            // Global search bar
            GlobalSearchBar()

            // Main header row
            HStack {
                if !showingSearch {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Contacts")
                                .font(.largeTitle)
                                .fontWeight(.bold)

                            Spacer()
                        }

                        Text("\(peopleManager.filteredPeople.count) contacts")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }

                // Enhanced search field with suggestions
                if showingSearch {
                    VStack(spacing: 0) {
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.secondary)

                            TextField("Search your connections...", text: $peopleManager.searchText)
                                .textFieldStyle(PlainTextFieldStyle())
                                .onSubmit {
                                    peopleManager.addToSearchHistory(peopleManager.searchText)
                                }
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )

                        // Search suggestions
                        if !peopleManager.searchText.isEmpty && !peopleManager.searchSuggestions.isEmpty {
                            PeopleSearchSuggestionsView(
                                suggestions: peopleManager.searchSuggestions,
                                onSuggestionTapped: { suggestion in
                                    peopleManager.searchText = suggestion
                                    peopleManager.addToSearchHistory(suggestion)
                                }
                            )
                            .transition(.move(edge: .top).combined(with: .opacity))
                        }
                    }
                    .transition(.move(edge: .trailing).combined(with: .opacity))
                }

                // Control buttons with emotional styling
                EmotionalControlButtons(
                    peopleManager: peopleManager,
                    showingSearch: $showingSearch,
                    showingFilters: $showingFilters,
                    showingDemoMode: $showingDemoMode,
                    searchText: $peopleManager.searchText
                )
            }

            // View mode selector
            if !showingSearch {
                HStack(spacing: 0) {
                    ForEach(PeopleListView.ViewMode.allCases, id: \.self) { mode in
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                selectedViewMode = mode
                            }
                        }) {
                            HStack(spacing: 6) {
                                Image(systemName: mode.icon)
                                    .font(.system(size: 14))

                                Text(mode.rawValue)
                                    .font(.system(size: 14, weight: .medium))
                            }
                            .foregroundColor(selectedViewMode == mode ? .white : .primary)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(selectedViewMode == mode ? Color.blue : Color.clear)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(4)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(.systemGray6))
                )
            }
        }
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: showingSearch)
    }
}





/// Emotional control buttons with haptic feedback
struct EmotionalControlButtons: View {
    @ObservedObject var peopleManager: PeopleManager
    @Binding var showingSearch: Bool
    @Binding var showingFilters: Bool
    @Binding var showingDemoMode: Bool
    @Binding var searchText: String

    var body: some View {
        HStack(spacing: 12) {
            // Load sample data button (for debugging)
            Button(action: {
                peopleManager.forceLoadSampleData()
            }) {
                Image(systemName: "person.3.fill")
                    .font(.system(size: 16))
                    .foregroundColor(.blue)
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(Color(.systemGray6))
                    )
            }

            // Search toggle
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showingSearch.toggle()
                    if !showingSearch {
                        searchText = ""
                    }
                }
            }) {
                Image(systemName: showingSearch ? "xmark" : "magnifyingglass")
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                    .frame(width: 32, height: 32)
                    .background(
                        Circle()
                            .fill(Color(.systemGray6))
                    )
            }

            // Enhanced filter and sort menu
            Menu {
                // Sort options
                Section("Sort By") {
                    ForEach(SortOption.allCases, id: \.self) { option in
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                peopleManager.setSortOption(option)
                            }
                        }) {
                            HStack {
                                Image(systemName: option.icon)
                                Text(option.rawValue)
                                Spacer()
                                if peopleManager.sortOption == option {
                                    Image(systemName: peopleManager.sortAscending ? "chevron.up" : "chevron.down")
                                }
                            }
                        }
                    }
                }

                Divider()

                // Filter options
                Section("Filters") {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            peopleManager.showFavoritesOnly.toggle()
                        }
                    }) {
                        HStack {
                            Image(systemName: "star.fill")
                            Text("Favorites Only")
                            Spacer()
                            if peopleManager.showFavoritesOnly {
                                Image(systemName: "checkmark")
                            }
                        }
                    }

                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            peopleManager.showOnlineOnly.toggle()
                        }
                    }) {
                        HStack {
                            Image(systemName: "circle.fill")
                                .foregroundColor(.green)
                            Text("Online Only")
                            Spacer()
                            if peopleManager.showOnlineOnly {
                                Image(systemName: "checkmark")
                            }
                        }
                    }
                }

                Divider()

                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        peopleManager.clearFilters()
                    }
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("Clear All")
                    }
                }
            } label: {
                ZStack {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(Color(.systemGray6))
                        )

                    // Active filter indicator
                    if peopleManager.showFavoritesOnly || peopleManager.showOnlineOnly ||
                       peopleManager.selectedRelationshipType != nil ||
                       peopleManager.sortOption != .name {
                        Circle()
                            .fill(.red)
                            .frame(width: 8, height: 8)
                            .offset(x: 10, y: -10)
                    }
                }
            }
        }
    }
}



// MARK: - Enhanced Search Components

/// Smart search suggestions view with enhanced UX
struct PeopleSearchSuggestionsView: View {
    let suggestions: [String]
    let onSuggestionTapped: (String) -> Void

    var body: some View {
        VStack(spacing: 0) {
            ForEach(suggestions, id: \.self) { suggestion in
                Button(action: {
                    // Haptic feedback
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    onSuggestionTapped(suggestion)
                }) {
                    HStack {
                        Image(systemName: getIconForSuggestion(suggestion))
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                            .frame(width: 20)

                        Text(suggestion)
                            .font(.system(size: 14))
                            .foregroundColor(.primary)

                        Spacer()

                        Image(systemName: "arrow.up.left")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        Rectangle()
                            .fill(Color(.systemBackground))
                    )
                }
                .buttonStyle(PlainButtonStyle())

                if suggestion != suggestions.last {
                    Divider()
                        .padding(.leading, 32)
                }
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .padding(.top, 4)
    }

    private func getIconForSuggestion(_ suggestion: String) -> String {
        let lowercased = suggestion.lowercased()

        if lowercased.contains("family") {
            return "person.2.fill"
        } else if lowercased.contains("friend") {
            return "heart.fill"
        } else if lowercased.contains("colleague") || lowercased.contains("work") {
            return "briefcase.fill"
        } else if lowercased.contains("client") {
            return "person.badge.plus"
        } else if lowercased.contains("online") {
            return "circle.fill"
        } else if lowercased.contains("favorite") {
            return "star.fill"
        } else if lowercased.contains("recent") {
            return "clock.fill"
        } else {
            return "magnifyingglass"
        }
    }
}

struct PeopleListView_Previews: PreviewProvider {
    static var previews: some View {
        PeopleListView(navigationCoordinator: NavigationCoordinator())
    }
}
