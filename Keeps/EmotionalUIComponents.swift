//
//  EmotionalUIComponents.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Emotional insights card showing relationship analytics
struct EmotionalInsightsCard: View {
    let analytics: PeopleAnalytics
    @State private var animateProgress = false

    // Computed property for close connections (daily + weekly interactions)
    private var closeConnectionsCount: Int {
        // For now, we'll estimate close connections as favorites + some percentage of total
        // In a real implementation, this would be based on interaction frequency
        return analytics.favoritesPeople + max(0, analytics.totalPeople / 4)
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("💝 Connection Insights")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("Today")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(Color(.systemGray6))
                    )
            }
            
            // Emotional metrics
            HStack(spacing: 20) {
                EmotionalMetric(
                    icon: "heart.fill",
                    title: "Favorites",
                    value: "\(analytics.favoritesPeople)",
                    color: .red,
                    progress: Double(analytics.favoritesPeople) / Double(max(analytics.totalPeople, 1)),
                    animateProgress: animateProgress
                )

                EmotionalMetric(
                    icon: "circle.fill",
                    title: "Online",
                    value: "\(analytics.onlinePeople)",
                    color: .green,
                    progress: Double(analytics.onlinePeople) / Double(max(analytics.totalPeople, 1)),
                    animateProgress: animateProgress
                )

                EmotionalMetric(
                    icon: "person.2.fill",
                    title: "Close",
                    value: "\(closeConnectionsCount)",
                    color: .blue,
                    progress: Double(closeConnectionsCount) / Double(max(analytics.totalPeople, 1)),
                    animateProgress: animateProgress
                )
            }
            
            // Relationship distribution
            if !analytics.relationshipBreakdown.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Relationship Types")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    HStack(spacing: 4) {
                        ForEach(Array(analytics.relationshipBreakdown.keys.sorted(by: { $0.rawValue < $1.rawValue })), id: \.self) { type in
                            let count = analytics.relationshipBreakdown[type] ?? 0
                            let percentage = Double(count) / Double(max(analytics.totalPeople, 1))
                            
                            Rectangle()
                                .fill(type.color)
                                .frame(height: 6)
                                .frame(maxWidth: .infinity)
                                .scaleEffect(x: animateProgress ? percentage : 0, anchor: .leading)
                                .animation(.spring(response: 1.0, dampingFraction: 0.8).delay(0.3), value: animateProgress)
                        }
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 3)
                            .fill(Color(.systemGray5))
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 3))
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 4)
        )
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2)) {
                animateProgress = true
            }
        }
    }
}

/// Individual emotional metric
struct EmotionalMetric: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    let progress: Double
    let animateProgress: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .stroke(color.opacity(0.2), lineWidth: 3)
                    .frame(width: 40, height: 40)
                
                Circle()
                    .trim(from: 0, to: animateProgress ? progress : 0)
                    .stroke(color, style: StrokeStyle(lineWidth: 3, lineCap: .round))
                    .frame(width: 40, height: 40)
                    .rotationEffect(.degrees(-90))
                    .animation(.spring(response: 1.0, dampingFraction: 0.8).delay(0.1), value: animateProgress)
                
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(color)
            }
            
            VStack(spacing: 2) {
                Text(value)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

/// Emotional filter view with enhanced styling
struct EmotionalFilterView: View {
    @ObservedObject var peopleManager: PeopleManager
    
    var body: some View {
        VStack(spacing: 16) {
            // Relationship type filter
            VStack(alignment: .leading, spacing: 8) {
                Text("💫 Relationship Type")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        EmotionalFilterChip(
                            title: "All",
                            isSelected: peopleManager.selectedRelationshipType == nil,
                            color: .blue
                        ) {
                            EmotionalHapticsManager.shared.playConnectionEstablished()
                            peopleManager.selectedRelationshipType = nil
                        }
                        
                        ForEach(Person.RelationshipType.allCases, id: \.self) { type in
                            EmotionalFilterChip(
                                title: type.rawValue,
                                isSelected: peopleManager.selectedRelationshipType == type,
                                color: type.color
                            ) {
                                EmotionalHapticsManager.shared.playConnectionEstablished()
                                peopleManager.selectedRelationshipType = type
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }
            
            // Quick filters
            HStack(spacing: 16) {
                EmotionalToggleFilter(
                    icon: "heart.fill",
                    title: "Favorites",
                    isOn: $peopleManager.showFavoritesOnly,
                    color: .red
                )
                
                EmotionalToggleFilter(
                    icon: "circle.fill",
                    title: "Online",
                    isOn: $peopleManager.showOnlineOnly,
                    color: .green
                )
                
                Spacer()
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.05), radius: 8)
        )
        .padding(.horizontal)
    }
}

/// Emotional filter chip
struct EmotionalFilterChip: View {
    let title: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(isSelected ? .white : color)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? color : color.opacity(0.1))
                        .shadow(color: isSelected ? color.opacity(0.3) : .clear, radius: 4)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// Emotional toggle filter
struct EmotionalToggleFilter: View {
    let icon: String
    let title: String
    @Binding var isOn: Bool
    let color: Color
    
    var body: some View {
        Button(action: {
            EmotionalHapticsManager.shared.playConnectionEstablished()
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                isOn.toggle()
            }
        }) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isOn ? .white : color)
                
                Text(title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isOn ? .white : color)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isOn ? color : color.opacity(0.1))
                    .shadow(color: isOn ? color.opacity(0.3) : .clear, radius: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// Emotional floating actions
struct EmotionalFloatingActions: View {
    @ObservedObject var peopleManager: PeopleManager
    @Binding var showingAddPerson: Bool
    @State private var isExpanded = false
    
    var body: some View {
        VStack(spacing: 16) {
            // Secondary actions (when expanded)
            if isExpanded {
                VStack(spacing: 12) {
                    EmotionalFloatingButton(
                        icon: "person.crop.circle.badge.plus",
                        color: .blue,
                        size: 44
                    ) {
                        EmotionalHapticsManager.shared.playCelebrationPattern()
                        showingAddPerson = true
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            isExpanded = false
                        }
                    }
                    
                    EmotionalFloatingButton(
                        icon: "heart.circle",
                        color: .red,
                        size: 44
                    ) {
                        EmotionalHapticsManager.shared.playHeartbeatPattern()
                        // Show favorites only
                        peopleManager.showFavoritesOnly.toggle()
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            isExpanded = false
                        }
                    }
                }
                .transition(.move(edge: .bottom).combined(with: .opacity))
            }
            
            // Main action button
            EmotionalFloatingButton(
                icon: isExpanded ? "xmark" : "plus",
                color: .purple,
                size: 56
            ) {
                EmotionalHapticsManager.shared.playConnectionEstablished()
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    isExpanded.toggle()
                }
            }
        }
        .padding(.trailing)
    }
}

/// Individual emotional floating button
struct EmotionalFloatingButton: View {
    let icon: String
    let color: Color
    let size: CGFloat
    let action: () -> Void
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: size * 0.4, weight: .medium))
                .foregroundColor(.white)
                .frame(width: size, height: size)
                .background(
                    ZStack {
                        // Blur background
                        Circle()
                            .fill(.ultraThinMaterial)
                            .overlay(
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            colors: [color.opacity(0.9), color.opacity(0.7)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                            )
                            .shadow(color: color.opacity(0.3), radius: 12, x: 0, y: 6)

                        // Subtle inner glow
                        Circle()
                            .stroke(
                                LinearGradient(
                                    colors: [Color.white.opacity(0.3), Color.clear],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    }
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
    }
}
