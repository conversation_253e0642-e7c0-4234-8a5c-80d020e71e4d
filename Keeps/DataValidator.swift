//
//  DataValidator.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import Foundation
import SwiftUI
import CoreData
import Combine

/// Validates data integrity and consistency across all app sections
class DataValidator: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var validationResults: [ValidationResult] = []
    @Published var isValidating = false
    @Published var validationStatistics = ValidationStatistics()
    
    // MARK: - Private Properties
    
    private let context: NSManagedObjectContext
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Models
    
    struct ValidationResult: Identifiable, Codable {
        let id = UUID()
        let entityType: SyncOperation.EntityType
        let entityId: UUID?
        let validationType: ValidationType
        let severity: ValidationSeverity
        let message: String
        let suggestedFix: String?
        let timestamp: Date
        var isResolved: Bool
        
        enum ValidationType: String, CaseIterable, Codable {
            case dataIntegrity = "dataIntegrity"
            case referentialIntegrity = "referentialInteg<PERSON>"
            case businessLogic = "businessLogic"
            case dataConsistency = "dataConsistency"
            case performanceIssue = "performanceIssue"
            case securityConcern = "securityConcern"
            
            var description: String {
                switch self {
                case .dataIntegrity: return "Data Integrity"
                case .referentialIntegrity: return "Referential Integrity"
                case .businessLogic: return "Business Logic"
                case .dataConsistency: return "Data Consistency"
                case .performanceIssue: return "Performance Issue"
                case .securityConcern: return "Security Concern"
                }
            }
        }
        
        enum ValidationSeverity: String, CaseIterable, Codable {
            case info = "info"
            case warning = "warning"
            case error = "error"
            case critical = "critical"
            
            var description: String {
                switch self {
                case .info: return "Info"
                case .warning: return "Warning"
                case .error: return "Error"
                case .critical: return "Critical"
                }
            }
            
            var color: Color {
                switch self {
                case .info: return .blue
                case .warning: return .orange
                case .error: return .red
                case .critical: return .purple
                }
            }
            
            var icon: String {
                switch self {
                case .info: return "info.circle"
                case .warning: return "exclamationmark.triangle"
                case .error: return "xmark.circle"
                case .critical: return "exclamationmark.octagon"
                }
            }
        }
        
        init(entityType: SyncOperation.EntityType, entityId: UUID? = nil, validationType: ValidationType, severity: ValidationSeverity, message: String, suggestedFix: String? = nil) {
            self.entityType = entityType
            self.entityId = entityId
            self.validationType = validationType
            self.severity = severity
            self.message = message
            self.suggestedFix = suggestedFix
            self.timestamp = Date()
            self.isResolved = false
        }
    }
    
    struct ValidationStatistics: Codable {
        var totalValidations: Int = 0
        var passedValidations: Int = 0
        var failedValidations: Int = 0
        var criticalIssues: Int = 0
        var lastValidation: Date?
        var averageValidationTime: TimeInterval = 0
        var validationsByType: [String: Int] = [:]
        
        var successRate: Double {
            guard totalValidations > 0 else { return 0 }
            return Double(passedValidations) / Double(totalValidations)
        }
        
        var failureRate: Double {
            guard totalValidations > 0 else { return 0 }
            return Double(failedValidations) / Double(totalValidations)
        }
    }
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext) {
        self.context = context
        loadValidationResults()
    }
    
    // MARK: - Public Validation Methods
    
    /// Validate a sync operation before processing
    func validate(_ operation: SyncOperation) async -> Bool {
        let startTime = Date()
        
        do {
            let isValid = try await performValidation(operation)
            
            // Update statistics
            updateValidationStatistics(success: isValid, time: Date().timeIntervalSince(startTime))
            
            return isValid
            
        } catch {
            // Record validation error
            let result = ValidationResult(
                entityType: operation.entityType,
                entityId: operation.entityId,
                validationType: .dataIntegrity,
                severity: .error,
                message: "Validation failed: \(error.localizedDescription)",
                suggestedFix: "Check data format and try again"
            )
            
            validationResults.append(result)
            updateValidationStatistics(success: false, time: Date().timeIntervalSince(startTime))
            
            return false
        }
    }
    
    /// Perform comprehensive data validation across all entities
    @MainActor
    func validateAllData() async {
        guard !isValidating else { return }
        
        isValidating = true
        validationResults.removeAll()
        
        let startTime = Date()
        
        // Validate all entity types
        await validatePeople()
        await validateTeams()
        await validateTimelineEntries()
        await validateRelationships()
        await validateDataConsistency()
        
        // Update statistics
        let validationTime = Date().timeIntervalSince(startTime)
        validationStatistics.lastValidation = Date()
        validationStatistics.averageValidationTime = validationTime
        
        saveValidationResults()
        isValidating = false
    }
    
    /// Validate specific entity by ID
    func validateEntity(_ entityType: SyncOperation.EntityType, id: UUID) async -> [ValidationResult] {
        var results: [ValidationResult] = []
        
        switch entityType {
        case .person:
            results = await validatePerson(id: id)
        case .team:
            results = await validateTeam(id: id)
        case .weekEntry:
            results = await validateWeekEntry(id: id)
        case .accomplishment, .reflection, .milestone:
            results = await validateTimelineComponent(id: id, type: entityType)
        case .relationship:
            results = await validateRelationship(id: id)
        }
        
        validationResults.append(contentsOf: results)
        saveValidationResults()
        
        return results
    }
    
    /// Fix validation issues automatically where possible
    func autoFixIssues() async -> Int {
        var fixedCount = 0
        
        for result in validationResults where !result.isResolved {
            if await attemptAutoFix(result) {
                if let index = validationResults.firstIndex(where: { $0.id == result.id }) {
                    validationResults[index].isResolved = true
                    fixedCount += 1
                }
            }
        }
        
        saveValidationResults()
        return fixedCount
    }
    
    // MARK: - Entity-Specific Validation
    
    private func validatePeople() async {
        let request: NSFetchRequest<PersonEntity> = PersonEntity.fetchRequest()
        
        do {
            let people = try context.fetch(request)
            
            for person in people {
                await validatePersonEntity(person)
            }
            
        } catch {
            let result = ValidationResult(
                entityType: .person,
                validationType: .dataIntegrity,
                severity: .error,
                message: "Failed to fetch people for validation: \(error.localizedDescription)"
            )
            validationResults.append(result)
        }
    }
    
    private func validatePersonEntity(_ person: PersonEntity) async {
        guard let personId = person.id else {
            let result = ValidationResult(
                entityType: .person,
                validationType: .dataIntegrity,
                severity: .critical,
                message: "Person entity missing ID",
                suggestedFix: "Regenerate ID for this person"
            )
            validationResults.append(result)
            return
        }
        
        // Validate required fields
        if person.name?.isEmpty != false {
            let result = ValidationResult(
                entityType: .person,
                entityId: personId,
                validationType: .businessLogic,
                severity: .error,
                message: "Person name is required",
                suggestedFix: "Add a name for this person"
            )
            validationResults.append(result)
        }
        
        // Validate email format if present
        if let email = person.email, !email.isEmpty && !isValidEmail(email) {
            let result = ValidationResult(
                entityType: .person,
                entityId: personId,
                validationType: .dataIntegrity,
                severity: .warning,
                message: "Invalid email format: \(email)",
                suggestedFix: "Correct the email format"
            )
            validationResults.append(result)
        }
        
        // Validate relationships
        await validatePersonRelationships(person)
    }
    
    private func validatePersonRelationships(_ person: PersonEntity) async {
        // Check for orphaned relationships
        // Check for circular references
        // Validate relationship consistency
        
        // This would involve checking the relationships in Core Data
        // For now, we'll add a placeholder validation
        
        if let personId = person.id {
            // Simulate relationship validation
            let hasValidRelationships = true // This would be actual validation logic
            
            if !hasValidRelationships {
                let result = ValidationResult(
                    entityType: .person,
                    entityId: personId,
                    validationType: .referentialIntegrity,
                    severity: .warning,
                    message: "Person has inconsistent relationships",
                    suggestedFix: "Review and fix person relationships"
                )
                validationResults.append(result)
            }
        }
    }
    
    private func validateTeams() async {
        let request: NSFetchRequest<TeamEntity> = TeamEntity.fetchRequest()
        
        do {
            let teams = try context.fetch(request)
            
            for team in teams {
                await validateTeamEntity(team)
            }
            
        } catch {
            let result = ValidationResult(
                entityType: .team,
                validationType: .dataIntegrity,
                severity: .error,
                message: "Failed to fetch teams for validation: \(error.localizedDescription)"
            )
            validationResults.append(result)
        }
    }
    
    private func validateTeamEntity(_ team: TeamEntity) async {
        guard let teamId = team.id else {
            let result = ValidationResult(
                entityType: .team,
                validationType: .dataIntegrity,
                severity: .critical,
                message: "Team entity missing ID",
                suggestedFix: "Regenerate ID for this team"
            )
            validationResults.append(result)
            return
        }
        
        // Validate required fields
        if team.name?.isEmpty != false {
            let result = ValidationResult(
                entityType: .team,
                entityId: teamId,
                validationType: .businessLogic,
                severity: .error,
                message: "Team name is required",
                suggestedFix: "Add a name for this team"
            )
            validationResults.append(result)
        }
        
        // Validate team members
        await validateTeamMembers(team)
    }
    
    private func validateTeamMembers(_ team: TeamEntity) async {
        // Validate that team members exist and are valid
        // Check for duplicate members
        // Validate member roles
        
        guard let teamId = team.id else { return }
        
        // This would involve checking the team-person relationships
        // For now, we'll add a placeholder validation
        
        let hasValidMembers = true // This would be actual validation logic
        
        if !hasValidMembers {
            let result = ValidationResult(
                entityType: .team,
                entityId: teamId,
                validationType: .referentialIntegrity,
                severity: .warning,
                message: "Team has invalid or missing members",
                suggestedFix: "Review and fix team membership"
            )
            validationResults.append(result)
        }
    }
    
    private func validateTimelineEntries() async {
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        
        do {
            let entries = try context.fetch(request)
            
            for entry in entries {
                await validateTimelineEntryEntity(entry)
            }
            
        } catch {
            let result = ValidationResult(
                entityType: .weekEntry,
                validationType: .dataIntegrity,
                severity: .error,
                message: "Failed to fetch timeline entries for validation: \(error.localizedDescription)"
            )
            validationResults.append(result)
        }
    }
    
    private func validateTimelineEntryEntity(_ entry: TimelineEntryEntity) async {
        guard let entryId = entry.id else {
            let result = ValidationResult(
                entityType: .weekEntry,
                validationType: .dataIntegrity,
                severity: .critical,
                message: "Timeline entry missing ID",
                suggestedFix: "Regenerate ID for this entry"
            )
            validationResults.append(result)
            return
        }
        
        // Validate week number
        if entry.weekNumber < 1 || entry.weekNumber > 4000 {
            let result = ValidationResult(
                entityType: .weekEntry,
                entityId: entryId,
                validationType: .businessLogic,
                severity: .error,
                message: "Invalid week number: \(entry.weekNumber)",
                suggestedFix: "Set a valid week number (1-4000)"
            )
            validationResults.append(result)
        }
        
        // Validate dates
        if let startDate = entry.startDate, let endDate = entry.endDate {
            if startDate > endDate {
                let result = ValidationResult(
                    entityType: .weekEntry,
                    entityId: entryId,
                    validationType: .businessLogic,
                    severity: .error,
                    message: "Start date is after end date",
                    suggestedFix: "Correct the date range"
                )
                validationResults.append(result)
            }
        }
    }
    
    // MARK: - Placeholder Methods
    
    private func validateRelationships() async {
        // Validate relationship entities
    }
    
    private func validateDataConsistency() async {
        // Cross-entity consistency checks
    }
    
    private func validatePerson(id: UUID) async -> [ValidationResult] {
        return []
    }
    
    private func validateTeam(id: UUID) async -> [ValidationResult] {
        return []
    }
    
    private func validateWeekEntry(id: UUID) async -> [ValidationResult] {
        return []
    }
    
    private func validateTimelineComponent(id: UUID, type: SyncOperation.EntityType) async -> [ValidationResult] {
        return []
    }
    
    private func validateRelationship(id: UUID) async -> [ValidationResult] {
        return []
    }
    
    // MARK: - Private Helper Methods
    
    private func performValidation(_ operation: SyncOperation) async throws -> Bool {
        // Perform operation-specific validation
        switch operation.operationType {
        case .create:
            return try await validateCreateOperation(operation)
        case .update:
            return try await validateUpdateOperation(operation)
        case .delete:
            return try await validateDeleteOperation(operation)
        case .merge:
            return try await validateMergeOperation(operation)
        case .restore:
            return try await validateRestoreOperation(operation)
        }
    }
    
    private func validateCreateOperation(_ operation: SyncOperation) async throws -> Bool {
        // Validate create operations
        return true
    }
    
    private func validateUpdateOperation(_ operation: SyncOperation) async throws -> Bool {
        // Validate update operations
        return true
    }
    
    private func validateDeleteOperation(_ operation: SyncOperation) async throws -> Bool {
        // Validate delete operations
        return true
    }
    
    private func validateMergeOperation(_ operation: SyncOperation) async throws -> Bool {
        // Validate merge operations
        return true
    }
    
    private func validateRestoreOperation(_ operation: SyncOperation) async throws -> Bool {
        // Validate restore operations
        return true
    }
    
    private func attemptAutoFix(_ result: ValidationResult) async -> Bool {
        // Attempt to automatically fix validation issues
        switch result.validationType {
        case .dataIntegrity:
            return await fixDataIntegrityIssue(result)
        case .referentialIntegrity:
            return await fixReferentialIntegrityIssue(result)
        case .businessLogic:
            return await fixBusinessLogicIssue(result)
        case .dataConsistency:
            return await fixDataConsistencyIssue(result)
        case .performanceIssue:
            return false // Performance issues usually require manual intervention
        case .securityConcern:
            return false // Security concerns require manual review
        }
    }
    
    private func fixDataIntegrityIssue(_ result: ValidationResult) async -> Bool {
        // Attempt to fix data integrity issues
        return false
    }
    
    private func fixReferentialIntegrityIssue(_ result: ValidationResult) async -> Bool {
        // Attempt to fix referential integrity issues
        return false
    }
    
    private func fixBusinessLogicIssue(_ result: ValidationResult) async -> Bool {
        // Attempt to fix business logic issues
        return false
    }
    
    private func fixDataConsistencyIssue(_ result: ValidationResult) async -> Bool {
        // Attempt to fix data consistency issues
        return false
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    private func updateValidationStatistics(success: Bool, time: TimeInterval) {
        validationStatistics.totalValidations += 1
        
        if success {
            validationStatistics.passedValidations += 1
        } else {
            validationStatistics.failedValidations += 1
        }
        
        // Update average time
        let totalTime = validationStatistics.averageValidationTime * Double(validationStatistics.totalValidations - 1) + time
        validationStatistics.averageValidationTime = totalTime / Double(validationStatistics.totalValidations)
    }
    
    private func loadValidationResults() {
        // Load validation results from UserDefaults if needed
    }
    
    private func saveValidationResults() {
        // Save validation results to UserDefaults if needed
    }
}
