//
//  SmartSuggestionsViews.swift
//  Keeps
//
//  Revolutionary UI components for displaying and interacting with smart suggestions
//  Apple-style design with smooth animations and intelligent interactions
//

import SwiftUI

// MARK: - Smart Suggestions Panel

/// Main panel for displaying smart suggestions with Apple-style design
struct SmartSuggestionsPanel: View {
    @ObservedObject var suggestionsEngine: SmartSuggestionsEngine
    @State private var animateCards = false
    @State private var showingAllSuggestions = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            suggestionsHeader
            
            // Active suggestions
            if !suggestionsEngine.activeSuggestions.isEmpty {
                activeSuggestionsView
            } else {
                emptyStateView
            }
            
            // Quick actions
            quickActionsView
        }
        .background(.ultraThinMaterial)
        .cornerRadius(20)
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        .onAppear {
            animateCards = true
        }
        .sheet(isPresented: $showingAllSuggestions) {
            AllSuggestionsView(suggestionsEngine: suggestionsEngine)
        }
    }
    
    private var suggestionsHeader: some View {
        HStack {
            // AI icon with animation
            Circle()
                .fill(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: "brain.head.profile")
                        .font(.headline)
                        .foregroundColor(.white)
                )
                .scaleEffect(animateCards ? 1.0 : 0.8)
                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("Smart Suggestions")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("AI-powered recommendations")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Accuracy indicator
            if suggestionsEngine.suggestionAccuracy > 0 {
                AccuracyIndicator(accuracy: suggestionsEngine.suggestionAccuracy)
            }
            
            // View all button
            Button("View All") {
                showingAllSuggestions = true
            }
            .font(.caption)
            .foregroundColor(.blue)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    private var activeSuggestionsView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                ForEach(Array(suggestionsEngine.activeSuggestions.enumerated()), id: \.element.id) { index, suggestion in
                    SmartSuggestionCard(
                        suggestion: suggestion,
                        onAccept: {
                            suggestionsEngine.acceptSuggestion(suggestion)
                        },
                        onDismiss: {
                            suggestionsEngine.dismissSuggestion(suggestion)
                        }
                    )
                    .opacity(animateCards ? 1 : 0)
                    .offset(x: animateCards ? 0 : 50)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animateCards)
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.bottom, 16)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "lightbulb.circle")
                .font(.title)
                .foregroundColor(.secondary)
            
            Text("No suggestions right now")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text("Keep using the app to get personalized recommendations")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 30)
        .opacity(animateCards ? 1 : 0)
        .animation(.easeOut(duration: 0.6).delay(0.3), value: animateCards)
    }
    
    private var quickActionsView: some View {
        HStack(spacing: 16) {
            Button(action: {
                suggestionsEngine.generateSuggestions(for: .general)
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.clockwise")
                    Text("Refresh")
                }
                .font(.caption)
                .foregroundColor(.blue)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(20)
            }
            
            Button(action: {
                // Toggle learning
                suggestionsEngine.isLearningEnabled.toggle()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: suggestionsEngine.isLearningEnabled ? "brain.head.profile.fill" : "brain.head.profile")
                    Text(suggestionsEngine.isLearningEnabled ? "Learning On" : "Learning Off")
                }
                .font(.caption)
                .foregroundColor(suggestionsEngine.isLearningEnabled ? .green : .gray)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background((suggestionsEngine.isLearningEnabled ? Color.green : Color.gray).opacity(0.1))
                .cornerRadius(20)
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 16)
    }
}

// MARK: - Smart Suggestion Card

struct SmartSuggestionCard: View {
    let suggestion: SmartSuggestion
    let onAccept: () -> Void
    let onDismiss: () -> Void
    
    @State private var isPressed = false
    @State private var showingDetails = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with icon and priority
            HStack {
                Circle()
                    .fill(suggestion.type.color.opacity(0.2))
                    .frame(width: 30, height: 30)
                    .overlay(
                        Image(systemName: suggestion.type.icon)
                            .font(.caption)
                            .foregroundColor(suggestion.type.color)
                    )
                
                Spacer()
                
                PriorityBadge(priority: suggestion.priority)
            }
            
            // Content
            VStack(alignment: .leading, spacing: 8) {
                Text(suggestion.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .lineLimit(2)
                
                Text(suggestion.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
            }
            
            // Confidence indicator
            ConfidenceIndicator(confidence: suggestion.confidence)
            
            // Actions
            HStack(spacing: 8) {
                Button(action: onAccept) {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark")
                        Text("Accept")
                    }
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(suggestion.type.color)
                    .cornerRadius(12)
                }
                
                Button(action: onDismiss) {
                    Image(systemName: "xmark")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(6)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                }
                
                Spacer()
                
                Button(action: { showingDetails = true }) {
                    Image(systemName: "info.circle")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(16)
        .frame(width: 280)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.05))
                .shadow(color: suggestion.type.color.opacity(0.2), radius: isPressed ? 8 : 4, x: 0, y: isPressed ? 4 : 2)
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .sheet(isPresented: $showingDetails) {
            SuggestionDetailView(suggestion: suggestion)
        }
    }
}

// MARK: - Supporting Views

struct AccuracyIndicator: View {
    let accuracy: Double
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "target")
                .font(.caption2)
                .foregroundColor(accuracyColor)
            
            Text("\(Int(accuracy * 100))%")
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(accuracyColor)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(accuracyColor.opacity(0.1))
        .cornerRadius(8)
    }
    
    private var accuracyColor: Color {
        switch accuracy {
        case 0.8...1.0: return .green
        case 0.6...0.8: return .blue
        case 0.4...0.6: return .orange
        default: return .red
        }
    }
}

struct PriorityBadge: View {
    let priority: SuggestionPriority
    
    var body: some View {
        Text(priority.rawValue)
            .font(.caption2)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(priorityColor)
            .cornerRadius(8)
    }
    
    private var priorityColor: Color {
        switch priority {
        case .urgent: return .red
        case .high: return .orange
        case .medium: return .blue
        case .low: return .gray
        }
    }
}

struct ConfidenceIndicator: View {
    let confidence: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("Confidence")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(Int(confidence * 100))%")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(confidenceColor)
            }
            
            ProgressView(value: confidence)
                .progressViewStyle(LinearProgressViewStyle(tint: confidenceColor))
                .scaleEffect(y: 0.5)
        }
    }
    
    private var confidenceColor: Color {
        switch confidence {
        case 0.8...1.0: return .green
        case 0.6...0.8: return .blue
        case 0.4...0.6: return .orange
        default: return .red
        }
    }
}

// MARK: - All Suggestions View

struct AllSuggestionsView: View {
    @ObservedObject var suggestionsEngine: SmartSuggestionsEngine
    @Environment(\.dismiss) private var dismiss
    @State private var selectedCategory: SuggestionCategory = .all
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Category picker
                categoryPicker
                
                // Suggestions list
                suggestionsList
            }
            .navigationTitle("All Suggestions")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var categoryPicker: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(SuggestionCategory.allCases, id: \.self) { category in
                    SuggestionCategoryChip(
                        category: category,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.vertical, 16)
    }
    
    private var suggestionsList: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(filteredSuggestions) { suggestion in
                    SmartSuggestionRow(
                        suggestion: suggestion,
                        onAccept: {
                            suggestionsEngine.acceptSuggestion(suggestion)
                        },
                        onDismiss: {
                            suggestionsEngine.dismissSuggestion(suggestion)
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 100)
        }
    }
    
    private var filteredSuggestions: [SmartSuggestion] {
        switch selectedCategory {
        case .all:
            return suggestionsEngine.activeSuggestions
        case .workflow:
            return suggestionsEngine.activeSuggestions.filter { $0.type == .startWorkflow }
        case .people:
            return suggestionsEngine.activeSuggestions.filter { $0.type == .addPerson || $0.type == .connectPeople }
        case .goals:
            return suggestionsEngine.activeSuggestions.filter { $0.type == .scheduleGoal || $0.type == .celebrateAchievement }
        }
    }
}

// MARK: - Supporting Enums and Views

enum SuggestionCategory: String, CaseIterable {
    case all = "All"
    case workflow = "Workflows"
    case people = "People"
    case goals = "Goals"
    
    var icon: String {
        switch self {
        case .all: return "square.grid.2x2"
        case .workflow: return "arrow.triangle.branch"
        case .people: return "person.crop.circle"
        case .goals: return "target"
        }
    }
}

// CategoryChip is already defined in EnhancedNotesInterface.swift

struct SmartSuggestionRow: View {
    let suggestion: SmartSuggestion
    let onAccept: () -> Void
    let onDismiss: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            // Icon
            Circle()
                .fill(suggestion.type.color.opacity(0.2))
                .frame(width: 50, height: 50)
                .overlay(
                    Image(systemName: suggestion.type.icon)
                        .font(.headline)
                        .foregroundColor(suggestion.type.color)
                )
            
            // Content
            VStack(alignment: .leading, spacing: 4) {
                Text(suggestion.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(suggestion.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                HStack {
                    PriorityBadge(priority: suggestion.priority)
                    
                    Spacer()
                    
                    Text("\(Int(suggestion.confidence * 100))% confidence")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Actions
            VStack(spacing: 8) {
                Button(action: onAccept) {
                    Image(systemName: "checkmark")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(8)
                        .background(suggestion.type.color)
                        .cornerRadius(8)
                }
                
                Button(action: onDismiss) {
                    Image(systemName: "xmark")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(8)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(16)
    }
}

struct SuggestionDetailView: View {
    let suggestion: SmartSuggestion
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    HStack {
                        Circle()
                            .fill(suggestion.type.color.opacity(0.2))
                            .frame(width: 60, height: 60)
                            .overlay(
                                Image(systemName: suggestion.type.icon)
                                    .font(.title2)
                                    .foregroundColor(suggestion.type.color)
                            )
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(suggestion.title)
                                .font(.title2)
                                .fontWeight(.bold)
                            
                            Text(suggestion.type.rawValue)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    // Description
                    Text(suggestion.description)
                        .font(.body)
                    
                    // Metrics
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Suggestion Metrics")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        HStack {
                            VStack(alignment: .leading) {
                                Text("Confidence")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text("\(Int(suggestion.confidence * 100))%")
                                    .font(.title3)
                                    .fontWeight(.semibold)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .leading) {
                                Text("Priority")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                PriorityBadge(priority: suggestion.priority)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .leading) {
                                Text("Context")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text(suggestion.context.rawValue)
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                        }
                    }
                    .padding()
                    .background(.ultraThinMaterial)
                    .cornerRadius(12)
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Suggestion Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Components

struct SuggestionCategoryChip: View {
    let category: SuggestionCategory
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: category.icon)
                    .font(.caption)

                Text(category.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : .blue)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(isSelected ? Color.blue : Color.blue.opacity(0.1))
            .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Smart Suggestions Floating Button

struct SmartSuggestionsFloatingButton: View {
    let smartSuggestionsEngine: SmartSuggestionsEngine
    @Binding var showingSmartSuggestions: Bool

    @State private var isAnimating = false

    var body: some View {
        Button(action: {
            showingSmartSuggestions = true
        }) {
            ZStack {
                Circle()
                    .fill(.ultraThinMaterial)
                    .frame(width: 60, height: 60)
                    .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)

                Image(systemName: "brain.head.profile")
                    .font(.title2)
                    .foregroundColor(.blue)
                    .scaleEffect(isAnimating ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isAnimating)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            isAnimating = true
        }
    }
}
