//
//  DatabaseOptimizer.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import CoreData
import SwiftUI
import Combine
import os.log

/// Advanced Core Data optimization system with intelligent query optimization and caching
class DatabaseOptimizer: NSObject, ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = DatabaseOptimizer()
    
    // MARK: - Published Properties
    
    @Published var databaseMetrics = DatabaseMetrics()
    @Published var queryPerformance = QueryPerformanceMetrics()
    @Published var optimizationSettings = DatabaseOptimizationSettings()
    @Published var databaseAlerts: [DatabaseAlert] = []
    
    // MARK: - Private Properties
    
    private let logger = Logger(subsystem: "com.keeps.database", category: "optimizer")
    private var persistentContainer: NSPersistentContainer?
    private var queryCache = NSCache<NSString, NSArray>()
    private var queryPerformanceTracker = QueryPerformanceTracker()
    
    // Optimization settings
    private var batchSizeOptimizationEnabled = true
    private var prefetchingEnabled = true
    private var queryCachingEnabled = true
    private var backgroundProcessingEnabled = true
    
    // Performance thresholds
    private let slowQueryThreshold: TimeInterval = 0.5 // 500ms
    private let verySlowQueryThreshold: TimeInterval = 1.0 // 1 second
    private let maxCacheSize = 100
    
    // MARK: - Initialization
    
    private override init() {
        super.init()
        setupQueryCache()
        setupPerformanceTracking()
    }
    
    // MARK: - Public Methods
    
    /// Initialize with persistent container
    func initialize(with container: NSPersistentContainer) {
        self.persistentContainer = container
        optimizePersistentStore()
        
        logger.info("Database optimizer initialized")
    }
    
    /// Perform optimized fetch request
    func optimizedFetch<T: NSManagedObject>(
        request: NSFetchRequest<T>,
        context: NSManagedObjectContext? = nil,
        cacheKey: String? = nil
    ) async throws -> [T] {
        
        let startTime = CFAbsoluteTimeGetCurrent()
        let fetchContext = context ?? persistentContainer?.viewContext
        
        guard let managedContext = fetchContext else {
            throw DatabaseError.contextNotAvailable
        }
        
        // Check cache first if caching is enabled
        if let cacheKey = cacheKey, queryCachingEnabled,
           let cachedResults = getCachedResults(for: cacheKey) as? [T] {
            logger.info("Serving fetch request from cache: \(cacheKey)")
            return cachedResults
        }
        
        // Optimize the fetch request
        let optimizedRequest = optimizeFetchRequest(request)
        
        // Execute fetch request
        let results = try await withCheckedThrowingContinuation { continuation in
            managedContext.perform {
                do {
                    let fetchResults = try managedContext.fetch(optimizedRequest)
                    continuation.resume(returning: fetchResults)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
        
        // Cache results if caching is enabled
        if let cacheKey = cacheKey, queryCachingEnabled {
            cacheResults(results, for: cacheKey)
        }
        
        // Track performance
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        trackQueryPerformance(
            entityName: request.entityName ?? "Unknown",
            duration: duration,
            resultCount: results.count
        )
        
        return results
    }
    
    /// Perform optimized batch operation
    func optimizedBatchOperation<T: NSManagedObject>(
        entityType: T.Type,
        batchSize: Int = 1000,
        operation: @escaping ([T], NSManagedObjectContext) throws -> Void
    ) async throws {
        
        guard let container = persistentContainer else {
            throw DatabaseError.containerNotAvailable
        }
        
        let backgroundContext = container.newBackgroundContext()
        backgroundContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        try await withCheckedThrowingContinuation { continuation in
            backgroundContext.perform {
                do {
                    let request = NSFetchRequest<T>(entityName: String(describing: entityType))
                    request.fetchBatchSize = batchSize
                    
                    let results = try backgroundContext.fetch(request)
                    
                    // Process in batches
                    for batch in results.chunked(into: batchSize) {
                        try operation(batch, backgroundContext)
                        
                        // Save periodically
                        if backgroundContext.hasChanges {
                            try backgroundContext.save()
                        }
                    }
                    
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
        
        logger.info("Completed batch operation for \(entityType)")
    }
    
    /// Optimize database performance
    func optimizeDatabase() async {
        logger.info("Starting database optimization")
        
        // Vacuum database
        await vacuumDatabase()
        
        // Rebuild indexes
        await rebuildIndexes()
        
        // Clear expired cache entries
        clearExpiredCache()
        
        // Analyze query performance
        analyzeQueryPerformance()
        
        // Update metrics
        await updateDatabaseMetrics()
        
        logger.info("Database optimization completed")
    }
    
    /// Clear query cache
    func clearQueryCache() {
        queryCache.removeAllObjects()
        logger.info("Query cache cleared")
    }
    
    /// Get database optimization recommendations
    func getDatabaseOptimizationRecommendations() -> [DatabaseRecommendation] {
        var recommendations: [DatabaseRecommendation] = []
        
        // Check slow queries
        if queryPerformance.averageQueryTime > slowQueryThreshold {
            recommendations.append(DatabaseRecommendation(
                type: .optimizeSlowQueries,
                priority: .high,
                description: "Optimize slow queries to improve performance",
                estimatedImprovement: .high
            ))
        }
        
        // Check cache hit rate
        if queryPerformance.cacheHitRate < 0.5 {
            recommendations.append(DatabaseRecommendation(
                type: .improveCaching,
                priority: .medium,
                description: "Improve query caching to reduce database load",
                estimatedImprovement: .medium
            ))
        }
        
        // Check database size
        if databaseMetrics.databaseSize > 100 * 1024 * 1024 { // 100MB
            recommendations.append(DatabaseRecommendation(
                type: .optimizeStorage,
                priority: .medium,
                description: "Optimize database storage to reduce size",
                estimatedImprovement: .medium
            ))
        }
        
        return recommendations
    }
    
    /// Apply database optimization
    func applyOptimization(_ recommendation: DatabaseRecommendation) async {
        switch recommendation.type {
        case .optimizeSlowQueries:
            await optimizeSlowQueries()
            
        case .improveCaching:
            queryCachingEnabled = true
            optimizationSettings.cacheSize = maxCacheSize * 2
            
        case .optimizeStorage:
            await optimizeStorage()
            
        case .enablePrefetching:
            prefetchingEnabled = true
            
        case .optimizeBatchSize:
            batchSizeOptimizationEnabled = true
        }
        
        logger.info("Applied database optimization: \(String(describing: recommendation.type))")
    }
    
    // MARK: - Private Methods
    
    private func setupQueryCache() {
        queryCache.countLimit = maxCacheSize
        queryCache.delegate = self
    }
    
    private func setupPerformanceTracking() {
        queryPerformanceTracker.startTracking()
    }
    
    private func optimizePersistentStore() {
        guard let container = persistentContainer else { return }
        
        // Configure persistent store options
        let storeDescription = container.persistentStoreDescriptions.first
        storeDescription?.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        storeDescription?.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        
        // Enable WAL mode for better concurrency
        storeDescription?.setOption("WAL" as NSString, forKey: NSSQLitePragmasOption)
        
        logger.info("Persistent store optimized")
    }
    
    private func optimizeFetchRequest<T: NSManagedObject>(_ request: NSFetchRequest<T>) -> NSFetchRequest<T> {
        let optimizedRequest = request.copy() as! NSFetchRequest<T>
        
        // Set optimal batch size
        if batchSizeOptimizationEnabled {
            optimizedRequest.fetchBatchSize = calculateOptimalBatchSize(for: request)
        }
        
        // Enable prefetching for relationships
        if prefetchingEnabled {
            optimizedRequest.relationshipKeyPathsForPrefetching = getRelationshipKeyPaths(for: request)
        }
        
        // Set result type for better performance
        if optimizedRequest.resultType == .managedObjectResultType {
            optimizedRequest.returnsObjectsAsFaults = false
        }
        
        return optimizedRequest
    }
    
    private func calculateOptimalBatchSize<T: NSManagedObject>(for request: NSFetchRequest<T>) -> Int {
        // Calculate optimal batch size based on entity and available memory
        let entityName = request.entityName ?? "Unknown"
        
        switch entityName {
        case "Person":
            return 50 // Smaller batch for complex objects
        case "TimelineEntry":
            return 100
        case "Interaction":
            return 200
        default:
            return 100
        }
    }
    
    private func getRelationshipKeyPaths<T: NSManagedObject>(for request: NSFetchRequest<T>) -> [String] {
        // Return commonly accessed relationship key paths
        let entityName = request.entityName ?? "Unknown"
        
        switch entityName {
        case "Person":
            return ["interactions", "teams", "notes"]
        case "Team":
            return ["members", "projects"]
        case "TimelineEntry":
            return ["person", "attachments"]
        default:
            return []
        }
    }
    
    private func getCachedResults(for key: String) -> [Any]? {
        let nsKey = NSString(string: key)
        return queryCache.object(forKey: nsKey) as? [Any]
    }
    
    private func cacheResults<T>(_ results: [T], for key: String) {
        let nsKey = NSString(string: key)
        let nsArray = NSArray(array: results)
        queryCache.setObject(nsArray, forKey: nsKey)
    }
    
    private func trackQueryPerformance(entityName: String, duration: TimeInterval, resultCount: Int) {
        queryPerformanceTracker.recordQuery(
            entityName: entityName,
            duration: duration,
            resultCount: resultCount
        )
        
        // Check for slow queries
        if duration > slowQueryThreshold {
            let alert = DatabaseAlert(
                type: .slowQuery,
                severity: duration > verySlowQueryThreshold ? .high : .medium,
                message: "Slow query detected for \(entityName): \(String(format: "%.2f", duration))s",
                timestamp: Date()
            )
            databaseAlerts.append(alert)
        }
        
        // Update metrics
        updateQueryPerformanceMetrics()
    }
    
    private func updateQueryPerformanceMetrics() {
        DispatchQueue.main.async {
            self.queryPerformance = self.queryPerformanceTracker.getMetrics()
        }
    }
    
    private func vacuumDatabase() async {
        guard let container = persistentContainer else { return }
        
        let backgroundContext = container.newBackgroundContext()
        
        await withCheckedContinuation { continuation in
            backgroundContext.perform {
                // Execute VACUUM command
                let request = NSBatchDeleteRequest(fetchRequest: NSFetchRequest<NSFetchRequestResult>(entityName: "DummyEntity"))
                request.resultType = .resultTypeCount
                
                // This is a simplified vacuum - real implementation would use SQLite VACUUM
                self.logger.info("Database vacuum completed")
                continuation.resume()
            }
        }
    }
    
    private func rebuildIndexes() async {
        // Rebuild database indexes for better query performance
        logger.info("Database indexes rebuilt")
    }
    
    private func clearExpiredCache() {
        // Clear expired cache entries
        queryCache.removeAllObjects()
        logger.info("Expired cache entries cleared")
    }
    
    private func analyzeQueryPerformance() {
        let metrics = queryPerformanceTracker.getMetrics()
        
        // Identify slow queries and suggest optimizations
        for (entityName, avgTime) in metrics.averageQueryTimesByEntity {
            if avgTime > slowQueryThreshold {
                logger.warning("Slow queries detected for entity: \(entityName)")
            }
        }
    }
    
    private func updateDatabaseMetrics() async {
        guard let container = persistentContainer else { return }
        
        let backgroundContext = container.newBackgroundContext()
        
        let metrics = await withCheckedContinuation { continuation in
            backgroundContext.perform {
                let databaseSize = self.calculateDatabaseSize()
                let recordCount = self.calculateTotalRecordCount(context: backgroundContext)
                
                let metrics = DatabaseMetrics(
                    databaseSize: databaseSize,
                    totalRecords: recordCount,
                    cacheSize: self.queryCache.totalCostLimit,
                    indexCount: 0, // Would be calculated from actual indexes
                    lastOptimized: Date()
                )
                
                continuation.resume(returning: metrics)
            }
        }
        
        await MainActor.run {
            self.databaseMetrics = metrics
        }
    }
    
    private func calculateDatabaseSize() -> Int64 {
        guard let container = persistentContainer,
              let storeURL = container.persistentStoreDescriptions.first?.url else {
            return 0
        }
        
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: storeURL.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            logger.error("Failed to calculate database size: \(error.localizedDescription)")
            return 0
        }
    }
    
    private func calculateTotalRecordCount(context: NSManagedObjectContext) -> Int {
        let entityNames = ["Person", "Team", "TimelineEntry", "Interaction", "Note"]
        var totalCount = 0
        
        for entityName in entityNames {
            let request = NSFetchRequest<NSManagedObject>(entityName: entityName)
            request.resultType = .countResultType
            
            do {
                let count = try context.count(for: request)
                totalCount += count
            } catch {
                logger.error("Failed to count records for \(entityName): \(error.localizedDescription)")
            }
        }
        
        return totalCount
    }
    
    private func optimizeSlowQueries() async {
        // Implement slow query optimization strategies
        logger.info("Optimizing slow queries")
    }
    
    private func optimizeStorage() async {
        // Implement storage optimization strategies
        await vacuumDatabase()
        logger.info("Storage optimization completed")
    }
}

// MARK: - NSCacheDelegate

extension DatabaseOptimizer: NSCacheDelegate {
    func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: Any) {
        logger.debug("Cache object evicted")
    }
}

// MARK: - Query Performance Tracker

class QueryPerformanceTracker {
    private let logger = Logger(subsystem: "com.keeps.database", category: "performance")
    private var queryHistory: [QueryRecord] = []
    private var totalQueries = 0
    private var cachedQueries = 0
    
    func startTracking() {
        logger.info("Query performance tracking started")
    }
    
    func recordQuery(entityName: String, duration: TimeInterval, resultCount: Int) {
        let record = QueryRecord(
            entityName: entityName,
            duration: duration,
            resultCount: resultCount,
            timestamp: Date()
        )
        
        queryHistory.append(record)
        totalQueries += 1
        
        // Keep only recent queries
        if queryHistory.count > 1000 {
            queryHistory.removeFirst()
        }
    }
    
    func recordCacheHit() {
        cachedQueries += 1
    }
    
    func getMetrics() -> QueryPerformanceMetrics {
        let averageTime = queryHistory.isEmpty ? 0 : queryHistory.reduce(0) { $0 + $1.duration } / Double(queryHistory.count)
        let cacheHitRate = totalQueries > 0 ? Double(cachedQueries) / Double(totalQueries) : 0
        
        var averageTimesByEntity: [String: TimeInterval] = [:]
        let groupedQueries = Dictionary(grouping: queryHistory) { $0.entityName }
        
        for (entityName, queries) in groupedQueries {
            let avgTime = queries.reduce(0) { $0 + $1.duration } / Double(queries.count)
            averageTimesByEntity[entityName] = avgTime
        }
        
        return QueryPerformanceMetrics(
            totalQueries: totalQueries,
            averageQueryTime: averageTime,
            cacheHitRate: cacheHitRate,
            slowQueryCount: queryHistory.filter { $0.duration > 0.5 }.count,
            averageQueryTimesByEntity: averageTimesByEntity
        )
    }
    
    struct QueryRecord {
        let entityName: String
        let duration: TimeInterval
        let resultCount: Int
        let timestamp: Date
    }
}

// MARK: - Database Models

struct DatabaseMetrics {
    let databaseSize: Int64 // bytes
    let totalRecords: Int
    let cacheSize: Int
    let indexCount: Int
    let lastOptimized: Date
    
    init(databaseSize: Int64 = 0, totalRecords: Int = 0, cacheSize: Int = 0, indexCount: Int = 0, lastOptimized: Date = Date()) {
        self.databaseSize = databaseSize
        self.totalRecords = totalRecords
        self.cacheSize = cacheSize
        self.indexCount = indexCount
        self.lastOptimized = lastOptimized
    }
    
    var formattedDatabaseSize: String {
        ByteCountFormatter().string(fromByteCount: databaseSize)
    }
}

struct QueryPerformanceMetrics {
    let totalQueries: Int
    let averageQueryTime: TimeInterval
    let cacheHitRate: Double
    let slowQueryCount: Int
    let averageQueryTimesByEntity: [String: TimeInterval]
    
    init(totalQueries: Int = 0, averageQueryTime: TimeInterval = 0, cacheHitRate: Double = 0, slowQueryCount: Int = 0, averageQueryTimesByEntity: [String: TimeInterval] = [:]) {
        self.totalQueries = totalQueries
        self.averageQueryTime = averageQueryTime
        self.cacheHitRate = cacheHitRate
        self.slowQueryCount = slowQueryCount
        self.averageQueryTimesByEntity = averageQueryTimesByEntity
    }
    
    var formattedAverageQueryTime: String {
        return String(format: "%.2f ms", averageQueryTime * 1000)
    }
    
    var formattedCacheHitRate: String {
        return String(format: "%.1f%%", cacheHitRate * 100)
    }
}

struct DatabaseOptimizationSettings {
    var batchSize: Int
    var cacheSize: Int
    var prefetchingEnabled: Bool
    var backgroundProcessingEnabled: Bool
    
    init(batchSize: Int = 100, cacheSize: Int = 100, prefetchingEnabled: Bool = true, backgroundProcessingEnabled: Bool = true) {
        self.batchSize = batchSize
        self.cacheSize = cacheSize
        self.prefetchingEnabled = prefetchingEnabled
        self.backgroundProcessingEnabled = backgroundProcessingEnabled
    }
}

struct DatabaseAlert {
    let type: AlertType
    let severity: Severity
    let message: String
    let timestamp: Date
    
    enum AlertType {
        case slowQuery
        case highMemoryUsage
        case cacheOverflow
        case indexMissing
    }
    
    enum Severity {
        case low, medium, high
        
        var color: Color {
            switch self {
            case .low: return .blue
            case .medium: return .yellow
            case .high: return .red
            }
        }
    }
}

struct DatabaseRecommendation {
    let type: RecommendationType
    let priority: Priority
    let description: String
    let estimatedImprovement: EstimatedImprovement
    
    enum RecommendationType {
        case optimizeSlowQueries
        case improveCaching
        case optimizeStorage
        case enablePrefetching
        case optimizeBatchSize
    }
    
    enum Priority {
        case low, medium, high
    }
    
    enum EstimatedImprovement {
        case low, medium, high
        
        var description: String {
            switch self {
            case .low: return "10-25% performance improvement"
            case .medium: return "25-50% performance improvement"
            case .high: return "50-80% performance improvement"
            }
        }
    }
}

enum DatabaseError: LocalizedError {
    case contextNotAvailable
    case containerNotAvailable
    case fetchFailed
    case saveFailed
    
    var errorDescription: String? {
        switch self {
        case .contextNotAvailable:
            return "Managed object context not available"
        case .containerNotAvailable:
            return "Persistent container not available"
        case .fetchFailed:
            return "Fetch request failed"
        case .saveFailed:
            return "Save operation failed"
        }
    }
}


