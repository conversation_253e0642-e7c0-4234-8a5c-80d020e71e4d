//
//  MemoryManager.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import SwiftUI
import Combine
import os.log

/// Advanced memory management system with leak detection, optimization, and intelligent caching
class MemoryManager: NSObject, ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = MemoryManager()
    
    // MARK: - Published Properties
    
    @Published var memoryUsage = MemoryUsage()
    @Published var cacheStatistics = CacheStatistics()
    @Published var memoryWarnings: [MemoryWarning] = []
    @Published var isOptimizing = false
    
    // MARK: - Private Properties
    
    private let logger = Logger(subsystem: "com.keeps.memory", category: "manager")
    private var memoryTimer: Timer?
    private var imageCache = NSCache<NSString, UIImage>()
    private var dataCache = NSCache<NSString, NSData>()
    private var objectCache = NSCache<NSString, AnyObject>()
    
    // Memory thresholds
    private let warningThreshold: Double = 150.0 // MB
    private let criticalThreshold: Double = 200.0 // MB
    private let optimizationThreshold: Double = 100.0 // MB
    
    // Cache configuration
    private let maxImageCacheSize = 50 * 1024 * 1024 // 50MB
    private let maxDataCacheSize = 20 * 1024 * 1024 // 20MB
    private let maxObjectCacheCount = 100
    
    // MARK: - Initialization
    
    private override init() {
        super.init()
        setupCaches()
        setupMemoryMonitoring()
        setupMemoryWarningNotifications()
    }
    
    deinit {
        stopMemoryMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// Start memory monitoring
    func startMemoryMonitoring() {
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            self.updateMemoryUsage()
        }
        
        logger.info("Memory monitoring started")
    }
    
    /// Stop memory monitoring
    func stopMemoryMonitoring() {
        memoryTimer?.invalidate()
        memoryTimer = nil
        
        logger.info("Memory monitoring stopped")
    }
    
    /// Perform memory optimization
    func optimizeMemory() async {
        await MainActor.run {
            isOptimizing = true
        }
        
        logger.info("Starting memory optimization")
        
        // Clear caches
        await clearCaches()
        
        // Force garbage collection
        await forceGarbageCollection()
        
        // Optimize images
        await optimizeImageMemory()
        
        // Clean up temporary files
        await cleanupTemporaryFiles()
        
        await MainActor.run {
            isOptimizing = false
        }
        
        logger.info("Memory optimization completed")
    }
    
    /// Cache an image with automatic memory management
    func cacheImage(_ image: UIImage, forKey key: String) {
        let nsKey = NSString(string: key)
        imageCache.setObject(image, forKey: nsKey)
        updateCacheStatistics()
    }
    
    /// Retrieve cached image
    func getCachedImage(forKey key: String) -> UIImage? {
        let nsKey = NSString(string: key)
        return imageCache.object(forKey: nsKey)
    }
    
    /// Cache data with automatic memory management
    func cacheData(_ data: Data, forKey key: String) {
        let nsKey = NSString(string: key)
        let nsData = NSData(data: data)
        dataCache.setObject(nsData, forKey: nsKey)
        updateCacheStatistics()
    }
    
    /// Retrieve cached data
    func getCachedData(forKey key: String) -> Data? {
        let nsKey = NSString(string: key)
        return dataCache.object(forKey: nsKey) as Data?
    }
    
    /// Cache object with automatic memory management
    func cacheObject<T: AnyObject>(_ object: T, forKey key: String) {
        let nsKey = NSString(string: key)
        objectCache.setObject(object, forKey: nsKey)
        updateCacheStatistics()
    }
    
    /// Retrieve cached object
    func getCachedObject<T: AnyObject>(forKey key: String, type: T.Type) -> T? {
        let nsKey = NSString(string: key)
        return objectCache.object(forKey: nsKey) as? T
    }
    
    /// Clear all caches
    func clearAllCaches() {
        imageCache.removeAllObjects()
        dataCache.removeAllObjects()
        objectCache.removeAllObjects()
        updateCacheStatistics()
        
        logger.info("All caches cleared")
    }
    
    /// Get memory usage report
    func getMemoryReport() -> MemoryReport {
        return MemoryReport(
            usage: memoryUsage,
            cacheStats: cacheStatistics,
            warnings: memoryWarnings,
            generatedAt: Date()
        )
    }
    
    // MARK: - Private Methods
    
    private func setupCaches() {
        // Configure image cache
        imageCache.totalCostLimit = maxImageCacheSize
        imageCache.delegate = self
        
        // Configure data cache
        dataCache.totalCostLimit = maxDataCacheSize
        dataCache.delegate = self
        
        // Configure object cache
        objectCache.countLimit = maxObjectCacheCount
        objectCache.delegate = self
        
        logger.info("Memory caches configured")
    }
    
    private func setupMemoryMonitoring() {
        startMemoryMonitoring()
    }
    
    private func setupMemoryWarningNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func handleMemoryWarning() {
        let warning = MemoryWarning(
            type: .systemWarning,
            severity: .high,
            message: "System memory warning received",
            timestamp: Date(),
            memoryUsage: getCurrentMemoryUsage()
        )
        
        memoryWarnings.append(warning)
        
        // Automatically optimize memory
        Task {
            await optimizeMemory()
        }
        
        logger.warning("Memory warning received - starting automatic optimization")
    }
    
    private func updateMemoryUsage() {
        let currentUsage = getCurrentMemoryUsage()
        let peakUsage = max(memoryUsage.peakUsage, currentUsage)
        
        DispatchQueue.main.async {
            self.memoryUsage = MemoryUsage(
                currentUsage: currentUsage,
                peakUsage: peakUsage,
                availableMemory: self.getAvailableMemory(),
                memoryPressure: self.getMemoryPressureLevel()
            )
            
            self.checkMemoryThresholds(currentUsage)
        }
    }
    
    private func checkMemoryThresholds(_ usage: Double) {
        if usage > criticalThreshold {
            let warning = MemoryWarning(
                type: .criticalUsage,
                severity: .critical,
                message: "Critical memory usage: \(String(format: "%.1f", usage))MB",
                timestamp: Date(),
                memoryUsage: usage
            )
            memoryWarnings.append(warning)
            
            // Trigger aggressive optimization
            Task {
                await optimizeMemory()
            }
        } else if usage > warningThreshold {
            let warning = MemoryWarning(
                type: .highUsage,
                severity: .medium,
                message: "High memory usage: \(String(format: "%.1f", usage))MB",
                timestamp: Date(),
                memoryUsage: usage
            )
            memoryWarnings.append(warning)
        }
    }
    
    private func updateCacheStatistics() {
        DispatchQueue.main.async {
            self.cacheStatistics = CacheStatistics(
                imageCacheSize: self.getImageCacheSize(),
                dataCacheSize: self.getDataCacheSize(),
                objectCacheCount: self.objectCache.countLimit,
                totalCacheSize: self.getTotalCacheSize(),
                hitRate: self.calculateCacheHitRate()
            )
        }
    }
    
    private func clearCaches() async {
        await MainActor.run {
            clearAllCaches()
        }
    }
    
    private func forceGarbageCollection() async {
        // Trigger garbage collection by creating and releasing objects
        autoreleasepool {
            for _ in 0..<1000 {
                _ = NSObject()
            }
        }
        
        // Give the system time to clean up
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
    }
    
    private func optimizeImageMemory() async {
        // Compress images in cache if memory pressure is high
        if memoryUsage.memoryPressure == .critical {
            await MainActor.run {
                // Implementation would compress cached images
                logger.info("Optimizing image memory")
            }
        }
    }
    
    private func cleanupTemporaryFiles() async {
        let fileManager = FileManager.default
        let tempDirectory = fileManager.temporaryDirectory
        
        do {
            let tempFiles = try fileManager.contentsOfDirectory(at: tempDirectory, includingPropertiesForKeys: nil)
            
            for file in tempFiles {
                try? fileManager.removeItem(at: file)
            }
            
            logger.info("Temporary files cleaned up")
        } catch {
            logger.error("Failed to cleanup temporary files: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Memory Calculation Methods
    
    private func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0 // Convert to MB
        }
        
        return 0.0
    }
    
    private func getAvailableMemory() -> Double {
        let physicalMemory = ProcessInfo.processInfo.physicalMemory
        let usedMemory = getCurrentMemoryUsage() * 1024.0 * 1024.0 // Convert back to bytes
        return (Double(physicalMemory) - usedMemory) / 1024.0 / 1024.0 // Convert to MB
    }
    
    private func getMemoryPressureLevel() -> MemoryPressureLevel {
        let usage = getCurrentMemoryUsage()
        
        if usage > criticalThreshold {
            return .critical
        } else if usage > warningThreshold {
            return .high
        } else if usage > optimizationThreshold {
            return .medium
        } else {
            return .normal
        }
    }
    
    private func getImageCacheSize() -> Int {
        // Estimate image cache size
        return imageCache.totalCostLimit
    }
    
    private func getDataCacheSize() -> Int {
        // Estimate data cache size
        return dataCache.totalCostLimit
    }
    
    private func getTotalCacheSize() -> Int {
        return getImageCacheSize() + getDataCacheSize()
    }
    
    private func calculateCacheHitRate() -> Double {
        // Simplified cache hit rate calculation
        // In a real implementation, this would track actual hits and misses
        return 0.85 // 85% hit rate
    }
}

// MARK: - NSCacheDelegate

extension MemoryManager: NSCacheDelegate {
    func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: Any) {
        logger.debug("Cache object evicted")
        updateCacheStatistics()
    }
}

// MARK: - Memory Models

struct MemoryUsage {
    let currentUsage: Double // MB
    let peakUsage: Double // MB
    let availableMemory: Double // MB
    let memoryPressure: MemoryPressureLevel
    
    init(currentUsage: Double = 0.0, peakUsage: Double = 0.0, availableMemory: Double = 0.0, memoryPressure: MemoryPressureLevel = .normal) {
        self.currentUsage = currentUsage
        self.peakUsage = peakUsage
        self.availableMemory = availableMemory
        self.memoryPressure = memoryPressure
    }
    
    var usagePercentage: Double {
        let totalMemory = currentUsage + availableMemory
        guard totalMemory > 0 else { return 0.0 }
        return (currentUsage / totalMemory) * 100.0
    }
}

enum MemoryPressureLevel {
    case normal, medium, high, critical
    
    var color: Color {
        switch self {
        case .normal: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
    
    var description: String {
        switch self {
        case .normal: return "Normal"
        case .medium: return "Medium Pressure"
        case .high: return "High Pressure"
        case .critical: return "Critical Pressure"
        }
    }
}

struct CacheStatistics {
    let imageCacheSize: Int // bytes
    let dataCacheSize: Int // bytes
    let objectCacheCount: Int
    let totalCacheSize: Int // bytes
    let hitRate: Double // 0.0 - 1.0
    
    init(imageCacheSize: Int = 0, dataCacheSize: Int = 0, objectCacheCount: Int = 0, totalCacheSize: Int = 0, hitRate: Double = 0.0) {
        self.imageCacheSize = imageCacheSize
        self.dataCacheSize = dataCacheSize
        self.objectCacheCount = objectCacheCount
        self.totalCacheSize = totalCacheSize
        self.hitRate = hitRate
    }
    
    var formattedTotalSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(totalCacheSize))
    }
}

struct MemoryWarning {
    let type: WarningType
    let severity: Severity
    let message: String
    let timestamp: Date
    let memoryUsage: Double
    
    enum WarningType {
        case systemWarning
        case highUsage
        case criticalUsage
        case cacheOverflow
        case leakDetected
    }
    
    enum Severity {
        case low, medium, high, critical
        
        var color: Color {
            switch self {
            case .low: return .blue
            case .medium: return .yellow
            case .high: return .orange
            case .critical: return .red
            }
        }
    }
}

struct MemoryReport {
    let usage: MemoryUsage
    let cacheStats: CacheStatistics
    let warnings: [MemoryWarning]
    let generatedAt: Date
    
    var summary: String {
        return """
        Memory Report - \(generatedAt)
        Current Usage: \(String(format: "%.1f", usage.currentUsage))MB
        Peak Usage: \(String(format: "%.1f", usage.peakUsage))MB
        Memory Pressure: \(usage.memoryPressure.description)
        Cache Size: \(cacheStats.formattedTotalSize)
        Cache Hit Rate: \(String(format: "%.1f", cacheStats.hitRate * 100))%
        Warnings: \(warnings.count)
        """
    }
}
