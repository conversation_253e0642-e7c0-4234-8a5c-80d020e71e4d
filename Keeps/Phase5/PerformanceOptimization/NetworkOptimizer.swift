//
//  NetworkOptimizer.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import Network
import SwiftUI
import Combine
import os.log

/// Intelligent network optimization system with smart caching, compression, and offline capabilities
class NetworkOptimizer: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = NetworkOptimizer()
    
    // MARK: - Published Properties
    
    @Published var networkStatus = NetworkStatus()
    @Published var cacheMetrics = NetworkCacheMetrics()
    @Published var dataUsage = DataUsage()
    @Published var isOfflineMode = false
    @Published var networkAlerts: [NetworkAlert] = []
    
    // MARK: - Private Properties
    
    private let logger = Logger(subsystem: "com.keeps.network", category: "optimizer")
    private let monitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "NetworkMonitor")
    
    // Caching
    private let urlCache: URLCache
    private let imageCache = NSCache<NSString, NSData>()
    private let responseCache = NSCache<NSString, NSData>()
    
    // Network optimization settings
    private var compressionEnabled = true
    private var prefetchingEnabled = true
    private var adaptiveQualityEnabled = true
    private var offlineModeEnabled = false
    
    // Data usage tracking
    private var dataUsageTracker = DataUsageTracker()
    private var requestQueue: [NetworkRequest] = []
    private var retryQueue: [FailedRequest] = []
    
    // MARK: - Initialization
    
    private init() {
        // Configure URL cache with larger size
        let cacheSize = 50 * 1024 * 1024 // 50MB
        urlCache = URLCache(memoryCapacity: cacheSize, diskCapacity: cacheSize * 2)
        URLCache.shared = urlCache
        
        setupNetworkMonitoring()
        setupImageCache()
        startDataUsageTracking()
    }
    
    deinit {
        monitor.cancel()
    }
    
    // MARK: - Public Methods
    
    /// Perform optimized network request
    func optimizedRequest<T: Codable>(
        url: URL,
        type: T.Type,
        cachePolicy: CachePolicy = .automatic,
        priority: RequestPriority = .normal
    ) async throws -> T {
        
        // Check cache first
        if let cachedData = getCachedResponse(for: url, cachePolicy: cachePolicy) {
            logger.info("Serving request from cache: \(url)")
            return try JSONDecoder().decode(T.self, from: cachedData)
        }
        
        // Check network availability
        guard networkStatus.isConnected || cachePolicy == .cacheOnly else {
            throw NetworkError.noConnection
        }
        
        // Create optimized request
        let request = createOptimizedRequest(url: url, priority: priority)
        
        // Execute request with retry logic
        let data = try await executeRequestWithRetry(request)
        
        // Cache response
        cacheResponse(data, for: url)
        
        // Track data usage
        dataUsageTracker.recordDataUsage(bytes: data.count, type: .download)
        
        // Decode and return
        return try JSONDecoder().decode(T.self, from: data)
    }
    
    /// Download image with optimization
    func optimizedImageDownload(url: URL, quality: ImageQuality = .automatic) async throws -> Data {
        let cacheKey = NSString(string: url.absoluteString)
        
        // Check cache first
        if let cachedData = imageCache.object(forKey: cacheKey) {
            logger.info("Serving image from cache: \(url)")
            return cachedData as Data
        }
        
        // Adjust quality based on network conditions
        let adjustedQuality = adaptiveQualityEnabled ? adaptQualityToNetwork(quality) : quality
        let optimizedURL = appendQualityParameters(to: url, quality: adjustedQuality)
        
        // Download image
        let request = createOptimizedRequest(url: optimizedURL, priority: .normal)
        let data = try await executeRequestWithRetry(request)
        
        // Cache image
        let nsData = NSData(data: data)
        imageCache.setObject(nsData, forKey: cacheKey)
        
        // Track data usage
        dataUsageTracker.recordDataUsage(bytes: data.count, type: .image)
        
        return data
    }
    
    /// Enable offline mode
    func enableOfflineMode() {
        isOfflineMode = true
        offlineModeEnabled = true
        
        // Process queued requests when connection is restored
        processQueuedRequests()
        
        logger.info("Offline mode enabled")
    }
    
    /// Disable offline mode
    func disableOfflineMode() {
        isOfflineMode = false
        offlineModeEnabled = false
        
        logger.info("Offline mode disabled")
    }
    
    /// Prefetch important data
    func prefetchData(urls: [URL]) async {
        guard prefetchingEnabled && networkStatus.isConnected else { return }
        
        logger.info("Prefetching \(urls.count) resources")
        
        await withTaskGroup(of: Void.self) { group in
            for url in urls {
                group.addTask {
                    do {
                        let request = self.createOptimizedRequest(url: url, priority: .low)
                        let data = try await self.executeRequest(request)
                        self.cacheResponse(data, for: url)
                    } catch {
                        self.logger.error("Prefetch failed for \(url): \(error.localizedDescription)")
                    }
                }
            }
        }
    }
    
    /// Clear network cache
    func clearCache() {
        urlCache.removeAllCachedResponses()
        imageCache.removeAllObjects()
        responseCache.removeAllObjects()
        
        updateCacheMetrics()
        
        logger.info("Network cache cleared")
    }
    
    /// Get network optimization recommendations
    func getNetworkOptimizationRecommendations() -> [NetworkRecommendation] {
        var recommendations: [NetworkRecommendation] = []
        
        // Check data usage
        if dataUsage.totalUsage > 100 * 1024 * 1024 { // 100MB
            recommendations.append(NetworkRecommendation(
                type: .enableCompression,
                priority: .high,
                description: "Enable compression to reduce data usage",
                estimatedSavings: .high
            ))
        }
        
        // Check cache hit rate
        if cacheMetrics.hitRate < 0.5 {
            recommendations.append(NetworkRecommendation(
                type: .optimizeCaching,
                priority: .medium,
                description: "Optimize caching strategy to improve performance",
                estimatedSavings: .medium
            ))
        }
        
        // Check network type
        if networkStatus.connectionType == .cellular {
            recommendations.append(NetworkRecommendation(
                type: .enableAdaptiveQuality,
                priority: .medium,
                description: "Enable adaptive quality for cellular connections",
                estimatedSavings: .medium
            ))
        }
        
        return recommendations
    }
    
    /// Apply network optimization
    func applyOptimization(_ recommendation: NetworkRecommendation) {
        switch recommendation.type {
        case .enableCompression:
            compressionEnabled = true
            
        case .optimizeCaching:
            // Increase cache size and optimize policies
            optimizeCacheSettings()
            
        case .enableAdaptiveQuality:
            adaptiveQualityEnabled = true
            
        case .enablePrefetching:
            prefetchingEnabled = true
            
        case .enableOfflineMode:
            enableOfflineMode()
        }
        
        logger.info("Applied network optimization: \(String(describing: recommendation.type))")
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path)
            }
        }
        monitor.start(queue: monitorQueue)
    }
    
    private func setupImageCache() {
        imageCache.totalCostLimit = 20 * 1024 * 1024 // 20MB
        imageCache.countLimit = 100
    }
    
    private func startDataUsageTracking() {
        dataUsageTracker.startTracking()
    }
    
    private func updateNetworkStatus(_ path: NWPath) {
        let connectionType: NetworkConnectionType
        
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .ethernet
        } else {
            connectionType = .none
        }
        
        let quality = estimateNetworkQuality(path)
        
        networkStatus = NetworkStatus(
            isConnected: path.status == .satisfied,
            connectionType: connectionType,
            quality: quality,
            isExpensive: path.isExpensive,
            isConstrained: path.isConstrained
        )
        
        // Handle connection changes
        handleNetworkChange()
        
        logger.info("Network status updated: \(String(describing: connectionType)), Quality: \(String(describing: quality))")
    }
    
    private func handleNetworkChange() {
        if networkStatus.isConnected {
            // Process queued requests
            processQueuedRequests()
            
            // Process retry queue
            processRetryQueue()
        } else {
            // Enable offline mode if configured
            if offlineModeEnabled {
                enableOfflineMode()
            }
        }
    }
    
    private func createOptimizedRequest(url: URL, priority: RequestPriority) -> URLRequest {
        var request = URLRequest(url: url)
        
        // Set cache policy based on network conditions
        if networkStatus.connectionType == .cellular || networkStatus.isConstrained {
            request.cachePolicy = .returnCacheDataElseLoad
        } else {
            request.cachePolicy = .useProtocolCachePolicy
        }
        
        // Add compression headers
        if compressionEnabled {
            request.setValue("gzip, deflate", forHTTPHeaderField: "Accept-Encoding")
        }
        
        // Set timeout based on network quality
        let timeout: TimeInterval
        switch networkStatus.quality {
        case .excellent, .good:
            timeout = 30.0
        case .fair:
            timeout = 60.0
        case .poor:
            timeout = 120.0
        }
        request.timeoutInterval = timeout
        
        return request
    }
    
    private func executeRequestWithRetry(_ request: URLRequest) async throws -> Data {
        var lastError: Error?
        let maxRetries = 3
        
        for attempt in 0..<maxRetries {
            do {
                return try await executeRequest(request)
            } catch {
                lastError = error
                
                // Don't retry on certain errors
                if let networkError = error as? NetworkError,
                   case .invalidResponse = networkError {
                    throw error
                }
                
                // Exponential backoff
                let delay = pow(2.0, Double(attempt)) * 1.0
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            }
        }
        
        throw lastError ?? NetworkError.requestFailed
    }
    
    private func executeRequest(_ request: URLRequest) async throws -> Data {
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NetworkError.invalidResponse
        }
        
        guard 200...299 ~= httpResponse.statusCode else {
            throw NetworkError.httpError(httpResponse.statusCode)
        }
        
        return data
    }
    
    private func getCachedResponse(for url: URL, cachePolicy: CachePolicy) -> Data? {
        switch cachePolicy {
        case .cacheOnly:
            return getCachedData(for: url)
        case .networkOnly:
            return nil
        case .automatic:
            // Use cache if network is poor or expensive
            if networkStatus.quality == NetworkQuality.poor || networkStatus.isExpensive {
                return getCachedData(for: url)
            }
            return nil
        }
    }
    
    private func getCachedData(for url: URL) -> Data? {
        let request = URLRequest(url: url)
        if let cachedResponse = urlCache.cachedResponse(for: request) {
            return cachedResponse.data
        }
        return nil
    }
    
    private func cacheResponse(_ data: Data, for url: URL) {
        let request = URLRequest(url: url)
        let response = HTTPURLResponse(url: url, statusCode: 200, httpVersion: nil, headerFields: nil)!
        let cachedResponse = CachedURLResponse(response: response, data: data)
        urlCache.storeCachedResponse(cachedResponse, for: request)
        
        updateCacheMetrics()
    }
    
    private func updateCacheMetrics() {
        DispatchQueue.main.async {
            self.cacheMetrics = NetworkCacheMetrics(
                memoryUsage: self.urlCache.currentMemoryUsage,
                diskUsage: self.urlCache.currentDiskUsage,
                hitRate: self.calculateCacheHitRate(),
                totalRequests: self.dataUsageTracker.totalRequests,
                cachedRequests: self.dataUsageTracker.cachedRequests
            )
        }
    }
    
    private func calculateCacheHitRate() -> Double {
        let total = dataUsageTracker.totalRequests
        let cached = dataUsageTracker.cachedRequests
        
        guard total > 0 else { return 0.0 }
        return Double(cached) / Double(total)
    }
    
    private func estimateNetworkQuality(_ path: NWPath) -> NetworkQuality {
        // Simplified quality estimation
        // Real implementation would measure latency and bandwidth
        
        if path.usesInterfaceType(.wifi) {
            return .excellent
        } else if path.usesInterfaceType(.cellular) {
            return path.isConstrained ? .fair : .good
        } else {
            return .poor
        }
    }
    
    private func adaptQualityToNetwork(_ quality: ImageQuality) -> ImageQuality {
        switch networkStatus.quality {
        case .excellent:
            return quality
        case .good:
            return quality == .high ? .medium : quality
        case .fair:
            return .low
        case .poor:
            return .low
        }
    }
    
    private func appendQualityParameters(to url: URL, quality: ImageQuality) -> URL {
        guard var components = URLComponents(url: url, resolvingAgainstBaseURL: false) else {
            return url
        }
        
        let qualityParam = URLQueryItem(name: "quality", value: quality.rawValue)
        components.queryItems = (components.queryItems ?? []) + [qualityParam]
        
        return components.url ?? url
    }
    
    private func processQueuedRequests() {
        // Process requests that were queued during offline mode
        Task {
            for request in requestQueue {
                do {
                    _ = try await executeRequest(request.urlRequest)
                    logger.info("Processed queued request: \(request.url)")
                } catch {
                    logger.error("Failed to process queued request: \(error.localizedDescription)")
                }
            }
            requestQueue.removeAll()
        }
    }
    
    private func processRetryQueue() {
        // Process failed requests that should be retried
        Task {
            for failedRequest in retryQueue {
                do {
                    _ = try await executeRequestWithRetry(failedRequest.request)
                    logger.info("Retry successful for: \(failedRequest.url)")
                } catch {
                    logger.error("Retry failed for: \(failedRequest.url)")
                }
            }
            retryQueue.removeAll()
        }
    }
    
    private func optimizeCacheSettings() {
        // Increase cache sizes
        let newMemoryCapacity = urlCache.memoryCapacity * 2
        let newDiskCapacity = urlCache.diskCapacity * 2
        
        let optimizedCache = URLCache(
            memoryCapacity: newMemoryCapacity,
            diskCapacity: newDiskCapacity
        )
        
        URLCache.shared = optimizedCache
        
        logger.info("Cache settings optimized")
    }
}

// MARK: - Data Usage Tracker

class DataUsageTracker {
    private let logger = Logger(subsystem: "com.keeps.network", category: "usage")
    
    var totalRequests = 0
    var cachedRequests = 0
    private var downloadBytes = 0
    private var uploadBytes = 0
    private var imageBytes = 0
    
    func startTracking() {
        logger.info("Data usage tracking started")
    }
    
    func recordDataUsage(bytes: Int, type: DataType) {
        totalRequests += 1
        
        switch type {
        case .download:
            downloadBytes += bytes
        case .upload:
            uploadBytes += bytes
        case .image:
            imageBytes += bytes
        case .cached:
            cachedRequests += 1
        }
    }
    
    enum DataType {
        case download, upload, image, cached
    }
}

// MARK: - Network Models

struct NetworkStatus {
    let isConnected: Bool
    let connectionType: NetworkConnectionType
    let quality: NetworkQuality
    let isExpensive: Bool
    let isConstrained: Bool
    
    init(isConnected: Bool = false, connectionType: NetworkConnectionType = .none, quality: NetworkQuality = .poor, isExpensive: Bool = false, isConstrained: Bool = false) {
        self.isConnected = isConnected
        self.connectionType = connectionType
        self.quality = quality
        self.isExpensive = isExpensive
        self.isConstrained = isConstrained
    }
}

enum NetworkConnectionType {
    case none, wifi, cellular, ethernet
    
    var displayName: String {
        switch self {
        case .none: return "No Connection"
        case .wifi: return "Wi-Fi"
        case .cellular: return "Cellular"
        case .ethernet: return "Ethernet"
        }
    }
    
    var icon: String {
        switch self {
        case .none: return "wifi.slash"
        case .wifi: return "wifi"
        case .cellular: return "antenna.radiowaves.left.and.right"
        case .ethernet: return "cable.connector"
        }
    }
}

enum NetworkQuality {
    case excellent, good, fair, poor
    
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .yellow
        case .poor: return .red
        }
    }
}

enum CachePolicy {
    case automatic, cacheOnly, networkOnly
}

enum RequestPriority {
    case low, normal, high
}

enum ImageQuality: String {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case automatic = "auto"
}

struct NetworkCacheMetrics {
    let memoryUsage: Int
    let diskUsage: Int
    let hitRate: Double
    let totalRequests: Int
    let cachedRequests: Int
    
    init(memoryUsage: Int = 0, diskUsage: Int = 0, hitRate: Double = 0.0, totalRequests: Int = 0, cachedRequests: Int = 0) {
        self.memoryUsage = memoryUsage
        self.diskUsage = diskUsage
        self.hitRate = hitRate
        self.totalRequests = totalRequests
        self.cachedRequests = cachedRequests
    }
    
    var formattedMemoryUsage: String {
        ByteCountFormatter().string(fromByteCount: Int64(memoryUsage))
    }
    
    var formattedDiskUsage: String {
        ByteCountFormatter().string(fromByteCount: Int64(diskUsage))
    }
}

struct DataUsage {
    let totalUsage: Int64 // bytes
    let downloadUsage: Int64
    let uploadUsage: Int64
    let imageUsage: Int64
    
    init(totalUsage: Int64 = 0, downloadUsage: Int64 = 0, uploadUsage: Int64 = 0, imageUsage: Int64 = 0) {
        self.totalUsage = totalUsage
        self.downloadUsage = downloadUsage
        self.uploadUsage = uploadUsage
        self.imageUsage = imageUsage
    }
    
    var formattedTotalUsage: String {
        ByteCountFormatter().string(fromByteCount: totalUsage)
    }
}

struct NetworkAlert {
    let type: AlertType
    let severity: Severity
    let message: String
    let timestamp: Date
    
    enum AlertType {
        case connectionLost
        case slowConnection
        case highDataUsage
        case cacheOverflow
    }
    
    enum Severity {
        case low, medium, high
        
        var color: Color {
            switch self {
            case .low: return .blue
            case .medium: return .yellow
            case .high: return .red
            }
        }
    }
}

struct NetworkRecommendation {
    let type: RecommendationType
    let priority: Priority
    let description: String
    let estimatedSavings: EstimatedSavings
    
    enum RecommendationType {
        case enableCompression
        case optimizeCaching
        case enableAdaptiveQuality
        case enablePrefetching
        case enableOfflineMode
    }
    
    enum Priority {
        case low, medium, high
    }
    
    enum EstimatedSavings {
        case low, medium, high
        
        var description: String {
            switch self {
            case .low: return "5-15% data savings"
            case .medium: return "15-30% data savings"
            case .high: return "30-50% data savings"
            }
        }
    }
}

enum NetworkError: LocalizedError {
    case noConnection
    case requestFailed
    case invalidResponse
    case httpError(Int)
    
    var errorDescription: String? {
        switch self {
        case .noConnection:
            return "No network connection available"
        case .requestFailed:
            return "Network request failed"
        case .invalidResponse:
            return "Invalid response received"
        case .httpError(let code):
            return "HTTP error: \(code)"
        }
    }
}

// MARK: - Supporting Models

struct NetworkRequest {
    let url: URL
    let urlRequest: URLRequest
    let timestamp: Date
}

struct FailedRequest {
    let url: URL
    let request: URLRequest
    let error: Error
    let retryCount: Int
    let timestamp: Date
}


