//
//  PerformanceDashboard.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI
import Charts
import SwiftUIX

/// Comprehensive performance monitoring dashboard with real-time metrics and optimization controls
struct PerformanceDashboard: View {
    
    // MARK: - State Objects
    
    @StateObject private var performanceMonitor = SystemPerformanceMonitor.shared
    @StateObject private var memoryManager = MemoryManager.shared
    @StateObject private var batteryOptimizer = BatteryOptimizer.shared
    @StateObject private var networkOptimizer = NetworkOptimizer.shared
    @StateObject private var uiOptimizer = UIPerformanceOptimizer.shared
    @StateObject private var databaseOptimizer = DatabaseOptimizer.shared
    
    // MARK: - State Variables
    
    @State private var selectedTab: DashboardTab = .overview
    @State private var showingOptimizationSheet = false
    @State private var showingDetailedMetrics = false
    @State private var isOptimizing = false
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with overall performance score
                performanceHeaderView
                
                // Tab selector
                tabSelectorView
                
                // Content based on selected tab
                ScrollView {
                    LazyVStack(spacing: 16) {
                        switch selectedTab {
                        case .overview:
                            overviewContent
                        case .memory:
                            memoryContent
                        case .battery:
                            batteryContent
                        case .network:
                            networkContent
                        case .ui:
                            uiContent
                        case .database:
                            databaseContent
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 20)
                }
            }
            .navigationTitle("Performance")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Optimize") {
                        showingOptimizationSheet = true
                    }
                    .disabled(isOptimizing)
                }
            }
        }
        .sheet(isPresented: $showingOptimizationSheet) {
            OptimizationControlPanel(isOptimizing: $isOptimizing)
        }
    }
    
    // MARK: - Header View
    
    private var performanceHeaderView: some View {
        VStack(spacing: 12) {
            // Overall performance score
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Overall Performance")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(overallPerformanceGrade.displayName)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(overallPerformanceGrade.color)
                }
                
                Spacer()
                
                // Performance ring
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                        .frame(width: 60, height: 60)
                    
                    Circle()
                        .trim(from: 0, to: overallPerformanceScore)
                        .stroke(overallPerformanceGrade.color, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                        .frame(width: 60, height: 60)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 1.0), value: overallPerformanceScore)
                    
                    Text("\(Int(overallPerformanceScore * 100))")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(overallPerformanceGrade.color)
                }
            }
            
            // Quick metrics
            HStack(spacing: 20) {
                quickMetricView(
                    title: "FPS",
                    value: String(format: "%.0f", uiOptimizer.frameRateMetrics.currentFrameRate),
                    color: uiOptimizer.frameRateMetrics.performanceGrade.color
                )
                
                quickMetricView(
                    title: "Memory",
                    value: String(format: "%.0f MB", memoryManager.memoryUsage.currentUsage),
                    color: memoryManager.memoryUsage.memoryPressure.color
                )
                
                quickMetricView(
                    title: "Battery",
                    value: "\(batteryOptimizer.batteryStatus.levelPercentage)%",
                    color: batteryOptimizer.batteryStatus.statusColor
                )
                
                quickMetricView(
                    title: "Network",
                    value: networkOptimizer.networkStatus.connectionType.displayName,
                    color: networkOptimizer.networkStatus.quality.color
                )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    private func quickMetricView(title: String, value: String, color: Color) -> some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
    
    // MARK: - Tab Selector
    
    private var tabSelectorView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(DashboardTab.allCases, id: \.self) { tab in
                    Button(action: { selectedTab = tab }) {
                        VStack(spacing: 4) {
                            Image(systemName: tab.icon)
                                .font(.title3)
                            
                            Text(tab.displayName)
                                .font(.caption)
                        }
                        .foregroundColor(selectedTab == tab ? .white : .primary)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedTab == tab ? Color.blue : Color.clear)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Content Views
    
    private var overviewContent: some View {
        VStack(spacing: 16) {
            // Performance trends chart
            performanceTrendsChart
            
            // Recent alerts
            recentAlertsView
            
            // Optimization recommendations
            optimizationRecommendationsView
        }
    }
    
    private var memoryContent: some View {
        VStack(spacing: 16) {
            // Memory usage chart
            memoryUsageChart
            
            // Memory statistics
            memoryStatisticsView
            
            // Cache information
            cacheInformationView
        }
    }
    
    private var batteryContent: some View {
        VStack(spacing: 16) {
            // Battery status
            batteryStatusView
            
            // Power mode selector
            powerModeSelectorView
            
            // Battery optimization recommendations
            batteryRecommendationsView
        }
    }
    
    private var networkContent: some View {
        VStack(spacing: 16) {
            // Network status
            networkStatusView
            
            // Data usage chart
            dataUsageChart
            
            // Cache metrics
            networkCacheMetricsView
        }
    }
    
    private var uiContent: some View {
        VStack(spacing: 16) {
            // Frame rate chart
            frameRateChart
            
            // Animation settings
            animationSettingsView
            
            // UI optimization controls
            uiOptimizationControlsView
        }
    }
    
    private var databaseContent: some View {
        VStack(spacing: 16) {
            // Database metrics
            databaseMetricsView
            
            // Query performance
            queryPerformanceView
            
            // Database optimization controls
            databaseOptimizationControlsView
        }
    }
    
    // MARK: - Chart Views
    
    private var performanceTrendsChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Performance Trends")
                .font(.headline)
                .padding(.horizontal, 16)
            
            Chart {
                // Sample data - in real implementation, this would use actual performance history
                ForEach(0..<24, id: \.self) { hour in
                    LineMark(
                        x: .value("Hour", hour),
                        y: .value("Performance", Double.random(in: 60...100))
                    )
                    .foregroundStyle(.blue)
                }
            }
            .frame(height: 200)
            .padding(.horizontal, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    private var memoryUsageChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Memory Usage")
                .font(.headline)
                .padding(.horizontal, 16)
            
            Chart {
                // Sample memory usage data
                ForEach(0..<60, id: \.self) { minute in
                    AreaMark(
                        x: .value("Time", minute),
                        y: .value("Memory", Double.random(in: 50...200))
                    )
                    .foregroundStyle(.red.opacity(0.3))
                }
            }
            .frame(height: 150)
            .padding(.horizontal, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    private var frameRateChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Frame Rate")
                .font(.headline)
                .padding(.horizontal, 16)
            
            Chart {
                // Sample frame rate data
                ForEach(0..<60, id: \.self) { second in
                    LineMark(
                        x: .value("Time", second),
                        y: .value("FPS", Double.random(in: 45...60))
                    )
                    .foregroundStyle(.green)
                }
            }
            .frame(height: 150)
            .padding(.horizontal, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    private var dataUsageChart: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Data Usage")
                .font(.headline)
                .padding(.horizontal, 16)
            
            Chart {
                // Sample data usage
                BarMark(
                    x: .value("Type", "Download"),
                    y: .value("Usage", 45)
                )
                .foregroundStyle(.blue)
                
                BarMark(
                    x: .value("Type", "Upload"),
                    y: .value("Usage", 12)
                )
                .foregroundStyle(.orange)
                
                BarMark(
                    x: .value("Type", "Cache"),
                    y: .value("Usage", 23)
                )
                .foregroundStyle(.green)
            }
            .frame(height: 150)
            .padding(.horizontal, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    // MARK: - Information Views
    
    private var recentAlertsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Alerts")
                .font(.headline)
            
            if performanceMonitor.performanceAlerts.isEmpty {
                Text("No recent alerts")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 20)
            } else {
                ForEach(performanceMonitor.performanceAlerts.prefix(3), id: \.timestamp) { alert in
                    HStack {
                        Circle()
                            .fill(alert.severity.color)
                            .frame(width: 8, height: 8)
                        
                        Text(alert.message)
                            .font(.caption)
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Text(alert.timestamp, style: .time)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    private var optimizationRecommendationsView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Optimization Recommendations")
                .font(.headline)
            
            // Sample recommendations
            ForEach(0..<3, id: \.self) { index in
                HStack {
                    Image(systemName: "lightbulb")
                        .foregroundColor(.yellow)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Optimize Memory Usage")
                            .font(.caption)
                            .fontWeight(.medium)
                        
                        Text("Clear unused caches to free up memory")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("Apply") {
                        // Apply optimization
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .clipShape(Capsule())
                }
                .padding(.vertical, 4)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
    }
    
    // MARK: - Additional Views (Simplified for brevity)
    
    private var memoryStatisticsView: some View {
        // Memory statistics implementation
        Text("Memory Statistics")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var cacheInformationView: some View {
        // Cache information implementation
        Text("Cache Information")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var batteryStatusView: some View {
        // Battery status implementation
        Text("Battery Status")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var powerModeSelectorView: some View {
        // Power mode selector implementation
        Text("Power Mode Selector")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var batteryRecommendationsView: some View {
        // Battery recommendations implementation
        Text("Battery Recommendations")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var networkStatusView: some View {
        // Network status implementation
        Text("Network Status")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var networkCacheMetricsView: some View {
        // Network cache metrics implementation
        Text("Network Cache Metrics")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var animationSettingsView: some View {
        // Animation settings implementation
        Text("Animation Settings")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var uiOptimizationControlsView: some View {
        // UI optimization controls implementation
        Text("UI Optimization Controls")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var databaseMetricsView: some View {
        // Database metrics implementation
        Text("Database Metrics")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var queryPerformanceView: some View {
        // Query performance implementation
        Text("Query Performance")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private var databaseOptimizationControlsView: some View {
        // Database optimization controls implementation
        Text("Database Optimization Controls")
            .padding()
            .background(Color(.systemGray6))
            .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Computed Properties
    
    private var overallPerformanceScore: Double {
        // Calculate overall performance score based on all metrics
        let frameRateScore = uiOptimizer.frameRateMetrics.currentFrameRate / 60.0
        let memoryScore = max(0, 1.0 - (memoryManager.memoryUsage.currentUsage / 200.0))
        let batteryScore = Double(batteryOptimizer.batteryStatus.level)
        let networkScore = networkOptimizer.networkStatus.isConnected ? 1.0 : 0.0
        
        return (frameRateScore + memoryScore + batteryScore + networkScore) / 4.0
    }
    
    private var overallPerformanceGrade: PerformanceGrade {
        let score = overallPerformanceScore
        
        if score >= 0.9 {
            return .excellent
        } else if score >= 0.7 {
            return .good
        } else if score >= 0.5 {
            return .fair
        } else {
            return .poor
        }
    }
}

// MARK: - Dashboard Tab Enum

enum DashboardTab: String, CaseIterable {
    case overview = "overview"
    case memory = "memory"
    case battery = "battery"
    case network = "network"
    case ui = "ui"
    case database = "database"
    
    var displayName: String {
        switch self {
        case .overview: return "Overview"
        case .memory: return "Memory"
        case .battery: return "Battery"
        case .network: return "Network"
        case .ui: return "UI"
        case .database: return "Database"
        }
    }
    
    var icon: String {
        switch self {
        case .overview: return "chart.bar"
        case .memory: return "memorychip"
        case .battery: return "battery.100"
        case .network: return "wifi"
        case .ui: return "paintbrush"
        case .database: return "cylinder"
        }
    }
}

// MARK: - Optimization Control Panel

struct OptimizationControlPanel: View {
    @Binding var isOptimizing: Bool
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Performance Optimization")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("This will optimize all performance aspects of the app")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                if isOptimizing {
                    ProgressView("Optimizing...")
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                } else {
                    Button("Start Optimization") {
                        startOptimization()
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                
                Spacer()
            }
            .padding(20)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .disabled(isOptimizing)
                }
            }
        }
    }
    
    private func startOptimization() {
        isOptimizing = true
        
        Task {
            // Perform optimizations
            await MemoryManager.shared.optimizeMemory()
            await BatteryOptimizer.shared.optimizeForLowBattery()
            await NetworkOptimizer.shared.clearCache()
            await DatabaseOptimizer.shared.optimizeDatabase()
            
            await MainActor.run {
                isOptimizing = false
                dismiss()
            }
        }
    }
}

// MARK: - Preview

struct PerformanceDashboard_Previews: PreviewProvider {
    static var previews: some View {
        PerformanceDashboard()
    }
}
