//
//  PerformanceMonitor.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import SwiftUI
import Combine
import os.log

/// Comprehensive performance monitoring system with real-time metrics and optimization insights
class SystemPerformanceMonitor: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = SystemPerformanceMonitor()
    
    // MARK: - Published Properties
    
    @Published var currentMetrics = SystemPerformanceMetrics()
    @Published var isMonitoring = false
    @Published var performanceAlerts: [PerformanceAlert] = []
    @Published var optimizationSuggestions: [OptimizationSuggestion] = []
    
    // MARK: - Private Properties
    
    private var metricsTimer: Timer?
    private var startTime: CFAbsoluteTime = 0
    private var frameRateMonitor: CADisplayLink?
    private var memoryPressureSource: DispatchSourceMemoryPressure?
    private let logger = Logger(subsystem: "com.keeps.performance", category: "monitor")
    
    // Performance tracking
    private var frameCount = 0
    private var lastFrameTime: CFTimeInterval = 0
    private var frameRates: [Double] = []
    private var memoryUsageHistory: [Double] = []
    private var cpuUsageHistory: [Double] = []
    
    // Thresholds
    private let lowFrameRateThreshold: Double = 45.0
    private let highMemoryThreshold: Double = 200.0 // MB
    private let highCPUThreshold: Double = 80.0 // %
    
    // MARK: - Initialization
    
    private init() {
        setupMemoryPressureMonitoring()
        startMonitoring()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// Start performance monitoring
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        startTime = CFAbsoluteTimeGetCurrent()
        
        // Start metrics collection timer
        metricsTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            self.updateMetrics()
        }
        
        // Start frame rate monitoring
        setupFrameRateMonitoring()
        
        logger.info("Performance monitoring started")
    }
    
    /// Stop performance monitoring
    func stopMonitoring() {
        isMonitoring = false
        
        metricsTimer?.invalidate()
        metricsTimer = nil
        
        frameRateMonitor?.invalidate()
        frameRateMonitor = nil
        
        logger.info("Performance monitoring stopped")
    }
    
    /// Record a performance event
    func recordEvent(_ event: PerformanceEvent) {
        let timestamp = Date()
        
        // Log the event
        logger.info("Performance event: \(event.name) - Duration: \(event.duration)ms")
        
        // Check for performance issues
        checkPerformanceThresholds(for: event)
        
        // Update metrics
        DispatchQueue.main.async {
            self.currentMetrics.addEvent(event)
        }
    }
    
    /// Start timing an operation
    func startTiming(_ operationName: String) -> PerformanceTimer {
        return PerformanceTimer(name: operationName, monitor: self)
    }
    
    /// Get performance report
    func generatePerformanceReport() -> PerformanceReport {
        return PerformanceReport(
            metrics: currentMetrics,
            alerts: performanceAlerts,
            suggestions: optimizationSuggestions,
            generatedAt: Date()
        )
    }
    
    /// Clear performance history
    func clearHistory() {
        currentMetrics = SystemPerformanceMetrics()
        performanceAlerts.removeAll()
        optimizationSuggestions.removeAll()
        frameRates.removeAll()
        memoryUsageHistory.removeAll()
        cpuUsageHistory.removeAll()
    }
    
    // MARK: - Private Methods
    
    private func updateMetrics() {
        let newMetrics = collectCurrentMetrics()
        
        DispatchQueue.main.async {
            self.currentMetrics.update(with: newMetrics)
            self.analyzePerformanceTrends()
        }
    }
    
    private func collectCurrentMetrics() -> SystemPerformanceMetrics {
        var metrics = SystemPerformanceMetrics()
        
        // Memory usage
        metrics.memoryUsage = getCurrentMemoryUsage()
        memoryUsageHistory.append(metrics.memoryUsage)
        if memoryUsageHistory.count > 60 { // Keep last 60 seconds
            memoryUsageHistory.removeFirst()
        }
        
        // CPU usage
        metrics.cpuUsage = getCurrentCPUUsage()
        cpuUsageHistory.append(metrics.cpuUsage)
        if cpuUsageHistory.count > 60 {
            cpuUsageHistory.removeFirst()
        }
        
        // Frame rate
        metrics.frameRate = calculateAverageFrameRate()
        
        // App lifecycle
        metrics.uptime = CFAbsoluteTimeGetCurrent() - startTime
        
        // Battery info
        metrics.batteryLevel = getCurrentBatteryLevel()
        metrics.batteryState = getCurrentBatteryState()
        
        // Disk usage
        metrics.diskUsage = getCurrentDiskUsage()
        
        // Network status
        metrics.networkStatus = getCurrentNetworkStatus()
        
        return metrics
    }
    
    private func setupFrameRateMonitoring() {
        frameRateMonitor = CADisplayLink(target: self, selector: #selector(frameRateCallback))
        frameRateMonitor?.preferredFramesPerSecond = 60
        frameRateMonitor?.add(to: .main, forMode: .common)
    }
    
    @objc private func frameRateCallback(displayLink: CADisplayLink) {
        frameCount += 1
        
        let currentTime = displayLink.timestamp
        if lastFrameTime > 0 {
            let deltaTime = currentTime - lastFrameTime
            let fps = 1.0 / deltaTime
            frameRates.append(fps)
            
            // Keep only recent frame rates
            if frameRates.count > 60 {
                frameRates.removeFirst()
            }
        }
        lastFrameTime = currentTime
    }
    
    private func calculateAverageFrameRate() -> Double {
        guard !frameRates.isEmpty else { return 60.0 }
        return frameRates.reduce(0, +) / Double(frameRates.count)
    }
    
    private func setupMemoryPressureMonitoring() {
        memoryPressureSource = DispatchSource.makeMemoryPressureSource(
            eventMask: [.warning, .critical],
            queue: .main
        )
        
        memoryPressureSource?.setEventHandler { [weak self] in
            self?.handleMemoryPressure()
        }
        
        memoryPressureSource?.resume()
    }
    
    private func handleMemoryPressure() {
        let alert = PerformanceAlert(
            type: .memoryPressure,
            severity: .high,
            message: "High memory pressure detected",
            timestamp: Date()
        )
        
        performanceAlerts.append(alert)
        
        // Suggest memory optimization
        let suggestion = OptimizationSuggestion(
            type: .memoryOptimization,
            priority: .high,
            description: "Consider clearing caches and releasing unused resources",
            estimatedImpact: .high
        )
        
        optimizationSuggestions.append(suggestion)
        
        logger.warning("Memory pressure detected")
    }
    
    private func checkPerformanceThresholds(for event: PerformanceEvent) {
        // Check for slow operations
        if event.duration > 1000 { // 1 second
            let alert = PerformanceAlert(
                type: .slowOperation,
                severity: .medium,
                message: "Slow operation detected: \(event.name) took \(event.duration)ms",
                timestamp: Date()
            )
            performanceAlerts.append(alert)
        }
        
        // Check frame rate
        let currentFPS = calculateAverageFrameRate()
        if currentFPS < lowFrameRateThreshold {
            let alert = PerformanceAlert(
                type: .lowFrameRate,
                severity: .medium,
                message: "Low frame rate detected: \(String(format: "%.1f", currentFPS)) FPS",
                timestamp: Date()
            )
            performanceAlerts.append(alert)
        }
    }
    
    private func analyzePerformanceTrends() {
        // Analyze memory usage trend
        if memoryUsageHistory.count >= 10 {
            let recentAverage = memoryUsageHistory.suffix(5).reduce(0, +) / 5
            let olderAverage = memoryUsageHistory.prefix(5).reduce(0, +) / 5
            
            if recentAverage > olderAverage * 1.5 {
                let suggestion = OptimizationSuggestion(
                    type: .memoryOptimization,
                    priority: .medium,
                    description: "Memory usage is trending upward. Consider optimizing data structures.",
                    estimatedImpact: .medium
                )
                optimizationSuggestions.append(suggestion)
            }
        }
        
        // Analyze CPU usage trend
        if cpuUsageHistory.count >= 10 {
            let recentAverage = cpuUsageHistory.suffix(5).reduce(0, +) / 5
            
            if recentAverage > highCPUThreshold {
                let suggestion = OptimizationSuggestion(
                    type: .cpuOptimization,
                    priority: .high,
                    description: "High CPU usage detected. Consider optimizing algorithms or moving work to background.",
                    estimatedImpact: .high
                )
                optimizationSuggestions.append(suggestion)
            }
        }
    }
    
    // MARK: - System Metrics Collection
    
    private func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0 // Convert to MB
        }
        
        return 0.0
    }
    
    private func getCurrentCPUUsage() -> Double {
        // Simplified CPU calculation - real implementation would use host_processor_info
        return Double.random(in: 0...100) // Placeholder for actual CPU usage calculation
    }
    
    private func getCurrentBatteryLevel() -> Float {
        UIDevice.current.isBatteryMonitoringEnabled = true
        return UIDevice.current.batteryLevel
    }
    
    private func getCurrentBatteryState() -> UIDevice.BatteryState {
        UIDevice.current.isBatteryMonitoringEnabled = true
        return UIDevice.current.batteryState
    }
    
    private func getCurrentDiskUsage() -> DiskUsage {
        let fileManager = FileManager.default
        
        do {
            let systemAttributes = try fileManager.attributesOfFileSystem(forPath: NSHomeDirectory())
            let totalSpace = systemAttributes[.systemSize] as? NSNumber ?? 0
            let freeSpace = systemAttributes[.systemFreeSize] as? NSNumber ?? 0
            
            return DiskUsage(
                totalSpace: totalSpace.int64Value,
                freeSpace: freeSpace.int64Value,
                usedSpace: totalSpace.int64Value - freeSpace.int64Value
            )
        } catch {
            return DiskUsage(totalSpace: 0, freeSpace: 0, usedSpace: 0)
        }
    }
    
    private func getCurrentNetworkStatus() -> SystemNetworkStatus {
        // Simplified network status - real implementation would use Network framework
        return SystemNetworkStatus(
            isConnected: true,
            connectionType: .wifi,
            signalStrength: 0.8
        )
    }
}

// MARK: - Performance Timer

class PerformanceTimer {
    private let name: String
    private let startTime: CFAbsoluteTime
    private weak var monitor: SystemPerformanceMonitor?

    init(name: String, monitor: SystemPerformanceMonitor) {
        self.name = name
        self.monitor = monitor
        self.startTime = CFAbsoluteTimeGetCurrent()
    }
    
    func stop() {
        let duration = (CFAbsoluteTimeGetCurrent() - startTime) * 1000 // Convert to milliseconds
        let event = PerformanceEvent(name: name, duration: duration, timestamp: Date())
        monitor?.recordEvent(event)
    }
}

// MARK: - Performance Models

struct SystemPerformanceMetrics {
    var memoryUsage: Double = 0.0 // MB
    var cpuUsage: Double = 0.0 // %
    var frameRate: Double = 60.0 // FPS
    var uptime: TimeInterval = 0.0 // seconds
    var batteryLevel: Float = 1.0 // 0.0 - 1.0
    var batteryState: UIDevice.BatteryState = .unknown
    var diskUsage: DiskUsage = DiskUsage(totalSpace: 0, freeSpace: 0, usedSpace: 0)
    var networkStatus: SystemNetworkStatus = SystemNetworkStatus(isConnected: false, connectionType: .none, signalStrength: 0.0)
    var events: [PerformanceEvent] = []
    
    mutating func update(with newMetrics: SystemPerformanceMetrics) {
        memoryUsage = newMetrics.memoryUsage
        cpuUsage = newMetrics.cpuUsage
        frameRate = newMetrics.frameRate
        uptime = newMetrics.uptime
        batteryLevel = newMetrics.batteryLevel
        batteryState = newMetrics.batteryState
        diskUsage = newMetrics.diskUsage
        networkStatus = newMetrics.networkStatus
    }
    
    mutating func addEvent(_ event: PerformanceEvent) {
        events.append(event)
        
        // Keep only recent events
        if events.count > 100 {
            events.removeFirst()
        }
    }
}

struct PerformanceEvent {
    let name: String
    let duration: Double // milliseconds
    let timestamp: Date
    let metadata: [String: Any]
    
    init(name: String, duration: Double, timestamp: Date, metadata: [String: Any] = [:]) {
        self.name = name
        self.duration = duration
        self.timestamp = timestamp
        self.metadata = metadata
    }
}

struct PerformanceAlert {
    let type: AlertType
    let severity: Severity
    let message: String
    let timestamp: Date
    
    enum AlertType {
        case memoryPressure
        case lowFrameRate
        case slowOperation
        case highCPUUsage
        case batteryDrain
        case networkIssue
    }
    
    enum Severity {
        case low, medium, high, critical
        
        var color: Color {
            switch self {
            case .low: return .green
            case .medium: return .yellow
            case .high: return .orange
            case .critical: return .red
            }
        }
    }
}

struct OptimizationSuggestion {
    let type: SuggestionType
    let priority: Priority
    let description: String
    let estimatedImpact: Impact
    
    enum SuggestionType {
        case memoryOptimization
        case cpuOptimization
        case batteryOptimization
        case networkOptimization
        case uiOptimization
        case databaseOptimization
    }
    
    enum Priority {
        case low, medium, high, critical
    }
    
    enum Impact {
        case low, medium, high
        
        var description: String {
            switch self {
            case .low: return "Minor improvement"
            case .medium: return "Moderate improvement"
            case .high: return "Significant improvement"
            }
        }
    }
}

struct DiskUsage {
    let totalSpace: Int64
    let freeSpace: Int64
    let usedSpace: Int64
    
    var usagePercentage: Double {
        guard totalSpace > 0 else { return 0.0 }
        return Double(usedSpace) / Double(totalSpace) * 100.0
    }
}

struct SystemNetworkStatus {
    let isConnected: Bool
    let connectionType: ConnectionType
    let signalStrength: Double // 0.0 - 1.0
    
    enum ConnectionType {
        case none, wifi, cellular
    }
}

struct PerformanceReport {
    let metrics: SystemPerformanceMetrics
    let alerts: [PerformanceAlert]
    let suggestions: [OptimizationSuggestion]
    let generatedAt: Date
}
