//
//  RelationshipNetworkModels.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI
import Foundation

// MARK: - Network Node

/// Represents a person or entity in the relationship network
struct NetworkNode: Identifiable, Hashable {
    let id = UUID()
    let personId: UUID
    let name: String
    let initials: String
    let color: Color
    let icon: String?
    var position: CGPoint
    var size: CGFloat
    var strength: Double // Overall relationship strength (0.0 - 1.0)
    var connections: Set<UUID> = []
    // Node type classification
    let nodeType: NodeType

    // Implement Hashable manually since we can't use [String: Any]
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(personId)
        hasher.combine(name)
    }

    static func == (lhs: NetworkNode, rhs: NetworkNode) -> Bool {
        return lhs.id == rhs.id && lhs.personId == rhs.personId
    }
    
    enum NodeType: String, CaseIterable {
        case person = "person"
        case team = "team"
        case project = "project"
        case milestone = "milestone"
        
        var defaultIcon: String {
            switch self {
            case .person: return "person.circle.fill"
            case .team: return "person.3.fill"
            case .project: return "folder.fill"
            case .milestone: return "flag.fill"
            }
        }
        
        var defaultColor: Color {
            switch self {
            case .person: return .blue
            case .team: return .green
            case .project: return .orange
            case .milestone: return .purple
            }
        }
    }
    
    init(personId: UUID, name: String, nodeType: NodeType = .person, strength: Double = 0.5) {
        self.personId = personId
        self.name = name
        self.nodeType = nodeType
        self.strength = strength
        
        // Generate initials
        let nameComponents = name.components(separatedBy: " ")
        self.initials = nameComponents.compactMap { $0.first }.map(String.init).joined()
        
        // Set default appearance
        self.color = nodeType.defaultColor
        self.icon = nodeType.defaultIcon
        self.position = CGPoint(x: Double.random(in: 0.1...0.9), y: Double.random(in: 0.1...0.9))
        self.size = 30 + (strength * 20) // Size based on relationship strength
    }
}

// MARK: - Network Connection

/// Represents a connection between two nodes in the network
struct NetworkConnection: Identifiable, Hashable {
    let id = UUID()
    let fromNode: NetworkNode
    let toNode: NetworkNode
    let strength: Double // Connection strength (0.0 - 1.0)
    let connectionType: ConnectionType
    let label: String
    let color: Color
    let lastInteraction: Date?
    let interactionCount: Int

    // Implement Hashable manually since we can't use [String: Any]
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(fromNode.id)
        hasher.combine(toNode.id)
    }

    static func == (lhs: NetworkConnection, rhs: NetworkConnection) -> Bool {
        return lhs.id == rhs.id && lhs.fromNode.id == rhs.fromNode.id && lhs.toNode.id == rhs.toNode.id
    }
    
    enum ConnectionType: String, CaseIterable {
        case friendship = "friendship"
        case professional = "professional"
        case family = "family"
        case collaboration = "collaboration"
        case mentorship = "mentorship"
        case romantic = "romantic"
        
        var color: Color {
            switch self {
            case .friendship: return .blue
            case .professional: return .green
            case .family: return .red
            case .collaboration: return .orange
            case .mentorship: return .purple
            case .romantic: return .pink
            }
        }
        
        var icon: String {
            switch self {
            case .friendship: return "person.2.fill"
            case .professional: return "briefcase.fill"
            case .family: return "house.fill"
            case .collaboration: return "person.3.sequence.fill"
            case .mentorship: return "graduationcap.fill"
            case .romantic: return "heart.fill"
            }
        }
    }
    
    init(fromNode: NetworkNode, toNode: NetworkNode, connectionType: ConnectionType, strength: Double, interactionCount: Int = 0, lastInteraction: Date? = nil) {
        self.fromNode = fromNode
        self.toNode = toNode
        self.connectionType = connectionType
        self.strength = strength
        self.interactionCount = interactionCount
        self.lastInteraction = lastInteraction
        self.label = connectionType.rawValue.capitalized
        self.color = connectionType.color
    }
}

// MARK: - Network Layout

/// Different layout algorithms for the network visualization
enum NetworkLayout: String, CaseIterable {
    case force = "force"
    case circular = "circular"
    case hierarchical = "hierarchical"
    case grid = "grid"
    case cluster = "cluster"
    
    var displayName: String {
        switch self {
        case .force: return "Force"
        case .circular: return "Circular"
        case .hierarchical: return "Hierarchy"
        case .grid: return "Grid"
        case .cluster: return "Clusters"
        }
    }
    
    var icon: String {
        switch self {
        case .force: return "dot.radiowaves.left.and.right"
        case .circular: return "circle.grid.cross"
        case .hierarchical: return "list.bullet.indent"
        case .grid: return "grid"
        case .cluster: return "circles.hexagongrid"
        }
    }
}

// MARK: - Network Insight

/// Represents an insight or pattern discovered in the relationship network
struct NetworkInsight: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let icon: String
    let priority: InsightPriority
    let category: InsightCategory
    let actionable: Bool
    let confidence: Double // 0.0 - 1.0
    let relatedNodes: [UUID]
    let timestamp: Date
    
    enum InsightPriority: String, CaseIterable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        case critical = "critical"
        
        var color: Color {
            switch self {
            case .low: return .gray
            case .medium: return .blue
            case .high: return .orange
            case .critical: return .red
            }
        }
        
        var backgroundColor: Color {
            switch self {
            case .low: return .gray.opacity(0.1)
            case .medium: return .blue.opacity(0.1)
            case .high: return .orange.opacity(0.1)
            case .critical: return .red.opacity(0.1)
            }
        }
        
        var borderColor: Color {
            switch self {
            case .low: return .gray.opacity(0.3)
            case .medium: return .blue.opacity(0.3)
            case .high: return .orange.opacity(0.3)
            case .critical: return .red.opacity(0.3)
            }
        }
    }
    
    enum InsightCategory: String, CaseIterable {
        case networkHealth = "networkHealth"
        case relationshipGaps = "relationshipGaps"
        case strongConnections = "strongConnections"
        case networkGrowth = "networkGrowth"
        case collaborationOpportunities = "collaborationOpportunities"
        case maintenanceNeeded = "maintenanceNeeded"
        
        var displayName: String {
            switch self {
            case .networkHealth: return "Network Health"
            case .relationshipGaps: return "Relationship Gaps"
            case .strongConnections: return "Strong Connections"
            case .networkGrowth: return "Network Growth"
            case .collaborationOpportunities: return "Collaboration Opportunities"
            case .maintenanceNeeded: return "Maintenance Needed"
            }
        }
        
        var icon: String {
            switch self {
            case .networkHealth: return "heart.text.square"
            case .relationshipGaps: return "exclamationmark.triangle"
            case .strongConnections: return "link.badge.plus"
            case .networkGrowth: return "chart.line.uptrend.xyaxis"
            case .collaborationOpportunities: return "person.3.sequence"
            case .maintenanceNeeded: return "wrench.and.screwdriver"
            }
        }
    }
    
    init(title: String, description: String, priority: InsightPriority, category: InsightCategory, actionable: Bool = false, confidence: Double = 0.8, relatedNodes: [UUID] = []) {
        self.title = title
        self.description = description
        self.priority = priority
        self.category = category
        self.actionable = actionable
        self.confidence = confidence
        self.relatedNodes = relatedNodes
        self.timestamp = Date()
        self.icon = category.icon
    }
}

// MARK: - Time Range

/// Time range filter for network analysis
enum TimeRange: String, CaseIterable {
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    case year = "year"
    case all = "all"
    
    var displayName: String {
        switch self {
        case .week: return "This Week"
        case .month: return "This Month"
        case .quarter: return "This Quarter"
        case .year: return "This Year"
        case .all: return "All Time"
        }
    }
    
    var dateRange: DateInterval? {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .week:
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            return DateInterval(start: startOfWeek, end: now)
        case .month:
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            return DateInterval(start: startOfMonth, end: now)
        case .quarter:
            let startOfQuarter = calendar.dateInterval(of: .quarter, for: now)?.start ?? now
            return DateInterval(start: startOfQuarter, end: now)
        case .year:
            let startOfYear = calendar.dateInterval(of: .year, for: now)?.start ?? now
            return DateInterval(start: startOfYear, end: now)
        case .all:
            return nil
        }
    }
}

// MARK: - Network Statistics

/// Statistical information about the relationship network
struct NetworkStatistics {
    let totalNodes: Int
    let totalConnections: Int
    let averageConnectionStrength: Double
    let networkDensity: Double
    let clusteringCoefficient: Double
    let strongConnectionsCount: Int
    let weakConnectionsCount: Int
    let isolatedNodesCount: Int
    let mostConnectedNode: NetworkNode?
    let strongestConnection: NetworkConnection?
    let networkHealth: NetworkHealth
    
    enum NetworkHealth: String, CaseIterable {
        case excellent = "excellent"
        case good = "good"
        case fair = "fair"
        case poor = "poor"
        
        var color: Color {
            switch self {
            case .excellent: return .green
            case .good: return .blue
            case .fair: return .orange
            case .poor: return .red
            }
        }
        
        var description: String {
            switch self {
            case .excellent: return "Your network is thriving with strong, diverse connections"
            case .good: return "Your network is healthy with room for growth"
            case .fair: return "Your network needs attention to strengthen connections"
            case .poor: return "Your network requires significant improvement"
            }
        }
    }
}

// MARK: - Node Detail View Model

/// Detailed information about a specific node for the detail view
class NodeDetailViewModel: ObservableObject {
    @Published var directConnections: [NetworkConnection] = []
    @Published var mutualConnections: [NetworkNode] = []
    @Published var recentInteractions: [InteractionSummary] = []
    @Published var connectionHistory: [ConnectionHistoryItem] = []
    @Published var insights: [NetworkInsight] = []
    @Published var recommendations: [ActionRecommendation] = []
    @Published var isLoading = false

    func loadDetails(for node: NetworkNode) {
        isLoading = true

        // Simulate loading data - in real implementation, this would fetch from Core Data
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.directConnections = self.generateSampleConnections(for: node)
            self.mutualConnections = self.generateSampleMutualConnections()
            self.recentInteractions = self.generateSampleInteractions()
            self.connectionHistory = self.generateSampleHistory()
            self.insights = self.generateSampleInsights(for: node)
            self.recommendations = self.generateSampleRecommendations(for: node)
            self.isLoading = false
        }
    }

    private func generateSampleConnections(for node: NetworkNode) -> [NetworkConnection] {
        // Sample data generation
        return []
    }

    private func generateSampleMutualConnections() -> [NetworkNode] {
        // Sample data generation
        return []
    }

    private func generateSampleInteractions() -> [InteractionSummary] {
        return [
            InteractionSummary(type: "Meeting", date: Date().addingTimeInterval(-86400), description: "Project discussion", strength: 0.8),
            InteractionSummary(type: "Message", date: Date().addingTimeInterval(-172800), description: "Quick check-in", strength: 0.6),
            InteractionSummary(type: "Call", date: Date().addingTimeInterval(-259200), description: "Weekly sync", strength: 0.7)
        ]
    }

    private func generateSampleHistory() -> [ConnectionHistoryItem] {
        return [
            ConnectionHistoryItem(date: Date().addingTimeInterval(-86400), event: "Collaboration", strengthChange: 0.1, description: "Worked together on project"),
            ConnectionHistoryItem(date: Date().addingTimeInterval(-604800), event: "Meeting", strengthChange: 0.05, description: "Team meeting"),
            ConnectionHistoryItem(date: Date().addingTimeInterval(-1209600), event: "Introduction", strengthChange: 0.3, description: "First meeting")
        ]
    }

    private func generateSampleInsights(for node: NetworkNode) -> [NetworkInsight] {
        return [
            NetworkInsight(
                title: "Strong Connection",
                description: "This is one of your strongest professional relationships",
                priority: .low,
                category: .strongConnections
            )
        ]
    }

    private func generateSampleRecommendations(for node: NetworkNode) -> [ActionRecommendation] {
        return [
            ActionRecommendation(
                title: "Schedule Regular Check-in",
                description: "Set up a monthly coffee chat to maintain this relationship",
                priority: .medium,
                actionType: .schedule
            ),
            ActionRecommendation(
                title: "Introduce to Team",
                description: "Consider introducing them to your current project team",
                priority: .low,
                actionType: .introduce
            )
        ]
    }

    struct InteractionSummary {
        let id = UUID()
        let type: String
        let date: Date
        let description: String
        let strength: Double
    }

    struct ConnectionHistoryItem {
        let id = UUID()
        let date: Date
        let event: String
        let strengthChange: Double
        let description: String
    }

    struct ActionRecommendation {
        let id = UUID()
        let title: String
        let description: String
        let priority: NetworkInsight.InsightPriority
        let actionType: ActionType

        enum ActionType: String, CaseIterable {
            case reachOut = "reachOut"
            case schedule = "schedule"
            case collaborate = "collaborate"
            case introduce = "introduce"
            case strengthen = "strengthen"

            var icon: String {
                switch self {
                case .reachOut: return "phone"
                case .schedule: return "calendar"
                case .collaborate: return "person.3"
                case .introduce: return "person.badge.plus"
                case .strengthen: return "heart"
                }
            }
        }
    }
}
