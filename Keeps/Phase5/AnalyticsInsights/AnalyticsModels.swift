//
//  AnalyticsModels.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI
import Foundation

// MARK: - Analytics Time Range

enum AnalyticsTimeRange: String, CaseIterable {
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    case year = "year"
    case all = "all"
    
    var displayName: String {
        switch self {
        case .week: return "Week"
        case .month: return "Month"
        case .quarter: return "Quarter"
        case .year: return "Year"
        case .all: return "All Time"
        }
    }
    
    var dateRange: DateInterval? {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .week:
            let startOfWeek = calendar.dateInterval(of: .weekOfYear, for: now)?.start ?? now
            return DateInterval(start: startOfWeek, end: now)
        case .month:
            let startOfMonth = calendar.dateInterval(of: .month, for: now)?.start ?? now
            return DateInterval(start: startOfMonth, end: now)
        case .quarter:
            let startOfQuarter = calendar.dateInterval(of: .quarter, for: now)?.start ?? now
            return DateInterval(start: startOfQuarter, end: now)
        case .year:
            let startOfYear = calendar.dateInterval(of: .year, for: now)?.start ?? now
            return DateInterval(start: startOfYear, end: now)
        case .all:
            return nil
        }
    }
}

// MARK: - Analytics Category

enum AnalyticsCategory: String, CaseIterable {
    case overview = "overview"
    case relationships = "relationships"
    case teams = "teams"
    case timeline = "timeline"
    case growth = "growth"
    
    var displayName: String {
        switch self {
        case .overview: return "Overview"
        case .relationships: return "Relationships"
        case .teams: return "Teams"
        case .timeline: return "Timeline"
        case .growth: return "Growth"
        }
    }
    
    var icon: String {
        switch self {
        case .overview: return "chart.pie"
        case .relationships: return "person.2"
        case .teams: return "person.3"
        case .timeline: return "timeline.selection"
        case .growth: return "chart.line.uptrend.xyaxis"
        }
    }
}

// MARK: - Trend Direction

enum TrendDirection: String, CaseIterable {
    case up = "up"
    case down = "down"
    case stable = "stable"
    case unknown = "unknown"
    
    var icon: String {
        switch self {
        case .up: return "arrow.up"
        case .down: return "arrow.down"
        case .stable: return "minus"
        case .unknown: return "questionmark"
        }
    }
    
    var color: Color {
        switch self {
        case .up: return .green
        case .down: return .red
        case .stable: return .blue
        case .unknown: return .gray
        }
    }
    
    var displayText: String {
        switch self {
        case .up: return "↗"
        case .down: return "↘"
        case .stable: return "→"
        case .unknown: return "?"
        }
    }
}

// MARK: - Analytics Insight

struct AnalyticsInsight: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let category: AnalyticsCategory
    let priority: InsightPriority
    let confidence: Double // 0.0 - 1.0
    let actionable: Bool
    let icon: String
    let timestamp: Date
    let recommendations: [String]
    
    enum InsightPriority: String, CaseIterable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        case critical = "critical"
        
        var color: Color {
            switch self {
            case .low: return .gray
            case .medium: return .blue
            case .high: return .orange
            case .critical: return .red
            }
        }
        
        var displayName: String {
            switch self {
            case .low: return "Low"
            case .medium: return "Medium"
            case .high: return "High"
            case .critical: return "Critical"
            }
        }
    }
    
    init(title: String, description: String, category: AnalyticsCategory, priority: InsightPriority, actionable: Bool = false, confidence: Double = 0.8, recommendations: [String] = []) {
        self.title = title
        self.description = description
        self.category = category
        self.priority = priority
        self.actionable = actionable
        self.confidence = confidence
        self.recommendations = recommendations
        self.icon = category.icon
        self.timestamp = Date()
    }
}

// MARK: - Chart Data Point

struct ChartDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let value: Double
    let category: String
    let color: Color

    init(date: Date, value: Double, category: String = "", color: Color = .blue) {
        self.date = date
        self.value = value
        self.category = category
        self.color = color
    }
}

// MARK: - Activity Data

struct ActivityData: Identifiable {
    let id = UUID()
    let date: Date
    let activityType: ActivityType
    let intensity: Double // 0.0 - 1.0
    let duration: TimeInterval
    let participants: [UUID]
    
    enum ActivityType: String, CaseIterable {
        case interaction = "interaction"
        case meeting = "meeting"
        case collaboration = "collaboration"
        case achievement = "achievement"
        case reflection = "reflection"
        
        var color: Color {
            switch self {
            case .interaction: return .blue
            case .meeting: return .green
            case .collaboration: return .orange
            case .achievement: return .purple
            case .reflection: return .indigo
            }
        }
        
        var icon: String {
            switch self {
            case .interaction: return "message"
            case .meeting: return "person.2"
            case .collaboration: return "person.3"
            case .achievement: return "trophy"
            case .reflection: return "brain.head.profile"
            }
        }
    }
    
    init(date: Date, activityType: ActivityType, intensity: Double, duration: TimeInterval = 0, participants: [UUID] = []) {
        self.date = date
        self.activityType = activityType
        self.intensity = intensity
        self.duration = duration
        self.participants = participants
    }
}

// MARK: - Performance Metrics

struct PerformanceMetrics {
    let networkHealth: Double
    let relationshipStrength: Double
    let teamProductivity: Double
    let goalCompletion: Double
    let personalGrowth: Double
    let collaborationIndex: Double
    let engagementLevel: Double
    let achievementRate: Double
    
    var overallScore: Double {
        let metrics = [
            networkHealth,
            relationshipStrength,
            teamProductivity,
            goalCompletion,
            personalGrowth,
            collaborationIndex,
            engagementLevel,
            achievementRate
        ]
        return metrics.reduce(0, +) / Double(metrics.count)
    }
    
    var healthStatus: HealthStatus {
        switch overallScore {
        case 0.8...1.0: return .excellent
        case 0.6..<0.8: return .good
        case 0.4..<0.6: return .fair
        case 0.0..<0.4: return .poor
        default: return .unknown
        }
    }
    
    enum HealthStatus: String, CaseIterable {
        case excellent = "excellent"
        case good = "good"
        case fair = "fair"
        case poor = "poor"
        case unknown = "unknown"
        
        var color: Color {
            switch self {
            case .excellent: return .green
            case .good: return .blue
            case .fair: return .orange
            case .poor: return .red
            case .unknown: return .gray
            }
        }
        
        var description: String {
            switch self {
            case .excellent: return "Excellent performance across all areas"
            case .good: return "Good performance with room for improvement"
            case .fair: return "Fair performance, needs attention"
            case .poor: return "Poor performance, requires immediate action"
            case .unknown: return "Insufficient data for assessment"
            }
        }
    }
}

// MARK: - Goal Progress Data

struct GoalProgressData: Identifiable {
    let id = UUID()
    let goalId: UUID
    let title: String
    let category: String
    let progress: Double // 0.0 - 1.0
    let targetDate: Date
    let isCompleted: Bool
    let milestones: [Milestone]
    let relatedPeople: [UUID]
    let relatedTeams: [UUID]
    
    struct Milestone: Identifiable {
        let id = UUID()
        let title: String
        let targetDate: Date
        let isCompleted: Bool
        let completionDate: Date?
    }
    
    var daysRemaining: Int {
        let calendar = Calendar.current
        return calendar.dateComponents([.day], from: Date(), to: targetDate).day ?? 0
    }
    
    var isOverdue: Bool {
        return !isCompleted && Date() > targetDate
    }
    
    var progressStatus: ProgressStatus {
        if isCompleted {
            return .completed
        } else if isOverdue {
            return .overdue
        } else if progress > 0.8 {
            return .onTrack
        } else if progress > 0.5 {
            return .atRisk
        } else {
            return .behindSchedule
        }
    }
    
    enum ProgressStatus: String, CaseIterable {
        case completed = "completed"
        case onTrack = "onTrack"
        case atRisk = "atRisk"
        case behindSchedule = "behindSchedule"
        case overdue = "overdue"
        
        var color: Color {
            switch self {
            case .completed: return .green
            case .onTrack: return .blue
            case .atRisk: return .orange
            case .behindSchedule: return .red
            case .overdue: return .purple
            }
        }
        
        var displayName: String {
            switch self {
            case .completed: return "Completed"
            case .onTrack: return "On Track"
            case .atRisk: return "At Risk"
            case .behindSchedule: return "Behind Schedule"
            case .overdue: return "Overdue"
            }
        }
    }
}

// MARK: - Skill Development Data

struct SkillDevelopmentData: Identifiable {
    let id = UUID()
    let skillName: String
    let category: SkillCategory
    let currentLevel: Double // 0.0 - 1.0
    let targetLevel: Double
    let progressRate: Double // Progress per week
    let lastUpdated: Date
    let relatedActivities: [UUID]
    let milestones: [SkillMilestone]
    
    enum SkillCategory: String, CaseIterable {
        case technical = "technical"
        case communication = "communication"
        case leadership = "leadership"
        case creativity = "creativity"
        case analytical = "analytical"
        case interpersonal = "interpersonal"
        
        var color: Color {
            switch self {
            case .technical: return .blue
            case .communication: return .green
            case .leadership: return .purple
            case .creativity: return .orange
            case .analytical: return .indigo
            case .interpersonal: return .pink
            }
        }
        
        var icon: String {
            switch self {
            case .technical: return "gear"
            case .communication: return "message"
            case .leadership: return "crown"
            case .creativity: return "paintbrush"
            case .analytical: return "chart.bar"
            case .interpersonal: return "person.2"
            }
        }
    }
    
    struct SkillMilestone: Identifiable {
        let id = UUID()
        let title: String
        let requiredLevel: Double
        let isAchieved: Bool
        let achievedDate: Date?
    }
    
    var progressPercentage: Double {
        return currentLevel / targetLevel
    }
    
    var estimatedCompletionDate: Date {
        let remainingProgress = targetLevel - currentLevel
        let weeksToComplete = remainingProgress / progressRate
        return Calendar.current.date(byAdding: .weekOfYear, value: Int(weeksToComplete), to: Date()) ?? Date()
    }
}

// MARK: - Collaboration Data

struct CollaborationData: Identifiable {
    let id = UUID()
    let participants: [UUID]
    let projectId: UUID?
    let teamId: UUID?
    let collaborationType: CollaborationType
    let frequency: CollaborationFrequency
    let effectiveness: Double // 0.0 - 1.0
    let duration: TimeInterval
    let outcomes: [String]
    let startDate: Date
    let endDate: Date?
    
    enum CollaborationType: String, CaseIterable {
        case project = "project"
        case meeting = "meeting"
        case brainstorming = "brainstorming"
        case mentoring = "mentoring"
        case review = "review"
        case planning = "planning"
        
        var color: Color {
            switch self {
            case .project: return .blue
            case .meeting: return .green
            case .brainstorming: return .orange
            case .mentoring: return .purple
            case .review: return .red
            case .planning: return .indigo
            }
        }
        
        var icon: String {
            switch self {
            case .project: return "folder"
            case .meeting: return "person.2"
            case .brainstorming: return "lightbulb"
            case .mentoring: return "graduationcap"
            case .review: return "checkmark.circle"
            case .planning: return "calendar"
            }
        }
    }
    
    enum CollaborationFrequency: String, CaseIterable {
        case daily = "daily"
        case weekly = "weekly"
        case monthly = "monthly"
        case quarterly = "quarterly"
        case adhoc = "adhoc"
        
        var displayName: String {
            switch self {
            case .daily: return "Daily"
            case .weekly: return "Weekly"
            case .monthly: return "Monthly"
            case .quarterly: return "Quarterly"
            case .adhoc: return "Ad-hoc"
            }
        }
    }
    
    var isActive: Bool {
        return endDate == nil || endDate! > Date()
    }
    
    var collaborationScore: Double {
        // Calculate based on frequency, effectiveness, and duration
        let frequencyScore = frequency == .daily ? 1.0 : frequency == .weekly ? 0.8 : 0.6
        return (effectiveness + frequencyScore) / 2.0
    }
}
