//
//  RelationshipNetworkAnalyzer.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI
import CoreData
import Combine

/// Advanced relationship network analyzer with AI-powered insights and pattern recognition
/// Analyzes relationship data to provide network visualization and intelligent recommendations
class RelationshipNetworkAnalyzer: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var visibleNodes: [NetworkNode] = []
    @Published var visibleConnections: [NetworkConnection] = []
    @Published var keyInsights: [NetworkInsight] = []
    @Published var networkStatistics = NetworkStatistics(
        totalNodes: 0,
        totalConnections: 0,
        averageConnectionStrength: 0.0,
        networkDensity: 0.0,
        clusteringCoefficient: 0.0,
        strongConnectionsCount: 0,
        weakConnectionsCount: 0,
        isolatedNodesCount: 0,
        mostConnectedNode: nil,
        strongestConnection: nil,
        networkHealth: .fair
    )
    
    @Published var isAnalyzing = false
    @Published var analysisProgress: Double = 0.0
    @Published var lastAnalysisDate: Date?
    
    // MARK: - Computed Properties
    
    var totalConnections: Int {
        visibleConnections.count
    }
    
    var strongConnections: Int {
        visibleConnections.filter { $0.strength > 0.7 }.count
    }
    
    var networkDensity: Double {
        guard visibleNodes.count > 1 else { return 0.0 }
        let maxPossibleConnections = visibleNodes.count * (visibleNodes.count - 1) / 2
        return Double(totalConnections) / Double(maxPossibleConnections)
    }
    
    // MARK: - Private Properties
    
    private var allNodes: [NetworkNode] = []
    private var allConnections: [NetworkConnection] = []
    private var currentTimeRange: TimeRange = .all
    private var currentLayout: NetworkLayout = .force
    private var highlightedNodeIds: Set<UUID> = []
    
    // Core Data context
    private let context: NSManagedObjectContext
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext = PersistenceController.shared.container.viewContext) {
        self.context = context
    }
    
    // MARK: - Main Analysis Methods
    
    /// Perform comprehensive relationship network analysis
    @MainActor
    func analyzeRelationshipNetwork() async {
        guard !isAnalyzing else { return }
        
        isAnalyzing = true
        analysisProgress = 0.0
        
        do {
            // Step 1: Load and process people data (20%)
            analysisProgress = 0.2
            let people = try await loadPeopleData()
            
            // Step 2: Build network nodes (40%)
            analysisProgress = 0.4
            allNodes = await buildNetworkNodes(from: people)
            
            // Step 3: Analyze connections (60%)
            analysisProgress = 0.6
            allConnections = await analyzeConnections(between: allNodes)
            
            // Step 4: Apply filters and layout (80%)
            analysisProgress = 0.8
            updateVisibleElements()
            
            // Step 5: Generate insights (100%)
            analysisProgress = 1.0
            keyInsights = await generateNetworkInsights()
            
            // Update statistics
            updateNetworkStatistics()
            
            lastAnalysisDate = Date()
            
        } catch {
            print("Error analyzing relationship network: \(error)")
        }
        
        isAnalyzing = false
    }
    
    /// Update time range filter and re-analyze
    func updateTimeRange(_ timeRange: TimeRange) {
        currentTimeRange = timeRange
        updateVisibleElements()
        
        Task {
            keyInsights = await generateNetworkInsights()
            updateNetworkStatistics()
        }
    }
    
    /// Highlight connections for a specific node
    func highlightConnections(for node: NetworkNode) {
        highlightedNodeIds.removeAll()
        highlightedNodeIds.insert(node.id)
        
        // Add connected nodes
        for connection in visibleConnections {
            if connection.fromNode.id == node.id {
                highlightedNodeIds.insert(connection.toNode.id)
            } else if connection.toNode.id == node.id {
                highlightedNodeIds.insert(connection.fromNode.id)
            }
        }
        
        // Update visual state
        updateVisibleElements()
    }
    
    /// Reset network layout to default positions
    func resetLayout() {
        for i in 0..<allNodes.count {
            allNodes[i].position = generateLayoutPosition(for: allNodes[i], layout: currentLayout, index: i, total: allNodes.count)
        }
        updateVisibleElements()
    }
    
    // MARK: - Data Loading Methods
    
    private func loadPeopleData() async throws -> [PersonEntity] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<PersonEntity> = PersonEntity.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(keyPath: \PersonEntity.name, ascending: true)]
                
                do {
                    let people = try self.context.fetch(request)
                    continuation.resume(returning: people)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Network Building Methods
    
    private func buildNetworkNodes(from people: [PersonEntity]) async -> [NetworkNode] {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let nodes = people.compactMap { person -> NetworkNode? in
                    guard let name = person.name, let id = person.id else { return nil }
                    
                    // Calculate relationship strength based on interactions and data
                    let strength = self.calculateRelationshipStrength(for: person)
                    
                    var node = NetworkNode(
                        personId: id,
                        name: name,
                        nodeType: .person,
                        strength: strength
                    )
                    
                    // Set position based on current layout
                    node.position = self.generateLayoutPosition(
                        for: node,
                        layout: self.currentLayout,
                        index: people.firstIndex(of: person) ?? 0,
                        total: people.count
                    )
                    
                    return node
                }
                
                continuation.resume(returning: nodes)
            }
        }
    }
    
    private func analyzeConnections(between nodes: [NetworkNode]) async -> [NetworkConnection] {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                var connections: [NetworkConnection] = []
                
                // Analyze all possible pairs of nodes
                for i in 0..<nodes.count {
                    for j in (i+1)..<nodes.count {
                        let nodeA = nodes[i]
                        let nodeB = nodes[j]
                        
                        // Calculate connection strength and type
                        if let connection = self.analyzeConnection(between: nodeA, and: nodeB) {
                            connections.append(connection)
                        }
                    }
                }
                
                continuation.resume(returning: connections)
            }
        }
    }
    
    private func analyzeConnection(between nodeA: NetworkNode, and nodeB: NetworkNode) -> NetworkConnection? {
        // Analyze shared teams, interactions, and timeline entries
        let sharedTeams = getSharedTeams(for: nodeA.personId, and: nodeB.personId)
        let interactionCount = getInteractionCount(between: nodeA.personId, and: nodeB.personId)
        let lastInteraction = getLastInteraction(between: nodeA.personId, and: nodeB.personId)
        
        // Calculate connection strength (0.0 - 1.0)
        var strength = 0.0
        
        // Shared teams contribute to strength
        strength += Double(sharedTeams.count) * 0.3
        
        // Interaction frequency contributes to strength
        strength += min(Double(interactionCount) / 10.0, 0.5)
        
        // Recent interactions boost strength
        if let lastInteraction = lastInteraction {
            let daysSinceLastInteraction = Date().timeIntervalSince(lastInteraction) / (24 * 60 * 60)
            if daysSinceLastInteraction < 30 {
                strength += 0.2
            }
        }
        
        // Only create connection if strength is above threshold
        guard strength > 0.1 else { return nil }
        
        // Determine connection type based on context
        let connectionType = determineConnectionType(
            sharedTeams: sharedTeams,
            interactionCount: interactionCount
        )
        
        return NetworkConnection(
            fromNode: nodeA,
            toNode: nodeB,
            connectionType: connectionType,
            strength: min(strength, 1.0),
            interactionCount: interactionCount,
            lastInteraction: lastInteraction
        )
    }
    
    // MARK: - Layout Generation Methods
    
    private func generateLayoutPosition(for node: NetworkNode, layout: NetworkLayout, index: Int, total: Int) -> CGPoint {
        switch layout {
        case .force:
            return generateForceLayoutPosition(for: node, index: index, total: total)
        case .circular:
            return generateCircularLayoutPosition(index: index, total: total)
        case .hierarchical:
            return generateHierarchicalLayoutPosition(for: node, index: index, total: total)
        case .grid:
            return generateGridLayoutPosition(index: index, total: total)
        case .cluster:
            return generateClusterLayoutPosition(for: node, index: index, total: total)
        }
    }
    
    private func generateForceLayoutPosition(for node: NetworkNode, index: Int, total: Int) -> CGPoint {
        // Simple force-directed layout simulation
        let angle = Double(index) * 2.0 * .pi / Double(total)
        let radius = 0.3 + (node.strength * 0.2)
        
        return CGPoint(
            x: 0.5 + radius * cos(angle),
            y: 0.5 + radius * sin(angle)
        )
    }
    
    private func generateCircularLayoutPosition(index: Int, total: Int) -> CGPoint {
        let angle = Double(index) * 2.0 * .pi / Double(total)
        let radius = 0.35
        
        return CGPoint(
            x: 0.5 + radius * cos(angle),
            y: 0.5 + radius * sin(angle)
        )
    }
    
    private func generateHierarchicalLayoutPosition(for node: NetworkNode, index: Int, total: Int) -> CGPoint {
        // Arrange by relationship strength (hierarchy)
        let level = Int(node.strength * 3) // 0-3 levels
        let levelY = 0.2 + (Double(level) * 0.2)
        let levelWidth = 0.8
        let nodesInLevel = max(1, total / 4)
        let levelX = 0.1 + (Double(index % nodesInLevel) * levelWidth / Double(nodesInLevel))
        
        return CGPoint(x: levelX, y: levelY)
    }
    
    private func generateGridLayoutPosition(index: Int, total: Int) -> CGPoint {
        let columns = Int(ceil(sqrt(Double(total))))
        let row = index / columns
        let col = index % columns
        
        return CGPoint(
            x: 0.1 + (Double(col) * 0.8 / Double(columns - 1)),
            y: 0.1 + (Double(row) * 0.8 / Double(columns - 1))
        )
    }
    
    private func generateClusterLayoutPosition(for node: NetworkNode, index: Int, total: Int) -> CGPoint {
        // Group by connection strength
        let cluster = Int(node.strength * 2) // 0-2 clusters
        let clusterCenters = [
            CGPoint(x: 0.25, y: 0.3),
            CGPoint(x: 0.75, y: 0.3),
            CGPoint(x: 0.5, y: 0.7)
        ]
        
        let center = clusterCenters[min(cluster, clusterCenters.count - 1)]
        let offset = Double.random(in: -0.15...0.15)
        
        return CGPoint(
            x: center.x + offset,
            y: center.y + offset
        )
    }
    
    // MARK: - Helper Methods
    
    private func updateVisibleElements() {
        // Apply time range filter
        if let dateRange = currentTimeRange.dateRange {
            visibleConnections = allConnections.filter { connection in
                guard let lastInteraction = connection.lastInteraction else { return false }
                return dateRange.contains(lastInteraction)
            }
        } else {
            visibleConnections = allConnections
        }
        
        // Update visible nodes based on connections
        let connectedNodeIds = Set(visibleConnections.flatMap { [$0.fromNode.id, $0.toNode.id] })
        visibleNodes = allNodes.filter { connectedNodeIds.contains($0.id) || highlightedNodeIds.contains($0.id) }
    }
    
    private func updateNetworkStatistics() {
        networkStatistics = NetworkStatistics(
            totalNodes: visibleNodes.count,
            totalConnections: visibleConnections.count,
            averageConnectionStrength: visibleConnections.isEmpty ? 0.0 : visibleConnections.map(\.strength).reduce(0, +) / Double(visibleConnections.count),
            networkDensity: networkDensity,
            clusteringCoefficient: calculateClusteringCoefficient(),
            strongConnectionsCount: strongConnections,
            weakConnectionsCount: visibleConnections.filter { $0.strength <= 0.3 }.count,
            isolatedNodesCount: allNodes.count - visibleNodes.count,
            mostConnectedNode: findMostConnectedNode(),
            strongestConnection: visibleConnections.max(by: { $0.strength < $1.strength }),
            networkHealth: calculateNetworkHealth()
        )
    }
    
    private func calculateRelationshipStrength(for person: PersonEntity) -> Double {
        // Implementation would analyze interaction history, team memberships, etc.
        return Double.random(in: 0.3...0.9) // Placeholder
    }
    
    private func getSharedTeams(for personA: UUID, and personB: UUID) -> [UUID] {
        // Implementation would query Core Data for shared teams
        return [] // Placeholder
    }
    
    private func getInteractionCount(between personA: UUID, and personB: UUID) -> Int {
        // Implementation would count interactions from timeline entries
        return Int.random(in: 0...20) // Placeholder
    }
    
    private func getLastInteraction(between personA: UUID, and personB: UUID) -> Date? {
        // Implementation would find most recent interaction
        return Date().addingTimeInterval(-Double.random(in: 0...30) * 24 * 60 * 60) // Placeholder
    }
    
    private func determineConnectionType(sharedTeams: [UUID], interactionCount: Int) -> NetworkConnection.ConnectionType {
        if !sharedTeams.isEmpty {
            return .collaboration
        } else if interactionCount > 10 {
            return .friendship
        } else {
            return .professional
        }
    }
    
    private func calculateClusteringCoefficient() -> Double {
        // Implementation for clustering coefficient calculation
        return 0.6 // Placeholder
    }
    
    private func findMostConnectedNode() -> NetworkNode? {
        let connectionCounts = visibleNodes.map { node in
            (node, visibleConnections.filter { $0.fromNode.id == node.id || $0.toNode.id == node.id }.count)
        }
        return connectionCounts.max(by: { $0.1 < $1.1 })?.0
    }
    
    private func calculateNetworkHealth() -> NetworkStatistics.NetworkHealth {
        let density = networkDensity
        let avgStrength = networkStatistics.averageConnectionStrength
        
        if density > 0.6 && avgStrength > 0.7 {
            return .excellent
        } else if density > 0.4 && avgStrength > 0.5 {
            return .good
        } else if density > 0.2 && avgStrength > 0.3 {
            return .fair
        } else {
            return .poor
        }
    }
    
    // MARK: - Insights Generation
    
    private func generateNetworkInsights() async -> [NetworkInsight] {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                var insights: [NetworkInsight] = []
                
                // Network health insights
                if self.networkDensity < 0.3 {
                    insights.append(NetworkInsight(
                        title: "Sparse Network Detected",
                        description: "Your network could benefit from more connections between people you know.",
                        priority: .medium,
                        category: .networkHealth,
                        actionable: true
                    ))
                }
                
                // Strong connections insights
                if self.strongConnections > 5 {
                    insights.append(NetworkInsight(
                        title: "Strong Network Foundation",
                        description: "You have \(self.strongConnections) strong relationships that form a solid foundation.",
                        priority: .low,
                        category: .strongConnections
                    ))
                }
                
                // Maintenance needed insights
                let staleConnections = self.visibleConnections.filter { connection in
                    guard let lastInteraction = connection.lastInteraction else { return true }
                    return Date().timeIntervalSince(lastInteraction) > 30 * 24 * 60 * 60 // 30 days
                }
                
                if staleConnections.count > 3 {
                    insights.append(NetworkInsight(
                        title: "Relationships Need Attention",
                        description: "\(staleConnections.count) relationships haven't had recent interactions.",
                        priority: .high,
                        category: .maintenanceNeeded,
                        actionable: true
                    ))
                }
                
                continuation.resume(returning: insights)
            }
        }
    }
}
