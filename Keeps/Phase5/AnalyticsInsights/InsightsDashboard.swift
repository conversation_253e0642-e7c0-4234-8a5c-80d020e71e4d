//
//  InsightsDashboard.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI
import Charts

/// Comprehensive analytics dashboard showing insights across People, Teams, and Timeline
/// Features interactive charts, trend analysis, and actionable recommendations
struct InsightsDashboard: View {
    
    // MARK: - Properties
    
    @StateObject private var analyticsEngine = AnalyticsEngine()
    @StateObject private var chartDataManager = ChartDataManager()
    
    @State private var selectedTimeRange: AnalyticsTimeRange = .month
    @State private var selectedCategory: AnalyticsCategory = .overview
    @State private var showingDetailView = false
    @State private var selectedInsight: AnalyticsInsight?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header with time range selector
                    dashboardHeader
                    
                    // Key metrics overview
                    keyMetricsSection
                    
                    // Category tabs
                    categoryTabsSection
                    
                    // Main content based on selected category
                    mainContentSection
                    
                    // Insights and recommendations
                    insightsSection
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 20)
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Export Data") { exportAnalyticsData() }
                        Button("Share Insights") { shareInsights() }
                        Button("Settings") { /* Open settings */ }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .sheet(isPresented: $showingDetailView) {
                if let insight = selectedInsight {
                    // Placeholder for InsightDetailView - will be implemented in next phase
                    VStack {
                        Text("Insight Details")
                            .font(.title)
                        Text(insight.title)
                            .font(.headline)
                        Text(insight.description)
                            .padding()
                        Button("Close") {
                            showingDetailView = false
                        }
                    }
                    .padding()
                }
            }
            .onAppear {
                loadAnalyticsData()
            }
            .onChange(of: selectedTimeRange) { _ in
                updateAnalyticsData()
            }
        }
    }
    
    // MARK: - Dashboard Header
    
    private var dashboardHeader: some View {
        VStack(spacing: 16) {
            // Time Range Selector
            Picker("Time Range", selection: $selectedTimeRange) {
                ForEach(AnalyticsTimeRange.allCases, id: \.self) { range in
                    Text(range.displayName).tag(range)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            
            // Last Updated Info
            HStack {
                Image(systemName: "clock")
                    .foregroundColor(.secondary)
                Text("Last updated: \(analyticsEngine.lastUpdateTime, style: .time)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Button("Refresh") {
                    refreshAnalytics()
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    // MARK: - Key Metrics Section
    
    private var keyMetricsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Key Metrics")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                MetricCard(
                    title: "Network Health",
                    value: analyticsEngine.networkHealthScore,
                    format: .percentage,
                    trend: analyticsEngine.networkHealthTrend,
                    color: .green,
                    icon: "heart.text.square"
                )
                
                MetricCard(
                    title: "Active Relationships",
                    value: Double(analyticsEngine.activeRelationshipsCount),
                    format: .number,
                    trend: analyticsEngine.relationshipsTrend,
                    color: .blue,
                    icon: "person.2.fill"
                )
                
                MetricCard(
                    title: "Team Productivity",
                    value: analyticsEngine.teamProductivityScore,
                    format: .percentage,
                    trend: analyticsEngine.productivityTrend,
                    color: .orange,
                    icon: "chart.line.uptrend.xyaxis"
                )
                
                MetricCard(
                    title: "Goal Completion",
                    value: analyticsEngine.goalCompletionRate,
                    format: .percentage,
                    trend: analyticsEngine.goalsTrend,
                    color: .purple,
                    icon: "target"
                )
            }
        }
    }
    
    // MARK: - Category Tabs Section
    
    private var categoryTabsSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(AnalyticsCategory.allCases, id: \.self) { category in
                    CategoryTab(
                        category: category,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    // MARK: - Main Content Section
    
    private var mainContentSection: some View {
        Group {
            switch selectedCategory {
            case .overview:
                overviewContent
            case .relationships:
                relationshipsContent
            case .teams:
                teamsContent
            case .timeline:
                timelineContent
            case .growth:
                growthContent
            }
        }
    }
    
    // MARK: - Overview Content
    
    private var overviewContent: some View {
        VStack(spacing: 20) {
            // Network Overview Chart
            ChartCard(title: "Network Overview", icon: "network") {
                Chart(chartDataManager.networkOverviewData) { dataPoint in
                    LineMark(
                        x: .value("Date", dataPoint.date),
                        y: .value("Value", dataPoint.value)
                    )
                    .foregroundStyle(dataPoint.color)
                    .interpolationMethod(.catmullRom)
                }
                .frame(height: 200)
                .chartYAxis {
                    AxisMarks(position: .leading)
                }
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day, count: 7)) { _ in
                        AxisGridLine()
                        AxisTick()
                        AxisValueLabel(format: .dateTime.month().day())
                    }
                }
            }
            
            // Activity Heatmap
            ChartCard(title: "Activity Heatmap", icon: "calendar") {
                // Placeholder for ActivityHeatmapView - will be implemented in next phase
                Rectangle()
                    .fill(Color.blue.opacity(0.1))
                    .frame(height: 150)
                    .overlay(
                        Text("Activity Heatmap\nComing Soon")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    )
            }
        }
    }
    
    // MARK: - Relationships Content
    
    private var relationshipsContent: some View {
        VStack(spacing: 20) {
            // Relationship Strength Distribution
            ChartCard(title: "Relationship Strength", icon: "heart") {
                Chart(chartDataManager.relationshipStrengthData) { dataPoint in
                    BarMark(
                        x: .value("Strength", dataPoint.category),
                        y: .value("Count", dataPoint.value)
                    )
                    .foregroundStyle(dataPoint.color)
                }
                .frame(height: 200)
            }
            
            // Interaction Frequency
            ChartCard(title: "Interaction Frequency", icon: "message") {
                Chart(chartDataManager.interactionFrequencyData) { dataPoint in
                    LineMark(
                        x: .value("Date", dataPoint.date),
                        y: .value("Interactions", dataPoint.value)
                    )
                    .foregroundStyle(.blue)
                }
                .frame(height: 150)
            }
        }
    }
    
    // MARK: - Teams Content
    
    private var teamsContent: some View {
        VStack(spacing: 20) {
            // Team Performance
            ChartCard(title: "Team Performance", icon: "person.3") {
                Chart(chartDataManager.teamPerformanceData) { dataPoint in
                    BarMark(
                        x: .value("Team", dataPoint.category),
                        y: .value("Performance", dataPoint.value)
                    )
                    .foregroundStyle(dataPoint.color)
                }
                .frame(height: 200)
            }
            
            // Collaboration Network
            ChartCard(title: "Collaboration Network", icon: "link") {
                // Placeholder for CollaborationNetworkView - will be implemented in next phase
                Rectangle()
                    .fill(Color.green.opacity(0.1))
                    .frame(height: 200)
                    .overlay(
                        Text("Collaboration Network\nComing Soon")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    )
            }
        }
    }
    
    // MARK: - Timeline Content
    
    private var timelineContent: some View {
        VStack(spacing: 20) {
            // Achievement Trends
            ChartCard(title: "Achievement Trends", icon: "trophy") {
                Chart(chartDataManager.achievementTrendsData) { dataPoint in
                    AreaMark(
                        x: .value("Date", dataPoint.date),
                        y: .value("Achievements", dataPoint.value)
                    )
                    .foregroundStyle(.green.opacity(0.3))
                    .interpolationMethod(.catmullRom)
                }
                .frame(height: 200)
            }
            
            // Goal Progress
            ChartCard(title: "Goal Progress", icon: "target") {
                // Placeholder for GoalProgressView - will be implemented in next phase
                Rectangle()
                    .fill(Color.purple.opacity(0.1))
                    .frame(height: 150)
                    .overlay(
                        Text("Goal Progress\nComing Soon")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    )
            }
        }
    }
    
    // MARK: - Growth Content
    
    private var growthContent: some View {
        VStack(spacing: 20) {
            // Personal Growth Metrics
            ChartCard(title: "Personal Growth", icon: "chart.line.uptrend.xyaxis") {
                Chart(chartDataManager.personalGrowthData) { dataPoint in
                    LineMark(
                        x: .value("Date", dataPoint.date),
                        y: .value("Growth", dataPoint.value)
                    )
                    .foregroundStyle(.purple)
                    .interpolationMethod(.catmullRom)
                }
                .frame(height: 200)
            }
            
            // Skill Development
            ChartCard(title: "Skill Development", icon: "brain.head.profile") {
                // Placeholder for SkillDevelopmentView - will be implemented in next phase
                Rectangle()
                    .fill(Color.orange.opacity(0.1))
                    .frame(height: 150)
                    .overlay(
                        Text("Skill Development\nComing Soon")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    )
            }
        }
    }
    
    // MARK: - Insights Section
    
    private var insightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Insights & Recommendations")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(analyticsEngine.topInsights, id: \.id) { insight in
                AnalyticsInsightCard(insight: insight) {
                    selectedInsight = insight
                    showingDetailView = true
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func loadAnalyticsData() {
        Task {
            await analyticsEngine.loadAnalyticsData(for: selectedTimeRange)
            await chartDataManager.loadChartData(for: selectedTimeRange)
        }
    }
    
    private func updateAnalyticsData() {
        Task {
            await analyticsEngine.updateData(for: selectedTimeRange)
            await chartDataManager.updateData(for: selectedTimeRange)
        }
    }
    
    private func refreshAnalytics() {
        Task {
            await analyticsEngine.refreshData()
            await chartDataManager.refreshData()
        }
    }
    
    private func exportAnalyticsData() {
        // Implementation for exporting analytics data
        print("Exporting analytics data...")
    }
    
    private func shareInsights() {
        // Implementation for sharing insights
        print("Sharing insights...")
    }
}

// MARK: - Supporting Views

struct MetricCard: View {
    let title: String
    let value: Double
    let format: MetricFormat
    let trend: TrendDirection
    let color: Color
    let icon: String
    
    enum MetricFormat {
        case number
        case percentage
        case currency
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title3)
                
                Spacer()
                
                TrendIndicator(direction: trend)
            }
            
            Text(formattedValue)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    private var formattedValue: String {
        switch format {
        case .number:
            return String(format: "%.0f", value)
        case .percentage:
            return String(format: "%.1f%%", value * 100)
        case .currency:
            return String(format: "$%.2f", value)
        }
    }
}

struct CategoryTab: View {
    let category: AnalyticsCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: category.icon)
                    .font(.caption)
                
                Text(category.displayName)
                    .font(.caption)
                    .fontWeight(isSelected ? .semibold : .regular)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? Color.blue : Color(.systemGray5))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ChartCard<Content: View>: View {
    let title: String
    let icon: String
    let content: Content
    
    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            content
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

struct TrendIndicator: View {
    let direction: TrendDirection
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: direction.icon)
                .font(.caption2)
                .foregroundColor(direction.color)
            
            Text(direction.displayText)
                .font(.caption2)
                .foregroundColor(direction.color)
        }
    }
}

struct AnalyticsInsightCard: View {
    let insight: AnalyticsInsight
    let action: () -> Void

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: insight.icon)
                .font(.title3)
                .foregroundColor(insight.priority.color)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(insight.title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(insight.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            if insight.actionable {
                Button("View") {
                    action()
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(insight.priority.color)
                .foregroundColor(.white)
                .clipShape(Capsule())
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(insight.priority.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(insight.priority.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Preview

struct InsightsDashboard_Previews: PreviewProvider {
    static var previews: some View {
        InsightsDashboard()
    }
}
