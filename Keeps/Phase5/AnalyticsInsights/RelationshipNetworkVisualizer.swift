//
//  RelationshipNetworkVisualizer.swift
//  Keeps
//
//  Created by <PERSON>M<PERSON> on Phase 5 Implementation
//

import SwiftUI
import CoreData
import Combine

/// Revolutionary relationship network visualization with interactive 3D-style network graph
/// Displays relationship connections, strength indicators, and network insights
struct RelationshipNetworkVisualizer: View {
    
    // MARK: - Properties
    
    @StateObject private var networkAnalyzer = RelationshipNetworkAnalyzer()
    @State private var selectedNode: NetworkNode?
    @State private var showingNodeDetail = false
    @State private var networkLayout: NetworkLayout = .force
    @State private var animationProgress: Double = 0.0
    @State private var isAnimating = false
    
    // MARK: - Network Configuration
    
    @State private var showStrengthIndicators = true
    @State private var showConnectionLabels = false
    @State private var filterByStrength: Double = 0.0
    @State private var selectedTimeRange: TimeRange = .all
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        Color(.systemGray6).opacity(0.3)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Network Controls
                    networkControlsSection
                    
                    // Main Network Visualization
                    networkVisualizationSection
                    
                    // Network Insights Panel
                    networkInsightsSection
                }
            }
            .navigationTitle("Relationship Network")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    networkOptionsMenu
                }
            }
            .sheet(isPresented: $showingNodeDetail) {
                if let node = selectedNode {
                    NodeDetailView(node: node)
                }
            }
            .onAppear {
                loadNetworkData()
            }
        }
    }
    
    // MARK: - Network Controls Section
    
    private var networkControlsSection: some View {
        VStack(spacing: 16) {
            // Layout and Filter Controls
            HStack(spacing: 20) {
                // Layout Picker
                Picker("Layout", selection: $networkLayout) {
                    ForEach(NetworkLayout.allCases, id: \.self) { layout in
                        Text(layout.displayName)
                            .tag(layout)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                
                Spacer()
                
                // Animate Button
                Button(action: animateNetwork) {
                    Image(systemName: isAnimating ? "pause.circle.fill" : "play.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }
            
            // Strength Filter
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Relationship Strength Filter")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text("\(Int(filterByStrength * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Slider(value: $filterByStrength, in: 0...1, step: 0.1)
                    .accentColor(.blue)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    // MARK: - Network Visualization Section
    
    private var networkVisualizationSection: some View {
        GeometryReader { geometry in
            ZStack {
                // Network Canvas
                Canvas { context, size in
                    drawNetworkGraph(context: context, size: size)
                }
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(.systemGray6).opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                )
                .clipped()
                
                // Interactive Node Overlay
                ForEach(networkAnalyzer.visibleNodes) { node in
                    NetworkNodeView(
                        node: node,
                        isSelected: selectedNode?.id == node.id,
                        showStrengthIndicator: showStrengthIndicators
                    )
                    .position(
                        x: node.position.x * geometry.size.width,
                        y: node.position.y * geometry.size.height
                    )
                    .onTapGesture {
                        selectNode(node)
                    }
                    .scaleEffect(selectedNode?.id == node.id ? 1.2 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: selectedNode?.id)
                }
            }
        }
        .frame(minHeight: 400)
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    // MARK: - Network Insights Section
    
    private var networkInsightsSection: some View {
        VStack(spacing: 16) {
            // Network Statistics
            HStack(spacing: 20) {
                NetworkStatCard(
                    title: "Total Connections",
                    value: "\(networkAnalyzer.totalConnections)",
                    icon: "link",
                    color: .blue
                )
                
                NetworkStatCard(
                    title: "Strong Bonds",
                    value: "\(networkAnalyzer.strongConnections)",
                    icon: "heart.fill",
                    color: .red
                )
                
                NetworkStatCard(
                    title: "Network Density",
                    value: String(format: "%.1f%%", networkAnalyzer.networkDensity * 100),
                    icon: "network",
                    color: .green
                )
            }
            
            // Key Insights
            if !networkAnalyzer.keyInsights.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Key Insights")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    ForEach(networkAnalyzer.keyInsights, id: \.id) { insight in
                        InsightCard(insight: insight)
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
    }
    
    // MARK: - Network Options Menu
    
    private var networkOptionsMenu: some View {
        Menu {
            Button(action: { showStrengthIndicators.toggle() }) {
                Label(
                    showStrengthIndicators ? "Hide Strength Indicators" : "Show Strength Indicators",
                    systemImage: showStrengthIndicators ? "eye.slash" : "eye"
                )
            }
            
            Button(action: { showConnectionLabels.toggle() }) {
                Label(
                    showConnectionLabels ? "Hide Connection Labels" : "Show Connection Labels",
                    systemImage: showConnectionLabels ? "textformat.size" : "textformat"
                )
            }
            
            Divider()
            
            Menu("Time Range") {
                ForEach(TimeRange.allCases, id: \.self) { range in
                    Button(range.displayName) {
                        selectedTimeRange = range
                        networkAnalyzer.updateTimeRange(range)
                    }
                }
            }
            
            Divider()
            
            Button("Export Network Data") {
                exportNetworkData()
            }
            
            Button("Reset Layout") {
                resetNetworkLayout()
            }
        } label: {
            Image(systemName: "ellipsis.circle")
                .font(.title2)
        }
    }
    
    // MARK: - Helper Methods
    
    private func loadNetworkData() {
        Task {
            await networkAnalyzer.analyzeRelationshipNetwork()
        }
    }
    
    private func selectNode(_ node: NetworkNode) {
        selectedNode = node
        showingNodeDetail = true
        
        // Highlight connected nodes
        networkAnalyzer.highlightConnections(for: node)
    }
    
    private func animateNetwork() {
        isAnimating.toggle()
        
        if isAnimating {
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                animationProgress = 1.0
            }
        } else {
            withAnimation(.easeOut(duration: 0.5)) {
                animationProgress = 0.0
            }
        }
    }
    
    private func drawNetworkGraph(context: GraphicsContext, size: CGSize) {
        // Draw connections between nodes
        for connection in networkAnalyzer.visibleConnections {
            if connection.strength >= filterByStrength {
                drawConnection(context: context, connection: connection, size: size)
            }
        }
    }
    
    private func drawConnection(context: GraphicsContext, connection: NetworkConnection, size: CGSize) {
        let startPoint = CGPoint(
            x: connection.fromNode.position.x * size.width,
            y: connection.fromNode.position.y * size.height
        )
        
        let endPoint = CGPoint(
            x: connection.toNode.position.x * size.width,
            y: connection.toNode.position.y * size.height
        )
        
        // Connection line with strength-based styling
        let lineWidth = 1.0 + (connection.strength * 3.0)
        let opacity = 0.3 + (connection.strength * 0.7)
        
        context.stroke(
            Path { path in
                path.move(to: startPoint)
                path.addLine(to: endPoint)
            },
            with: .color(connection.color.opacity(opacity)),
            lineWidth: lineWidth
        )
        
        // Connection label (if enabled)
        if showConnectionLabels {
            let midPoint = CGPoint(
                x: (startPoint.x + endPoint.x) / 2,
                y: (startPoint.y + endPoint.y) / 2
            )
            
            context.draw(
                Text(connection.label)
                    .font(.caption2)
                    .foregroundColor(.secondary),
                at: midPoint
            )
        }
    }
    
    private func exportNetworkData() {
        // Implementation for exporting network data
        print("Exporting network data...")
    }
    
    private func resetNetworkLayout() {
        withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
            networkAnalyzer.resetLayout()
        }
    }
}

// MARK: - Supporting Views

struct NetworkNodeView: View {
    let node: NetworkNode
    let isSelected: Bool
    let showStrengthIndicator: Bool
    
    var body: some View {
        ZStack {
            // Node circle
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            node.color.opacity(0.8),
                            node.color.opacity(0.4)
                        ],
                        center: .topLeading,
                        startRadius: 5,
                        endRadius: 25
                    )
                )
                .frame(width: node.size, height: node.size)
                .overlay(
                    Circle()
                        .stroke(
                            isSelected ? Color.blue : Color.white,
                            lineWidth: isSelected ? 3 : 2
                        )
                )
            
            // Node icon or initials
            if let icon = node.icon {
                Image(systemName: icon)
                    .font(.system(size: node.size * 0.4, weight: .medium))
                    .foregroundColor(.white)
            } else {
                Text(node.initials)
                    .font(.system(size: node.size * 0.3, weight: .semibold))
                    .foregroundColor(.white)
            }
            
            // Strength indicator
            if showStrengthIndicator && node.strength > 0.7 {
                Circle()
                    .fill(Color.yellow)
                    .frame(width: 8, height: 8)
                    .offset(x: node.size * 0.3, y: -node.size * 0.3)
            }
        }
    }
}

struct NetworkStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

struct InsightCard: View {
    let insight: NetworkInsight
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: insight.icon)
                .font(.title3)
                .foregroundColor(insight.priority.color)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(insight.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(insight.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if insight.actionable {
                Button("Act") {
                    // Handle insight action
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue)
                .foregroundColor(.white)
                .clipShape(Capsule())
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(insight.priority.backgroundColor)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(insight.priority.borderColor, lineWidth: 1)
                )
        )
    }
}

// MARK: - Preview

struct RelationshipNetworkVisualizer_Previews: PreviewProvider {
    static var previews: some View {
        RelationshipNetworkVisualizer()
    }
}
