//
//  AnalyticsEngine.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI
import CoreData
import Combine

/// Advanced analytics engine that processes data across People, Teams, and Timeline
/// Generates insights, trends, and recommendations using AI-powered analysis
class AnalyticsEngine: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var networkHealthScore: Double = 0.0
    @Published var activeRelationshipsCount: Int = 0
    @Published var teamProductivityScore: Double = 0.0
    @Published var goalCompletionRate: Double = 0.0
    @Published var personalGrowthScore: Double = 0.0
    
    @Published var networkHealthTrend: TrendDirection = .stable
    @Published var relationshipsTrend: TrendDirection = .stable
    @Published var productivityTrend: TrendDirection = .stable
    @Published var goalsTrend: TrendDirection = .stable
    @Published var growthTrend: TrendDirection = .stable
    
    @Published var topInsights: [AnalyticsInsight] = []
    @Published var performanceMetrics = PerformanceMetrics(
        networkHealth: 0.0,
        relationshipStrength: 0.0,
        teamProductivity: 0.0,
        goalCompletion: 0.0,
        personalGrowth: 0.0,
        collaborationIndex: 0.0,
        engagementLevel: 0.0,
        achievementRate: 0.0
    )
    
    @Published var isAnalyzing = false
    @Published var lastUpdateTime = Date()
    @Published var analysisProgress: Double = 0.0
    
    // MARK: - Private Properties
    
    private let context: NSManagedObjectContext
    private var cancellables = Set<AnyCancellable>()
    private var currentTimeRange: AnalyticsTimeRange = .month
    
    // Data caches
    private var cachedPeople: [PersonEntity] = []
    private var cachedTeams: [TeamEntity] = []
    private var cachedTimelineEntries: [TimelineEntryEntity] = []
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext = PersistenceController.shared.container.viewContext) {
        self.context = context
        setupAnalyticsTimer()
    }
    
    // MARK: - Main Analytics Methods
    
    /// Load comprehensive analytics data for the specified time range
    @MainActor
    func loadAnalyticsData(for timeRange: AnalyticsTimeRange) async {
        guard !isAnalyzing else { return }
        
        isAnalyzing = true
        analysisProgress = 0.0
        currentTimeRange = timeRange
        
        do {
            // Step 1: Load core data (20%)
            analysisProgress = 0.2
            try await loadCoreData()
            
            // Step 2: Calculate network metrics (40%)
            analysisProgress = 0.4
            await calculateNetworkMetrics()
            
            // Step 3: Analyze team performance (60%)
            analysisProgress = 0.6
            await analyzeTeamPerformance()
            
            // Step 4: Process timeline data (80%)
            analysisProgress = 0.8
            await processTimelineData()
            
            // Step 5: Generate insights (100%)
            analysisProgress = 1.0
            await generateInsights()
            
            lastUpdateTime = Date()
            
        } catch {
            print("Error loading analytics data: \(error)")
        }
        
        isAnalyzing = false
    }
    
    /// Update analytics data for new time range
    func updateData(for timeRange: AnalyticsTimeRange) async {
        currentTimeRange = timeRange
        await loadAnalyticsData(for: timeRange)
    }
    
    /// Refresh all analytics data
    func refreshData() async {
        await loadAnalyticsData(for: currentTimeRange)
    }
    
    // MARK: - Core Data Loading
    
    private func loadCoreData() async throws {
        try await withThrowingTaskGroup(of: Void.self) { group in
            group.addTask {
                self.cachedPeople = try await self.loadPeople()
            }
            
            group.addTask {
                self.cachedTeams = try await self.loadTeams()
            }
            
            group.addTask {
                self.cachedTimelineEntries = try await self.loadTimelineEntries()
            }
            
            try await group.waitForAll()
        }
    }
    
    private func loadPeople() async throws -> [PersonEntity] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<PersonEntity> = PersonEntity.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(keyPath: \PersonEntity.name, ascending: true)]
                
                do {
                    let people = try self.context.fetch(request)
                    continuation.resume(returning: people)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func loadTeams() async throws -> [TeamEntity] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<TeamEntity> = TeamEntity.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(keyPath: \TeamEntity.name, ascending: true)]
                
                do {
                    let teams = try self.context.fetch(request)
                    continuation.resume(returning: teams)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func loadTimelineEntries() async throws -> [TimelineEntryEntity] {
        return try await withCheckedThrowingContinuation { continuation in
            context.perform {
                let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(keyPath: \TimelineEntryEntity.weekNumber, ascending: false)]
                
                // Apply time range filter if needed
                if let dateRange = self.currentTimeRange.dateRange {
                    request.predicate = NSPredicate(format: "createdAt >= %@ AND createdAt <= %@", dateRange.start as NSDate, dateRange.end as NSDate)
                }
                
                do {
                    let entries = try self.context.fetch(request)
                    continuation.resume(returning: entries)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    // MARK: - Network Metrics Calculation
    
    private func calculateNetworkMetrics() async {
        await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                // Calculate network health score
                let totalPeople = self.cachedPeople.count
                let activePeople = self.cachedPeople.filter { person in
                    // Check if person has recent interactions
                    return self.hasRecentInteractions(person: person)
                }.count
                
                self.activeRelationshipsCount = activePeople
                self.networkHealthScore = totalPeople > 0 ? Double(activePeople) / Double(totalPeople) : 0.0
                
                // Calculate relationship strength
                let relationshipStrengths = self.cachedPeople.compactMap { person in
                    self.calculateRelationshipStrength(for: person)
                }
                
                let averageStrength = relationshipStrengths.isEmpty ? 0.0 : relationshipStrengths.reduce(0, +) / Double(relationshipStrengths.count)
                
                // Update trends
                DispatchQueue.main.async {
                    self.networkHealthTrend = self.calculateTrend(current: self.networkHealthScore, previous: 0.7) // Placeholder previous value
                    self.relationshipsTrend = self.calculateTrend(current: Double(self.activeRelationshipsCount), previous: Double(totalPeople - 2))
                    
                    self.performanceMetrics = PerformanceMetrics(
                        networkHealth: self.networkHealthScore,
                        relationshipStrength: averageStrength,
                        teamProductivity: self.teamProductivityScore,
                        goalCompletion: self.goalCompletionRate,
                        personalGrowth: self.personalGrowthScore,
                        collaborationIndex: 0.8, // Placeholder
                        engagementLevel: 0.7, // Placeholder
                        achievementRate: 0.6 // Placeholder
                    )
                }
                
                continuation.resume()
            }
        }
    }
    
    // MARK: - Team Performance Analysis
    
    private func analyzeTeamPerformance() async {
        await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let activeTeams = self.cachedTeams.filter { team in
                    return team.isActive
                }
                
                var totalProductivity = 0.0
                var teamCount = 0
                
                for team in activeTeams {
                    let productivity = self.calculateTeamProductivity(for: team)
                    totalProductivity += productivity
                    teamCount += 1
                }
                
                let averageProductivity = teamCount > 0 ? totalProductivity / Double(teamCount) : 0.0
                
                DispatchQueue.main.async {
                    self.teamProductivityScore = averageProductivity
                    self.productivityTrend = self.calculateTrend(current: averageProductivity, previous: 0.6) // Placeholder
                }
                
                continuation.resume()
            }
        }
    }
    
    // MARK: - Timeline Data Processing
    
    private func processTimelineData() async {
        await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                // Calculate goal completion rate
                let totalEntries = self.cachedTimelineEntries.count
                let entriesWithGoals = self.cachedTimelineEntries.filter { entry in
                    return entry.accomplishments?.count ?? 0 > 0
                }.count
                
                let completionRate = totalEntries > 0 ? Double(entriesWithGoals) / Double(totalEntries) : 0.0
                
                // Calculate personal growth score
                let growthScore = self.calculatePersonalGrowthScore()
                
                DispatchQueue.main.async {
                    self.goalCompletionRate = completionRate
                    self.personalGrowthScore = growthScore
                    self.goalsTrend = self.calculateTrend(current: completionRate, previous: 0.5) // Placeholder
                    self.growthTrend = self.calculateTrend(current: growthScore, previous: 0.4) // Placeholder
                }
                
                continuation.resume()
            }
        }
    }
    
    // MARK: - Insights Generation
    
    private func generateInsights() async {
        await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                var insights: [AnalyticsInsight] = []
                
                // Network health insights
                if self.networkHealthScore < 0.5 {
                    insights.append(AnalyticsInsight(
                        title: "Network Health Needs Attention",
                        description: "Only \(Int(self.networkHealthScore * 100))% of your relationships are active. Consider reaching out to dormant connections.",
                        category: .relationships,
                        priority: .high,
                        actionable: true,
                        recommendations: [
                            "Schedule coffee chats with 3 people you haven't spoken to recently",
                            "Send a quick check-in message to old colleagues",
                            "Attend networking events to meet new people"
                        ]
                    ))
                }
                
                // Team productivity insights
                if self.teamProductivityScore > 0.8 {
                    insights.append(AnalyticsInsight(
                        title: "Excellent Team Performance",
                        description: "Your teams are performing exceptionally well with \(Int(self.teamProductivityScore * 100))% productivity.",
                        category: .teams,
                        priority: .low,
                        recommendations: [
                            "Document successful practices for future reference",
                            "Consider mentoring other teams",
                            "Celebrate team achievements"
                        ]
                    ))
                } else if self.teamProductivityScore < 0.5 {
                    insights.append(AnalyticsInsight(
                        title: "Team Productivity Below Average",
                        description: "Team productivity is at \(Int(self.teamProductivityScore * 100))%. Consider reviewing team dynamics and processes.",
                        category: .teams,
                        priority: .medium,
                        actionable: true,
                        recommendations: [
                            "Schedule team retrospectives",
                            "Review and optimize team processes",
                            "Provide additional support or resources"
                        ]
                    ))
                }
                
                // Goal completion insights
                if self.goalCompletionRate < 0.3 {
                    insights.append(AnalyticsInsight(
                        title: "Goal Completion Rate Low",
                        description: "Only \(Int(self.goalCompletionRate * 100))% of your timeline entries include accomplishments.",
                        category: .timeline,
                        priority: .medium,
                        actionable: true,
                        recommendations: [
                            "Set smaller, more achievable goals",
                            "Track progress more frequently",
                            "Break large goals into smaller milestones"
                        ]
                    ))
                }
                
                // Personal growth insights
                if self.personalGrowthScore > 0.7 {
                    insights.append(AnalyticsInsight(
                        title: "Strong Personal Growth",
                        description: "You're showing excellent personal development with consistent progress.",
                        category: .growth,
                        priority: .low,
                        recommendations: [
                            "Continue current learning practices",
                            "Share knowledge with others",
                            "Set more challenging goals"
                        ]
                    ))
                }
                
                DispatchQueue.main.async {
                    self.topInsights = insights.sorted { $0.priority.rawValue > $1.priority.rawValue }
                }
                
                continuation.resume()
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func setupAnalyticsTimer() {
        // Refresh analytics every hour
        Timer.publish(every: 3600, on: .main, in: .common)
            .autoconnect()
            .sink { _ in
                Task {
                    await self.refreshData()
                }
            }
            .store(in: &cancellables)
    }
    
    private func hasRecentInteractions(person: PersonEntity) -> Bool {
        // Check if person has interactions within the current time range
        guard currentTimeRange.dateRange != nil else { return true }
        
        // In a real implementation, this would check interaction history
        // For now, return a random value for demonstration
        return Bool.random()
    }
    
    private func calculateRelationshipStrength(for person: PersonEntity) -> Double {
        // Calculate relationship strength based on interaction frequency, recency, and quality
        // This is a simplified calculation - real implementation would be more sophisticated
        let baseStrength = 0.5
        let interactionBonus = Double.random(in: 0...0.3)
        let recencyBonus = Double.random(in: 0...0.2)
        
        return min(baseStrength + interactionBonus + recencyBonus, 1.0)
    }
    
    private func calculateTeamProductivity(for team: TeamEntity) -> Double {
        // Calculate team productivity based on goals achieved, collaboration frequency, etc.
        // This is a simplified calculation
        return Double.random(in: 0.3...0.9)
    }
    
    private func calculatePersonalGrowthScore() -> Double {
        // Calculate personal growth based on timeline entries, skill development, etc.
        let recentEntries = cachedTimelineEntries.prefix(10)
        let entriesWithReflections = recentEntries.filter { entry in
            return entry.insight?.contains("reflection") == true || entry.insight?.contains("learned") == true || entry.insight?.contains("growth") == true
        }
        
        return recentEntries.count > 0 ? Double(entriesWithReflections.count) / Double(recentEntries.count) : 0.0
    }
    
    private func calculateTrend(current: Double, previous: Double) -> TrendDirection {
        let difference = current - previous
        let threshold = 0.05 // 5% threshold for trend detection
        
        if difference > threshold {
            return .up
        } else if difference < -threshold {
            return .down
        } else {
            return .stable
        }
    }
}

// MARK: - Chart Data Manager

/// Manages chart data for the analytics dashboard
class ChartDataManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var networkOverviewData: [ChartDataPoint] = []
    @Published var activityHeatmapData: [ActivityData] = []
    @Published var relationshipStrengthData: [ChartDataPoint] = []
    @Published var interactionFrequencyData: [ChartDataPoint] = []
    @Published var teamPerformanceData: [ChartDataPoint] = []
    @Published var collaborationData: [CollaborationData] = []
    @Published var achievementTrendsData: [ChartDataPoint] = []
    @Published var goalProgressData: [GoalProgressData] = []
    @Published var personalGrowthData: [ChartDataPoint] = []
    @Published var skillDevelopmentData: [SkillDevelopmentData] = []
    
    @Published var isLoading = false
    
    // MARK: - Private Properties
    
    private var currentTimeRange: AnalyticsTimeRange = .month
    
    // MARK: - Main Methods
    
    /// Load chart data for the specified time range
    func loadChartData(for timeRange: AnalyticsTimeRange) async {
        currentTimeRange = timeRange
        isLoading = true
        
        await withTaskGroup(of: Void.self) { group in
            group.addTask { await self.generateNetworkOverviewData() }
            group.addTask { await self.generateActivityHeatmapData() }
            group.addTask { await self.generateRelationshipStrengthData() }
            group.addTask { await self.generateInteractionFrequencyData() }
            group.addTask { await self.generateTeamPerformanceData() }
            group.addTask { await self.generateCollaborationData() }
            group.addTask { await self.generateAchievementTrendsData() }
            group.addTask { await self.generateGoalProgressData() }
            group.addTask { await self.generatePersonalGrowthData() }
            group.addTask { await self.generateSkillDevelopmentData() }
        }
        
        isLoading = false
    }
    
    /// Update chart data for new time range
    func updateData(for timeRange: AnalyticsTimeRange) async {
        await loadChartData(for: timeRange)
    }
    
    /// Refresh all chart data
    func refreshData() async {
        await loadChartData(for: currentTimeRange)
    }
    
    // MARK: - Data Generation Methods
    
    private func generateNetworkOverviewData() async {
        let data = generateSampleTimeSeriesData(count: 30, baseValue: 0.7, variance: 0.2)
        
        await MainActor.run {
            self.networkOverviewData = data
        }
    }
    
    private func generateActivityHeatmapData() async {
        var data: [ActivityData] = []
        let calendar = Calendar.current
        let now = Date()
        
        for i in 0..<90 { // Last 90 days
            if let date = calendar.date(byAdding: .day, value: -i, to: now) {
                let activityType = ActivityData.ActivityType.allCases.randomElement() ?? .interaction
                let intensity = Double.random(in: 0.1...1.0)
                
                data.append(ActivityData(
                    date: date,
                    activityType: activityType,
                    intensity: intensity,
                    duration: TimeInterval.random(in: 300...3600), // 5 minutes to 1 hour
                    participants: [UUID(), UUID()] // Sample participants
                ))
            }
        }
        
        await MainActor.run {
            self.activityHeatmapData = data
        }
    }
    
    private func generateRelationshipStrengthData() async {
        let categories = ["Strong", "Medium", "Weak", "Dormant"]
        let values = [25.0, 35.0, 20.0, 20.0] // Sample distribution
        let colors: [Color] = [.green, .blue, .orange, .red]
        
        let data = zip(zip(categories, values), colors).map { categoryValue, color in
            ChartDataPoint(
                date: Date(),
                value: categoryValue.1,
                category: categoryValue.0,
                color: color
            )
        }
        
        await MainActor.run {
            self.relationshipStrengthData = data
        }
    }
    
    private func generateInteractionFrequencyData() async {
        let data = generateSampleTimeSeriesData(count: 30, baseValue: 15.0, variance: 8.0)
        
        await MainActor.run {
            self.interactionFrequencyData = data
        }
    }
    
    private func generateTeamPerformanceData() async {
        let teams = ["Team Alpha", "Team Beta", "Team Gamma", "Team Delta"]
        let performances = [0.85, 0.72, 0.91, 0.68]
        let colors: [Color] = [.blue, .green, .purple, .orange]
        
        let data = zip(zip(teams, performances), colors).map { teamPerformance, color in
            ChartDataPoint(
                date: Date(),
                value: teamPerformance.1,
                category: teamPerformance.0,
                color: color
            )
        }
        
        await MainActor.run {
            self.teamPerformanceData = data
        }
    }
    
    private func generateCollaborationData() async {
        // Generate sample collaboration data
        // This would be replaced with real data from Core Data
        await MainActor.run {
            self.collaborationData = []
        }
    }
    
    private func generateAchievementTrendsData() async {
        let data = generateSampleTimeSeriesData(count: 12, baseValue: 8.0, variance: 4.0, interval: .month)
        
        await MainActor.run {
            self.achievementTrendsData = data
        }
    }
    
    private func generateGoalProgressData() async {
        // Generate sample goal progress data
        // This would be replaced with real data from Core Data
        await MainActor.run {
            self.goalProgressData = []
        }
    }
    
    private func generatePersonalGrowthData() async {
        let data = generateSampleTimeSeriesData(count: 52, baseValue: 0.6, variance: 0.3, interval: .weekOfYear)
        
        await MainActor.run {
            self.personalGrowthData = data
        }
    }
    
    private func generateSkillDevelopmentData() async {
        // Generate sample skill development data
        // This would be replaced with real data from Core Data
        await MainActor.run {
            self.skillDevelopmentData = []
        }
    }
    
    // MARK: - Helper Methods
    
    private func generateSampleTimeSeriesData(count: Int, baseValue: Double, variance: Double, interval: Calendar.Component = .day) -> [ChartDataPoint] {
        let calendar = Calendar.current
        let now = Date()
        var data: [ChartDataPoint] = []
        
        for i in 0..<count {
            if let date = calendar.date(byAdding: interval, value: -i, to: now) {
                let value = baseValue + Double.random(in: -variance...variance)
                data.append(ChartDataPoint(
                    date: date,
                    value: max(0, value),
                    color: .blue
                ))
            }
        }
        
        return data.reversed() // Chronological order
    }
}
