//
//  NodeDetailView.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI

/// Detailed view for a specific network node showing connections, insights, and recommendations
struct NodeDetailView: View {
    
    // MARK: - Properties
    
    let node: NetworkNode
    @StateObject private var viewModel = NodeDetailViewModel()
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedTab: DetailTab = .overview
    @State private var showingActionSheet = false
    @State private var selectedRecommendation: NodeDetailViewModel.ActionRecommendation?
    
    enum DetailTab: String, CaseIterable {
        case overview = "overview"
        case connections = "connections"
        case history = "history"
        case insights = "insights"
        
        var displayName: String {
            switch self {
            case .overview: return "Overview"
            case .connections: return "Connections"
            case .history: return "History"
            case .insights: return "Insights"
            }
        }
        
        var icon: String {
            switch self {
            case .overview: return "person.circle"
            case .connections: return "link"
            case .history: return "clock"
            case .insights: return "lightbulb"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // Header Section
                    nodeHeaderSection
                    
                    // Tab Navigation
                    tabNavigationSection
                    
                    // Content based on selected tab
                    tabContentSection
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle(node.name)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Actions") {
                        showingActionSheet = true
                    }
                }
            }
            .actionSheet(isPresented: $showingActionSheet) {
                ActionSheet(
                    title: Text("Actions for \(node.name)"),
                    buttons: [
                        .default(Text("Send Message")) { /* Handle message */ },
                        .default(Text("Schedule Meeting")) { /* Handle meeting */ },
                        .default(Text("Add to Team")) { /* Handle team addition */ },
                        .default(Text("View Timeline")) { /* Handle timeline view */ },
                        .cancel()
                    ]
                )
            }
            .onAppear {
                loadNodeDetails()
            }
        }
    }
    
    // MARK: - Header Section
    
    private var nodeHeaderSection: some View {
        VStack(spacing: 16) {
            // Profile Image and Basic Info
            HStack(spacing: 16) {
                // Profile Avatar
                ZStack {
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    node.color.opacity(0.8),
                                    node.color.opacity(0.4)
                                ],
                                center: .topLeading,
                                startRadius: 10,
                                endRadius: 40
                            )
                        )
                        .frame(width: 80, height: 80)
                    
                    if let icon = node.icon {
                        Image(systemName: icon)
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(.white)
                    } else {
                        Text(node.initials)
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                
                // Basic Information
                VStack(alignment: .leading, spacing: 8) {
                    Text(node.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(node.nodeType.rawValue.capitalized)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    // Relationship Strength Indicator
                    HStack(spacing: 8) {
                        Text("Relationship Strength")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        ProgressView(value: node.strength)
                            .progressViewStyle(LinearProgressViewStyle(tint: strengthColor))
                            .frame(width: 80)
                        
                        Text("\(Int(node.strength * 100))%")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(strengthColor)
                    }
                }
                
                Spacer()
            }
            
            // Quick Stats
            HStack(spacing: 20) {
                QuickStatView(
                    title: "Connections",
                    value: "\(viewModel.directConnections.count)",
                    icon: "link",
                    color: .blue
                )
                
                QuickStatView(
                    title: "Interactions",
                    value: "\(viewModel.recentInteractions.count)",
                    icon: "message",
                    color: .green
                )
                
                QuickStatView(
                    title: "Mutual",
                    value: "\(viewModel.mutualConnections.count)",
                    icon: "person.2",
                    color: .orange
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    // MARK: - Tab Navigation Section
    
    private var tabNavigationSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 0) {
                ForEach(DetailTab.allCases, id: \.self) { tab in
                    Button(action: { selectedTab = tab }) {
                        VStack(spacing: 8) {
                            Image(systemName: tab.icon)
                                .font(.title3)
                                .foregroundColor(selectedTab == tab ? .blue : .secondary)
                            
                            Text(tab.displayName)
                                .font(.caption)
                                .fontWeight(selectedTab == tab ? .semibold : .regular)
                                .foregroundColor(selectedTab == tab ? .blue : .secondary)
                        }
                        .frame(width: 80, height: 60)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedTab == tab ? Color.blue.opacity(0.1) : Color.clear)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 12)
    }
    
    // MARK: - Tab Content Section
    
    private var tabContentSection: some View {
        Group {
            switch selectedTab {
            case .overview:
                overviewContent
            case .connections:
                connectionsContent
            case .history:
                historyContent
            case .insights:
                insightsContent
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 20)
    }
    
    // MARK: - Overview Content
    
    private var overviewContent: some View {
        VStack(spacing: 16) {
            // Recent Interactions
            if !viewModel.recentInteractions.isEmpty {
                SectionCard(title: "Recent Interactions", icon: "clock") {
                    ForEach(viewModel.recentInteractions.prefix(3), id: \.id) { interaction in
                        InteractionRowView(interaction: interaction)
                    }
                }
            }
            
            // Top Recommendations
            if !viewModel.recommendations.isEmpty {
                SectionCard(title: "Recommendations", icon: "lightbulb") {
                    ForEach(viewModel.recommendations.prefix(2), id: \.id) { recommendation in
                        RecommendationRowView(recommendation: recommendation) {
                            selectedRecommendation = recommendation
                        }
                    }
                }
            }
            
            // Connection Summary
            SectionCard(title: "Connection Summary", icon: "person.2") {
                VStack(spacing: 12) {
                    HStack {
                        Text("Direct Connections")
                        Spacer()
                        Text("\(viewModel.directConnections.count)")
                            .fontWeight(.semibold)
                    }
                    
                    HStack {
                        Text("Mutual Connections")
                        Spacer()
                        Text("\(viewModel.mutualConnections.count)")
                            .fontWeight(.semibold)
                    }
                    
                    HStack {
                        Text("Last Interaction")
                        Spacer()
                        Text(lastInteractionText)
                            .fontWeight(.semibold)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    // MARK: - Connections Content
    
    private var connectionsContent: some View {
        VStack(spacing: 16) {
            // Direct Connections
            if !viewModel.directConnections.isEmpty {
                SectionCard(title: "Direct Connections", icon: "link") {
                    ForEach(viewModel.directConnections, id: \.id) { connection in
                        ConnectionRowView(connection: connection)
                    }
                }
            }
            
            // Mutual Connections
            if !viewModel.mutualConnections.isEmpty {
                SectionCard(title: "Mutual Connections", icon: "person.2") {
                    ForEach(viewModel.mutualConnections, id: \.id) { mutualNode in
                        MutualConnectionRowView(node: mutualNode)
                    }
                }
            }
        }
    }
    
    // MARK: - History Content
    
    private var historyContent: some View {
        VStack(spacing: 16) {
            // Interaction History
            if !viewModel.recentInteractions.isEmpty {
                SectionCard(title: "Interaction History", icon: "clock") {
                    ForEach(viewModel.recentInteractions, id: \.id) { interaction in
                        InteractionRowView(interaction: interaction)
                    }
                }
            }
            
            // Connection History
            if !viewModel.connectionHistory.isEmpty {
                SectionCard(title: "Connection History", icon: "chart.line.uptrend.xyaxis") {
                    ForEach(viewModel.connectionHistory, id: \.id) { historyItem in
                        ConnectionHistoryRowView(historyItem: historyItem)
                    }
                }
            }
        }
    }
    
    // MARK: - Insights Content
    
    private var insightsContent: some View {
        VStack(spacing: 16) {
            // Node-specific Insights
            if !viewModel.insights.isEmpty {
                SectionCard(title: "Insights", icon: "lightbulb") {
                    ForEach(viewModel.insights, id: \.id) { insight in
                        InsightRowView(insight: insight)
                    }
                }
            }
            
            // All Recommendations
            if !viewModel.recommendations.isEmpty {
                SectionCard(title: "All Recommendations", icon: "star") {
                    ForEach(viewModel.recommendations, id: \.id) { recommendation in
                        RecommendationRowView(recommendation: recommendation) {
                            selectedRecommendation = recommendation
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var strengthColor: Color {
        if node.strength > 0.7 {
            return .green
        } else if node.strength > 0.4 {
            return .orange
        } else {
            return .red
        }
    }
    
    private var lastInteractionText: String {
        guard let lastInteraction = viewModel.recentInteractions.first?.date else {
            return "No recent interactions"
        }
        
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: lastInteraction, relativeTo: Date())
    }
    
    // MARK: - Helper Methods
    
    private func loadNodeDetails() {
        // Load detailed information for the node
        viewModel.loadDetails(for: node)
    }
}

// MARK: - Supporting Views

struct QuickStatView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct SectionCard<Content: View>: View {
    let title: String
    let icon: String
    let content: Content
    
    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            content
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Row Views

struct InteractionRowView: View {
    let interaction: NodeDetailViewModel.InteractionSummary
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(interaction.type)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(interaction.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(interaction.date, style: .date)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                ProgressView(value: interaction.strength)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    .frame(width: 40)
            }
        }
        .padding(.vertical, 4)
    }
}

struct ConnectionRowView: View {
    let connection: NetworkConnection
    
    var body: some View {
        HStack {
            Circle()
                .fill(connection.color)
                .frame(width: 12, height: 12)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(connection.toNode.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(connection.connectionType.rawValue.capitalized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text("\(Int(connection.strength * 100))%")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(connection.color)
        }
        .padding(.vertical, 4)
    }
}

struct MutualConnectionRowView: View {
    let node: NetworkNode
    
    var body: some View {
        HStack {
            Text(node.initials)
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Circle().fill(node.color))
            
            Text(node.name)
                .font(.subheadline)
            
            Spacer()
            
            Text("\(Int(node.strength * 100))%")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

struct ConnectionHistoryRowView: View {
    let historyItem: NodeDetailViewModel.ConnectionHistoryItem
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(historyItem.event)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(historyItem.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(historyItem.date, style: .date)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack(spacing: 4) {
                    Image(systemName: historyItem.strengthChange > 0 ? "arrow.up" : "arrow.down")
                        .font(.caption2)
                        .foregroundColor(historyItem.strengthChange > 0 ? .green : .red)
                    
                    Text("\(Int(abs(historyItem.strengthChange) * 100))%")
                        .font(.caption2)
                        .foregroundColor(historyItem.strengthChange > 0 ? .green : .red)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

struct InsightRowView: View {
    let insight: NetworkInsight
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: insight.icon)
                .foregroundColor(insight.priority.color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(insight.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(insight.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct RecommendationRowView: View {
    let recommendation: NodeDetailViewModel.ActionRecommendation
    let action: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: recommendation.actionType.icon)
                .foregroundColor(recommendation.priority.color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(recommendation.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(recommendation.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("Act") {
                action()
            }
            .font(.caption)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(recommendation.priority.color)
            .foregroundColor(.white)
            .clipShape(Capsule())
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Preview

struct NodeDetailView_Previews: PreviewProvider {
    static var previews: some View {
        NodeDetailView(
            node: NetworkNode(
                personId: UUID(),
                name: "John Doe",
                strength: 0.8
            )
        )
    }
}
