//
//  CalendarIntegration.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import EventKit
import SwiftUI
import Combine

// MARK: - Calendar Models

/// Represents a calendar event related to Keeps entities
struct KeepsCalendarEvent: Identifiable {
    let id = UUID()
    let eventIdentifier: String?
    let title: String
    let notes: String?
    let startDate: Date
    let endDate: Date
    let isAllDay: Bool
    let location: String?
    let attendees: [String]
    let associatedEntityId: UUID?
    let associatedEntityType: AssociatedEntityType
    let eventType: EventType
    let reminderMinutes: Int?
    let recurrenceRule: RecurrenceRule?
    
    enum AssociatedEntityType: String, CaseIterable {
        case person = "person"
        case team = "team"
        case project = "project"
        case interaction = "interaction"
        
        var displayName: String {
            switch self {
            case .person: return "Person"
            case .team: return "Team"
            case .project: return "Project"
            case .interaction: return "Interaction"
            }
        }
    }
    
    enum EventType: String, CaseIterable {
        case meeting = "meeting"
        case call = "call"
        case coffee = "coffee"
        case followUp = "followUp"
        case deadline = "deadline"
        case reminder = "reminder"
        case celebration = "celebration"
        
        var displayName: String {
            switch self {
            case .meeting: return "Meeting"
            case .call: return "Call"
            case .coffee: return "Coffee"
            case .followUp: return "Follow-up"
            case .deadline: return "Deadline"
            case .reminder: return "Reminder"
            case .celebration: return "Celebration"
            }
        }
        
        var icon: String {
            switch self {
            case .meeting: return "person.2"
            case .call: return "phone"
            case .coffee: return "cup.and.saucer"
            case .followUp: return "arrow.clockwise"
            case .deadline: return "clock.badge.exclamationmark"
            case .reminder: return "bell"
            case .celebration: return "party.popper"
            }
        }
        
        var color: Color {
            switch self {
            case .meeting: return .blue
            case .call: return .green
            case .coffee: return .brown
            case .followUp: return .orange
            case .deadline: return .red
            case .reminder: return .purple
            case .celebration: return .pink
            }
        }
    }
    
    struct RecurrenceRule {
        let frequency: Frequency
        let interval: Int
        let endDate: Date?
        let occurrenceCount: Int?
        
        enum Frequency: String, CaseIterable {
            case daily = "daily"
            case weekly = "weekly"
            case monthly = "monthly"
            case yearly = "yearly"
            
            var displayName: String {
                switch self {
                case .daily: return "Daily"
                case .weekly: return "Weekly"
                case .monthly: return "Monthly"
                case .yearly: return "Yearly"
                }
            }
        }
    }
    
    var duration: TimeInterval {
        return endDate.timeIntervalSince(startDate)
    }
    
    var formattedDuration: String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

// MARK: - Calendar Manager

/// Comprehensive calendar integration manager with EventKit integration
class CalendarManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var events: [KeepsCalendarEvent] = []
    @Published var isAuthorized = false
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var availableCalendars: [EKCalendar] = []
    @Published var selectedCalendar: EKCalendar?
    
    // MARK: - Private Properties
    
    private let eventStore = EKEventStore()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init() {
        checkCalendarAuthorization()
        loadAvailableCalendars()
    }
    
    // MARK: - Public Methods
    
    /// Request calendar access permission
    func requestCalendarAccess() async -> Bool {
        return await withCheckedContinuation { continuation in
            eventStore.requestFullAccessToEvents { granted, error in
                DispatchQueue.main.async {
                    self.isAuthorized = granted
                    if granted {
                        self.loadAvailableCalendars()
                    }
                    continuation.resume(returning: granted)
                }
            }
        }
    }
    
    /// Create a new calendar event
    func createEvent(_ keepsEvent: KeepsCalendarEvent) async -> Bool {
        guard isAuthorized else {
            await MainActor.run {
                errorMessage = "Calendar access not authorized"
            }
            return false
        }
        
        guard let calendar = selectedCalendar ?? eventStore.defaultCalendarForNewEvents else {
            await MainActor.run {
                errorMessage = "No calendar selected"
            }
            return false
        }
        
        let event = EKEvent(eventStore: eventStore)
        event.title = keepsEvent.title
        event.notes = keepsEvent.notes
        event.startDate = keepsEvent.startDate
        event.endDate = keepsEvent.endDate
        event.isAllDay = keepsEvent.isAllDay
        event.location = keepsEvent.location
        event.calendar = calendar
        
        // Add reminder if specified
        if let reminderMinutes = keepsEvent.reminderMinutes {
            let alarm = EKAlarm(relativeOffset: TimeInterval(-reminderMinutes * 60))
            event.addAlarm(alarm)
        }
        
        // Add recurrence rule if specified
        if let recurrenceRule = keepsEvent.recurrenceRule {
            event.recurrenceRules = [createEKRecurrenceRule(from: recurrenceRule)]
        }
        
        do {
            try eventStore.save(event, span: .thisEvent)
            
            await MainActor.run {
                // Update local events array
                let updatedEvent = keepsEvent
                // In a real implementation, we would update the event with the actual identifier
                events.append(updatedEvent)
            }
            
            return true
        } catch {
            await MainActor.run {
                errorMessage = "Failed to create event: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    /// Update an existing calendar event
    func updateEvent(_ keepsEvent: KeepsCalendarEvent) async -> Bool {
        guard isAuthorized,
              let eventIdentifier = keepsEvent.eventIdentifier,
              let event = eventStore.event(withIdentifier: eventIdentifier) else {
            await MainActor.run {
                errorMessage = "Event not found"
            }
            return false
        }
        
        event.title = keepsEvent.title
        event.notes = keepsEvent.notes
        event.startDate = keepsEvent.startDate
        event.endDate = keepsEvent.endDate
        event.isAllDay = keepsEvent.isAllDay
        event.location = keepsEvent.location
        
        do {
            try eventStore.save(event, span: .thisEvent)
            
            await MainActor.run {
                // Update local events array
                if let index = events.firstIndex(where: { $0.id == keepsEvent.id }) {
                    events[index] = keepsEvent
                }
            }
            
            return true
        } catch {
            await MainActor.run {
                errorMessage = "Failed to update event: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    /// Delete a calendar event
    func deleteEvent(_ keepsEvent: KeepsCalendarEvent) async -> Bool {
        guard isAuthorized,
              let eventIdentifier = keepsEvent.eventIdentifier,
              let event = eventStore.event(withIdentifier: eventIdentifier) else {
            await MainActor.run {
                errorMessage = "Event not found"
            }
            return false
        }
        
        do {
            try eventStore.remove(event, span: .thisEvent)
            
            await MainActor.run {
                events.removeAll { $0.id == keepsEvent.id }
            }
            
            return true
        } catch {
            await MainActor.run {
                errorMessage = "Failed to delete event: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    /// Load events for a specific date range
    func loadEvents(from startDate: Date, to endDate: Date) async {
        guard isAuthorized else { return }
        
        await MainActor.run {
            isLoading = true
        }
        
        let predicate = eventStore.predicateForEvents(withStart: startDate, end: endDate, calendars: nil)
        let ekEvents = eventStore.events(matching: predicate)
        
        let keepsEvents = ekEvents.compactMap { ekEvent -> KeepsCalendarEvent? in
            return convertEKEventToKeepsEvent(ekEvent)
        }
        
        await MainActor.run {
            events = keepsEvents
            isLoading = false
        }
    }
    
    /// Get events for a specific entity
    func events(for entityId: UUID, type: KeepsCalendarEvent.AssociatedEntityType) -> [KeepsCalendarEvent] {
        return events.filter { 
            $0.associatedEntityId == entityId && $0.associatedEntityType == type 
        }
    }
    
    /// Get upcoming events (next 7 days)
    func getUpcomingEvents() -> [KeepsCalendarEvent] {
        let now = Date()
        let nextWeek = Calendar.current.date(byAdding: .day, value: 7, to: now) ?? now
        
        return events.filter { event in
            event.startDate >= now && event.startDate <= nextWeek
        }.sorted { $0.startDate < $1.startDate }
    }
    
    /// Schedule a follow-up meeting
    func scheduleFollowUp(for personId: UUID, in days: Int, duration: TimeInterval = 3600) async -> Bool {
        let startDate = Calendar.current.date(byAdding: .day, value: days, to: Date()) ?? Date()
        let endDate = startDate.addingTimeInterval(duration)
        
        let followUpEvent = KeepsCalendarEvent(
            eventIdentifier: nil,
            title: "Follow-up Meeting",
            notes: "Scheduled follow-up meeting",
            startDate: startDate,
            endDate: endDate,
            isAllDay: false,
            location: nil,
            attendees: [],
            associatedEntityId: personId,
            associatedEntityType: .person,
            eventType: .followUp,
            reminderMinutes: 15,
            recurrenceRule: nil
        )
        
        return await createEvent(followUpEvent)
    }
    
    /// Schedule a team meeting
    func scheduleTeamMeeting(for teamId: UUID, title: String, startDate: Date, duration: TimeInterval, location: String? = nil) async -> Bool {
        let endDate = startDate.addingTimeInterval(duration)
        
        let teamEvent = KeepsCalendarEvent(
            eventIdentifier: nil,
            title: title,
            notes: "Team meeting scheduled via Keeps",
            startDate: startDate,
            endDate: endDate,
            isAllDay: false,
            location: location,
            attendees: [],
            associatedEntityId: teamId,
            associatedEntityType: .team,
            eventType: .meeting,
            reminderMinutes: 15,
            recurrenceRule: nil
        )
        
        return await createEvent(teamEvent)
    }
    
    // MARK: - Private Methods
    
    private func checkCalendarAuthorization() {
        let status = EKEventStore.authorizationStatus(for: .event)
        isAuthorized = (status == .fullAccess)
    }
    
    private func loadAvailableCalendars() {
        guard isAuthorized else { return }
        
        availableCalendars = eventStore.calendars(for: .event).filter { calendar in
            calendar.allowsContentModifications
        }
        
        // Set default calendar
        if selectedCalendar == nil {
            selectedCalendar = eventStore.defaultCalendarForNewEvents
        }
    }
    
    private func convertEKEventToKeepsEvent(_ ekEvent: EKEvent) -> KeepsCalendarEvent? {
        // Extract Keeps-specific information from event notes or title
        // This is a simplified implementation
        
        let eventType: KeepsCalendarEvent.EventType
        if ekEvent.title.lowercased().contains("meeting") {
            eventType = .meeting
        } else if ekEvent.title.lowercased().contains("call") {
            eventType = .call
        } else if ekEvent.title.lowercased().contains("coffee") {
            eventType = .coffee
        } else if ekEvent.title.lowercased().contains("follow") {
            eventType = .followUp
        } else if ekEvent.title.lowercased().contains("deadline") {
            eventType = .deadline
        } else if ekEvent.title.lowercased().contains("reminder") {
            eventType = .reminder
        } else if ekEvent.title.lowercased().contains("celebration") {
            eventType = .celebration
        } else {
            eventType = .meeting
        }
        
        return KeepsCalendarEvent(
            eventIdentifier: ekEvent.eventIdentifier,
            title: ekEvent.title,
            notes: ekEvent.notes,
            startDate: ekEvent.startDate,
            endDate: ekEvent.endDate,
            isAllDay: ekEvent.isAllDay,
            location: ekEvent.location,
            attendees: ekEvent.attendees?.compactMap { $0.name } ?? [],
            associatedEntityId: nil, // Would need to be extracted from notes or custom fields
            associatedEntityType: .interaction,
            eventType: eventType,
            reminderMinutes: ekEvent.alarms?.first.map { Int(-$0.relativeOffset / 60) },
            recurrenceRule: nil // Would need to convert EKRecurrenceRule
        )
    }
    
    private func createEKRecurrenceRule(from rule: KeepsCalendarEvent.RecurrenceRule) -> EKRecurrenceRule {
        let frequency: EKRecurrenceFrequency
        switch rule.frequency {
        case .daily: frequency = .daily
        case .weekly: frequency = .weekly
        case .monthly: frequency = .monthly
        case .yearly: frequency = .yearly
        }
        
        var recurrenceEnd: EKRecurrenceEnd?
        if let endDate = rule.endDate {
            recurrenceEnd = EKRecurrenceEnd(end: endDate)
        } else if let occurrenceCount = rule.occurrenceCount {
            recurrenceEnd = EKRecurrenceEnd(occurrenceCount: occurrenceCount)
        }
        
        return EKRecurrenceRule(
            recurrenceWith: frequency,
            interval: rule.interval,
            end: recurrenceEnd
        )
    }
}

// MARK: - Calendar Integration Errors

enum CalendarIntegrationError: LocalizedError {
    case accessDenied
    case eventNotFound
    case calendarNotFound
    case saveFailed(String)
    case deleteFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .accessDenied:
            return "Calendar access denied. Please enable calendar access in Settings."
        case .eventNotFound:
            return "Calendar event not found."
        case .calendarNotFound:
            return "Calendar not found."
        case .saveFailed(let message):
            return "Failed to save event: \(message)"
        case .deleteFailed(let message):
            return "Failed to delete event: \(message)"
        }
    }
}

// MARK: - Calendar Helper Extensions

extension Date {
    var isToday: Bool {
        return Calendar.current.isDateInToday(self)
    }
    
    var isTomorrow: Bool {
        return Calendar.current.isDateInTomorrow(self)
    }
    
    var isThisWeek: Bool {
        return Calendar.current.isDate(self, equalTo: Date(), toGranularity: .weekOfYear)
    }
    
    func formattedForCalendar() -> String {
        let formatter = DateFormatter()
        
        if isToday {
            formatter.dateFormat = "'Today at' h:mm a"
        } else if isTomorrow {
            formatter.dateFormat = "'Tomorrow at' h:mm a"
        } else if isThisWeek {
            formatter.dateFormat = "EEEE 'at' h:mm a"
        } else {
            formatter.dateFormat = "MMM d 'at' h:mm a"
        }
        
        return formatter.string(from: self)
    }
}
