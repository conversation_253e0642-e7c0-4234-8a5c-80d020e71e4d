//
//  VoiceNoteRecorder.swift
//  Keeps
//
//  Created by <PERSON>M<PERSON> on Phase 5 Implementation
//

import Foundation
import AVFoundation
import SwiftUI
import Combine

/// High-performance voice note recorder with real-time audio processing and waveform generation
class VoiceNoteRecorder: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var recordingState: VoiceRecordingState = .idle
    @Published var currentWaveform: WaveformData = WaveformData()
    @Published var audioLevel: Float = 0.0
    @Published var recordingDuration: TimeInterval = 0.0
    @Published var isPermissionGranted = false
    
    // MARK: - Private Properties
    
    private var audioRecorder: AVAudioRecorder?
    private var audioSession: AVAudioSession = AVAudioSession.sharedInstance()
    private var recordingTimer: Timer?
    private var levelTimer: Timer?
    private var waveformSamples: [Float] = []
    
    private let settings: VoiceRecordingSettings
    private let fileManager = FileManager.default
    private var recordingURL: URL?
    
    // MARK: - Constants
    
    private let waveformUpdateInterval: TimeInterval = 0.05 // 20 FPS
    private let levelUpdateInterval: TimeInterval = 0.1 // 10 FPS
    private let maxWaveformSamples = 1000
    
    // MARK: - Initialization
    
    init(settings: VoiceRecordingSettings = .default) {
        self.settings = settings
        super.init()
        setupAudioSession()
        checkMicrophonePermission()
    }
    
    deinit {
        audioRecorder?.stop()
        invalidateTimers()
    }
    
    // MARK: - Public Methods
    
    /// Start recording a new voice note
    func startRecording() async {
        await MainActor.run {
            recordingState = .preparing
        }
        
        do {
            try await prepareForRecording()
            try await beginRecording()
            
            await MainActor.run {
                recordingState = .recording(duration: 0)
                startTimers()
            }
        } catch {
            await MainActor.run {
                recordingState = .error(mapError(error))
            }
        }
    }
    
    /// Pause the current recording
    func pauseRecording() {
        guard case .recording(let duration) = recordingState else { return }
        
        audioRecorder?.pause()
        invalidateTimers()
        recordingState = .paused(duration: duration)
    }
    
    /// Resume a paused recording
    func resumeRecording() {
        guard case .paused = recordingState else { return }
        
        audioRecorder?.record()
        startTimers()
        recordingState = .recording(duration: recordingDuration)
    }
    
    /// Stop recording and create voice note
    func stopRecording() async -> VoiceNote? {
        guard recordingState.isRecording || recordingState.isPaused else { return nil }
        
        audioRecorder?.stop()
        invalidateTimers()
        
        guard let url = recordingURL else {
            await MainActor.run {
                recordingState = .error(.fileCreationFailed)
            }
            return nil
        }
        
        do {
            let voiceNote = try await createVoiceNote(from: url)
            await MainActor.run {
                recordingState = .finished(voiceNote: voiceNote)
            }
            return voiceNote
        } catch {
            await MainActor.run {
                recordingState = .error(mapError(error))
            }
            return nil
        }
    }
    
    /// Cancel the current recording
    func cancelRecording() {
        audioRecorder?.stop()
        invalidateTimers()
        
        // Clean up recording file
        if let url = recordingURL {
            try? fileManager.removeItem(at: url)
        }
        
        recordingState = .idle
        resetRecordingData()
    }
    
    /// Check and request microphone permission
    func requestMicrophonePermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVAudioApplication.requestRecordPermission { granted in
                DispatchQueue.main.async {
                    self.isPermissionGranted = granted
                    continuation.resume(returning: granted)
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("Failed to set up audio session: \(error)")
        }
    }
    
    private func checkMicrophonePermission() {
        switch AVAudioApplication.shared.recordPermission {
        case .granted:
            isPermissionGranted = true
        case .denied:
            isPermissionGranted = false
        case .undetermined:
            Task {
                await requestMicrophonePermission()
            }
        @unknown default:
            isPermissionGranted = false
        }
    }
    
    private func prepareForRecording() async throws {
        guard isPermissionGranted else {
            throw VoiceRecordingError.permissionDenied
        }
        
        // Check available disk space
        guard hasEnoughDiskSpace() else {
            throw VoiceRecordingError.diskSpaceInsufficient
        }
        
        // Create recording URL
        recordingURL = createRecordingURL()
        guard let url = recordingURL else {
            throw VoiceRecordingError.fileCreationFailed
        }
        
        // Create audio recorder
        audioRecorder = try AVAudioRecorder(url: url, settings: settings.audioSettings)
        audioRecorder?.delegate = self
        audioRecorder?.isMeteringEnabled = true
        audioRecorder?.prepareToRecord()
    }
    
    private func beginRecording() async throws {
        guard let recorder = audioRecorder else {
            throw VoiceRecordingError.recordingFailed("Audio recorder not initialized")
        }
        
        guard recorder.record() else {
            throw VoiceRecordingError.recordingFailed("Failed to start recording")
        }
    }
    
    private func startTimers() {
        // Recording duration timer
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
            self.updateRecordingDuration()
        }
        
        // Audio level and waveform timer
        levelTimer = Timer.scheduledTimer(withTimeInterval: waveformUpdateInterval, repeats: true) { _ in
            self.updateAudioLevel()
            self.updateWaveform()
        }
    }
    
    private func invalidateTimers() {
        recordingTimer?.invalidate()
        levelTimer?.invalidate()
        recordingTimer = nil
        levelTimer = nil
    }
    
    private func updateRecordingDuration() {
        guard let recorder = audioRecorder, recorder.isRecording else { return }
        
        recordingDuration = recorder.currentTime
        
        // Check for maximum duration
        if recordingDuration >= settings.maxDuration && settings.autoStop {
            Task {
                await stopRecording()
            }
        }
        
        // Update recording state with current duration
        if case .recording = recordingState {
            recordingState = .recording(duration: recordingDuration)
        }
    }
    
    private func updateAudioLevel() {
        guard let recorder = audioRecorder, recorder.isRecording else { return }
        
        recorder.updateMeters()
        let averagePower = recorder.averagePower(forChannel: 0)
        let normalizedLevel = pow(10.0, averagePower / 20.0)
        
        DispatchQueue.main.async {
            self.audioLevel = Float(normalizedLevel)
        }
    }
    
    private func updateWaveform() {
        guard let recorder = audioRecorder, recorder.isRecording else { return }
        
        recorder.updateMeters()
        let peakPower = recorder.peakPower(forChannel: 0)
        let normalizedPeak = pow(10.0, peakPower / 20.0)
        
        waveformSamples.append(Float(normalizedPeak))
        
        // Limit waveform samples to prevent memory issues
        if waveformSamples.count > maxWaveformSamples {
            waveformSamples.removeFirst()
        }
        
        DispatchQueue.main.async {
            self.currentWaveform = WaveformData(
                samples: self.waveformSamples,
                duration: self.recordingDuration,
                sampleRate: self.settings.quality.sampleRate
            )
        }
    }
    
    private func createVoiceNote(from url: URL) async throws -> VoiceNote {
        let asset = AVURLAsset(url: url)
        let duration = try await asset.load(.duration).seconds
        
        // Validate recording duration
        guard duration >= settings.minDuration else {
            throw VoiceRecordingError.recordingTooShort
        }
        
        guard duration <= settings.maxDuration else {
            throw VoiceRecordingError.recordingTooLong
        }
        
        // Get file attributes
        let attributes = try fileManager.attributesOfItem(atPath: url.path)
        let fileSize = attributes[.size] as? Int64 ?? 0
        
        // Generate title with timestamp
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        let title = "Voice Note - \(formatter.string(from: Date()))"
        
        return VoiceNote(
            title: title,
            fileName: url.lastPathComponent,
            filePath: url.path,
            duration: duration,
            fileSize: fileSize,
            quality: settings.quality
        )
    }
    
    private func createRecordingURL() -> URL? {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let voiceNotesPath = documentsPath.appendingPathComponent("VoiceNotes", isDirectory: true)
        
        // Create voice notes directory if it doesn't exist
        if !fileManager.fileExists(atPath: voiceNotesPath.path) {
            try? fileManager.createDirectory(at: voiceNotesPath, withIntermediateDirectories: true)
        }
        
        let fileName = "voice_note_\(Date().timeIntervalSince1970).m4a"
        return voiceNotesPath.appendingPathComponent(fileName)
    }
    
    private func hasEnoughDiskSpace() -> Bool {
        guard let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return false
        }
        
        do {
            let attributes = try fileManager.attributesOfItem(atPath: documentsPath.path)
            if let freeSpace = attributes[.systemFreeSize] as? Int64 {
                // Require at least 50MB free space
                return freeSpace > 50 * 1024 * 1024
            }
        } catch {
            print("Failed to check disk space: \(error)")
        }
        
        return true
    }
    
    private func resetRecordingData() {
        recordingDuration = 0.0
        audioLevel = 0.0
        waveformSamples.removeAll()
        currentWaveform = WaveformData()
        recordingURL = nil
    }
    
    private func mapError(_ error: Error) -> VoiceRecordingError {
        if let recordingError = error as? VoiceRecordingError {
            return recordingError
        }
        
        if let avError = error as? AVError {
            switch avError.code {
            case .mediaServicesWereReset:
                return .audioSessionSetupFailed
            default:
                return .recordingFailed(avError.localizedDescription)
            }
        }
        
        return .unknown(error.localizedDescription)
    }
}

// MARK: - AVAudioRecorderDelegate

extension VoiceNoteRecorder: AVAudioRecorderDelegate {
    
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        invalidateTimers()
        
        if !flag {
            recordingState = .error(.recordingFailed("Recording finished unsuccessfully"))
        }
    }
    
    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        invalidateTimers()
        
        let recordingError = error.map(mapError) ?? .unknown("Unknown encoding error")
        recordingState = .error(recordingError)
    }
}

// MARK: - Audio Session Notifications

extension VoiceNoteRecorder {
    
    private func setupAudioSessionNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(audioSessionInterruption),
            name: AVAudioSession.interruptionNotification,
            object: audioSession
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(audioSessionRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: audioSession
        )
    }
    
    @objc private func audioSessionInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            if recordingState.isRecording {
                pauseRecording()
            }
        case .ended:
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) && recordingState.isPaused {
                    resumeRecording()
                }
            }
        @unknown default:
            break
        }
    }
    
    @objc private func audioSessionRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        
        switch reason {
        case .oldDeviceUnavailable:
            // Microphone was disconnected
            if recordingState.isRecording {
                Task {
                    await stopRecording()
                }
            }
        default:
            break
        }
    }
}
