//
//  ContactSyncManager.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
@preconcurrency import Contacts
import SwiftUI
import Combine

// MARK: - Contact Sync Models

/// Represents a contact with enhanced Keeps-specific information
struct KeepsContact: Identifiable {
    let id = UUID()
    let contactIdentifier: String?
    let firstName: String
    let lastName: String
    let organizationName: String?
    let jobTitle: String?
    let phoneNumbers: [ContactPhoneNumber]
    let emailAddresses: [ContactEmailAddress]
    let postalAddresses: [ContactPostalAddress]
    let socialProfiles: [ContactSocialProfile]
    let birthday: Date?
    let notes: String?
    let imageData: Data?
    let lastSyncDate: Date
    let keepsPersonId: UUID?
    let syncStatus: SyncStatus
    
    enum SyncStatus: String, CaseIterable {
        case synced = "synced"
        case needsSync = "needsSync"
        case conflict = "conflict"
        case error = "error"
        
        var displayName: String {
            switch self {
            case .synced: return "Synced"
            case .needsSync: return "Needs Sync"
            case .conflict: return "Conflict"
            case .error: return "Error"
            }
        }
        
        var color: Color {
            switch self {
            case .synced: return .green
            case .needsSync: return .orange
            case .conflict: return .red
            case .error: return .red
            }
        }
    }
    
    var fullName: String {
        let components = [firstName, lastName].filter { !$0.isEmpty }
        return components.joined(separator: " ")
    }
    
    var displayName: String {
        if !fullName.isEmpty {
            return fullName
        } else if let organization = organizationName, !organization.isEmpty {
            return organization
        } else {
            return "Unknown Contact"
        }
    }
    
    var primaryPhoneNumber: String? {
        return phoneNumbers.first?.number
    }
    
    var primaryEmailAddress: String? {
        return emailAddresses.first?.address
    }
}

struct ContactPhoneNumber: Identifiable {
    let id = UUID()
    let number: String
    let label: String
    
    init(number: String, label: String = "main") {
        self.number = number
        self.label = label
    }
}

struct ContactEmailAddress: Identifiable {
    let id = UUID()
    let address: String
    let label: String
    
    init(address: String, label: String = "main") {
        self.address = address
        self.label = label
    }
}

struct ContactPostalAddress: Identifiable {
    let id = UUID()
    let street: String
    let city: String
    let state: String
    let postalCode: String
    let country: String
    let label: String
    
    var formattedAddress: String {
        let components = [street, city, state, postalCode, country].filter { !$0.isEmpty }
        return components.joined(separator: ", ")
    }
}

struct ContactSocialProfile: Identifiable {
    let id = UUID()
    let service: String
    let username: String
    let url: String?
}

// MARK: - Contact Sync Manager

/// Comprehensive contact synchronization manager with Contacts framework integration
class ContactSyncManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var contacts: [KeepsContact] = []
    @Published var isAuthorized = false
    @Published var isLoading = false
    @Published var syncProgress: Double = 0.0
    @Published var errorMessage: String?
    @Published var lastSyncDate: Date?
    @Published var conflictedContacts: [KeepsContact] = []
    
    // MARK: - Private Properties
    
    private let contactStore = CNContactStore()
    private var cancellables = Set<AnyCancellable>()
    private let syncQueue = DispatchQueue(label: "contact.sync", qos: .userInitiated)
    
    // Contact keys to fetch
    private let contactKeys: [CNKeyDescriptor] = [
        CNContactIdentifierKey as CNKeyDescriptor,
        CNContactGivenNameKey as CNKeyDescriptor,
        CNContactFamilyNameKey as CNKeyDescriptor,
        CNContactOrganizationNameKey as CNKeyDescriptor,
        CNContactJobTitleKey as CNKeyDescriptor,
        CNContactPhoneNumbersKey as CNKeyDescriptor,
        CNContactEmailAddressesKey as CNKeyDescriptor,
        CNContactPostalAddressesKey as CNKeyDescriptor,
        CNContactSocialProfilesKey as CNKeyDescriptor,
        CNContactBirthdayKey as CNKeyDescriptor,
        CNContactNoteKey as CNKeyDescriptor,
        CNContactImageDataKey as CNKeyDescriptor,
        CNContactImageDataAvailableKey as CNKeyDescriptor
    ]
    
    // MARK: - Initialization
    
    init() {
        checkContactsAuthorization()
        setupNotifications()
    }
    
    // MARK: - Public Methods
    
    /// Request contacts access permission
    func requestContactsAccess() async -> Bool {
        return await withCheckedContinuation { continuation in
            contactStore.requestAccess(for: .contacts) { granted, error in
                DispatchQueue.main.async {
                    self.isAuthorized = granted
                    if let error = error {
                        self.errorMessage = error.localizedDescription
                    }
                    continuation.resume(returning: granted)
                }
            }
        }
    }
    
    /// Perform full contact synchronization
    func syncAllContacts() async {
        guard isAuthorized else {
            await MainActor.run {
                errorMessage = "Contacts access not authorized"
            }
            return
        }
        
        await MainActor.run {
            isLoading = true
            syncProgress = 0.0
        }
        
        do {
            let cnContacts = try await fetchAllContacts()
            
            await MainActor.run {
                syncProgress = 0.3
            }
            
            let keepsContacts = await convertContactsToKeepsContacts(cnContacts)
            
            await MainActor.run {
                syncProgress = 0.7
            }
            
            await processContactSync(keepsContacts)
            
            await MainActor.run {
                contacts = keepsContacts
                lastSyncDate = Date()
                syncProgress = 1.0
                isLoading = false
            }
            
        } catch {
            await MainActor.run {
                errorMessage = "Failed to sync contacts: \(error.localizedDescription)"
                isLoading = false
            }
        }
    }
    
    /// Sync a specific contact by identifier
    func syncContact(identifier: String) async -> KeepsContact? {
        guard isAuthorized else { return nil }
        
        do {
            let cnContact = try contactStore.unifiedContact(withIdentifier: identifier, keysToFetch: contactKeys)
            let keepsContact = convertCNContactToKeepsContact(cnContact)
            
            await MainActor.run {
                if let index = contacts.firstIndex(where: { $0.contactIdentifier == identifier }) {
                    contacts[index] = keepsContact
                } else {
                    contacts.append(keepsContact)
                }
            }
            
            return keepsContact
            
        } catch {
            await MainActor.run {
                errorMessage = "Failed to sync contact: \(error.localizedDescription)"
            }
            return nil
        }
    }
    
    /// Create a new contact in the system contacts
    func createContact(_ keepsContact: KeepsContact) async -> Bool {
        guard isAuthorized else {
            await MainActor.run {
                errorMessage = "Contacts access not authorized"
            }
            return false
        }
        
        let cnContact = CNMutableContact()
        cnContact.givenName = keepsContact.firstName
        cnContact.familyName = keepsContact.lastName
        cnContact.organizationName = keepsContact.organizationName ?? ""
        cnContact.jobTitle = keepsContact.jobTitle ?? ""
        cnContact.note = keepsContact.notes ?? ""
        
        // Add phone numbers
        cnContact.phoneNumbers = keepsContact.phoneNumbers.map { phoneNumber in
            CNLabeledValue(label: phoneNumber.label, value: CNPhoneNumber(stringValue: phoneNumber.number))
        }
        
        // Add email addresses
        cnContact.emailAddresses = keepsContact.emailAddresses.map { emailAddress in
            CNLabeledValue(label: emailAddress.label, value: emailAddress.address as NSString)
        }
        
        // Add postal addresses
        cnContact.postalAddresses = keepsContact.postalAddresses.map { postalAddress in
            let cnAddress = CNMutablePostalAddress()
            cnAddress.street = postalAddress.street
            cnAddress.city = postalAddress.city
            cnAddress.state = postalAddress.state
            cnAddress.postalCode = postalAddress.postalCode
            cnAddress.country = postalAddress.country
            return CNLabeledValue(label: postalAddress.label, value: cnAddress)
        }
        
        // Add image data
        if let imageData = keepsContact.imageData {
            cnContact.imageData = imageData
        }
        
        // Add birthday
        if let birthday = keepsContact.birthday {
            cnContact.birthday = Calendar.current.dateComponents([.year, .month, .day], from: birthday)
        }
        
        let saveRequest = CNSaveRequest()
        saveRequest.add(cnContact, toContainerWithIdentifier: nil)
        
        do {
            try contactStore.execute(saveRequest)
            
            await MainActor.run {
                // Update the contact with the new identifier
                let updatedContact = keepsContact
                // In a real implementation, we would update the contact identifier
                contacts.append(updatedContact)
            }
            
            return true
        } catch {
            await MainActor.run {
                errorMessage = "Failed to create contact: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    /// Update an existing contact
    func updateContact(_ keepsContact: KeepsContact) async -> Bool {
        guard isAuthorized,
              let contactIdentifier = keepsContact.contactIdentifier else {
            await MainActor.run {
                errorMessage = "Contact identifier not found"
            }
            return false
        }
        
        do {
            let cnContact = try contactStore.unifiedContact(withIdentifier: contactIdentifier, keysToFetch: contactKeys).mutableCopy() as! CNMutableContact
            
            cnContact.givenName = keepsContact.firstName
            cnContact.familyName = keepsContact.lastName
            cnContact.organizationName = keepsContact.organizationName ?? ""
            cnContact.jobTitle = keepsContact.jobTitle ?? ""
            cnContact.note = keepsContact.notes ?? ""
            
            // Update phone numbers
            cnContact.phoneNumbers = keepsContact.phoneNumbers.map { phoneNumber in
                CNLabeledValue(label: phoneNumber.label, value: CNPhoneNumber(stringValue: phoneNumber.number))
            }
            
            // Update email addresses
            cnContact.emailAddresses = keepsContact.emailAddresses.map { emailAddress in
                CNLabeledValue(label: emailAddress.label, value: emailAddress.address as NSString)
            }
            
            let saveRequest = CNSaveRequest()
            saveRequest.update(cnContact)
            
            try contactStore.execute(saveRequest)
            
            await MainActor.run {
                if let index = contacts.firstIndex(where: { $0.id == keepsContact.id }) {
                    contacts[index] = keepsContact
                }
            }
            
            return true
        } catch {
            await MainActor.run {
                errorMessage = "Failed to update contact: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    /// Delete a contact
    func deleteContact(_ keepsContact: KeepsContact) async -> Bool {
        guard isAuthorized,
              let contactIdentifier = keepsContact.contactIdentifier else {
            await MainActor.run {
                errorMessage = "Contact identifier not found"
            }
            return false
        }
        
        do {
            let cnContact = try contactStore.unifiedContact(withIdentifier: contactIdentifier, keysToFetch: contactKeys).mutableCopy() as! CNMutableContact
            
            let saveRequest = CNSaveRequest()
            saveRequest.delete(cnContact)
            
            try contactStore.execute(saveRequest)
            
            await MainActor.run {
                contacts.removeAll { $0.id == keepsContact.id }
            }
            
            return true
        } catch {
            await MainActor.run {
                errorMessage = "Failed to delete contact: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    /// Search contacts by name or email
    func searchContacts(query: String) -> [KeepsContact] {
        let lowercaseQuery = query.lowercased()
        
        return contacts.filter { contact in
            contact.fullName.lowercased().contains(lowercaseQuery) ||
            contact.organizationName?.lowercased().contains(lowercaseQuery) == true ||
            contact.emailAddresses.contains { $0.address.lowercased().contains(lowercaseQuery) } ||
            contact.phoneNumbers.contains { $0.number.contains(query) }
        }
    }
    
    /// Get contacts that need synchronization
    func getContactsNeedingSync() -> [KeepsContact] {
        return contacts.filter { $0.syncStatus == .needsSync || $0.syncStatus == .conflict }
    }
    
    /// Resolve contact conflict
    func resolveConflict(for contact: KeepsContact, useKeepsVersion: Bool) async {
        if useKeepsVersion {
            _ = await updateContact(contact)
        } else {
            _ = await syncContact(identifier: contact.contactIdentifier ?? "")
        }
        
        await MainActor.run {
            conflictedContacts.removeAll { $0.id == contact.id }
        }
    }
    
    // MARK: - Private Methods
    
    private func checkContactsAuthorization() {
        let status = CNContactStore.authorizationStatus(for: .contacts)
        isAuthorized = (status == .authorized)
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(contactStoreDidChange),
            name: .CNContactStoreDidChange,
            object: nil
        )
    }
    
    @objc private func contactStoreDidChange() {
        // Handle contact store changes
        Task {
            await syncAllContacts()
        }
    }
    
    private func fetchAllContacts() async throws -> [CNContact] {
        return try await withCheckedThrowingContinuation { continuation in
            syncQueue.async {
                do {
                    var contacts: [CNContact] = []
                    let request = CNContactFetchRequest(keysToFetch: self.contactKeys)
                    
                    try self.contactStore.enumerateContacts(with: request) { contact, _ in
                        contacts.append(contact)
                    }
                    
                    continuation.resume(returning: contacts)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func convertContactsToKeepsContacts(_ cnContacts: [CNContact]) async -> [KeepsContact] {
        return await withCheckedContinuation { continuation in
            syncQueue.async {
                let keepsContacts = cnContacts.map { cnContact in
                    self.convertCNContactToKeepsContact(cnContact)
                }
                continuation.resume(returning: keepsContacts)
            }
        }
    }
    
    private func convertCNContactToKeepsContact(_ cnContact: CNContact) -> KeepsContact {
        let phoneNumbers = cnContact.phoneNumbers.map { labeledValue in
            ContactPhoneNumber(
                number: labeledValue.value.stringValue,
                label: CNLabeledValue<CNPhoneNumber>.localizedString(forLabel: labeledValue.label ?? "")
            )
        }
        
        let emailAddresses = cnContact.emailAddresses.map { labeledValue in
            ContactEmailAddress(
                address: labeledValue.value as String,
                label: CNLabeledValue<NSString>.localizedString(forLabel: labeledValue.label ?? "")
            )
        }
        
        let postalAddresses = cnContact.postalAddresses.map { labeledValue in
            let address = labeledValue.value
            return ContactPostalAddress(
                street: address.street,
                city: address.city,
                state: address.state,
                postalCode: address.postalCode,
                country: address.country,
                label: CNLabeledValue<CNPostalAddress>.localizedString(forLabel: labeledValue.label ?? "")
            )
        }
        
        let socialProfiles = cnContact.socialProfiles.map { labeledValue in
            let profile = labeledValue.value
            return ContactSocialProfile(
                service: profile.service,
                username: profile.username,
                url: profile.urlString
            )
        }
        
        let birthday = cnContact.birthday?.date
        
        return KeepsContact(
            contactIdentifier: cnContact.identifier,
            firstName: cnContact.givenName,
            lastName: cnContact.familyName,
            organizationName: cnContact.organizationName.isEmpty ? nil : cnContact.organizationName,
            jobTitle: cnContact.jobTitle.isEmpty ? nil : cnContact.jobTitle,
            phoneNumbers: phoneNumbers,
            emailAddresses: emailAddresses,
            postalAddresses: postalAddresses,
            socialProfiles: socialProfiles,
            birthday: birthday,
            notes: cnContact.note.isEmpty ? nil : cnContact.note,
            imageData: cnContact.imageData,
            lastSyncDate: Date(),
            keepsPersonId: nil, // Would need to be matched with existing Keeps persons
            syncStatus: .synced
        )
    }
    
    private func processContactSync(_ keepsContacts: [KeepsContact]) async {
        // In a real implementation, this would:
        // 1. Match contacts with existing Keeps persons
        // 2. Detect conflicts between Keeps data and contact data
        // 3. Update sync status accordingly
        // 4. Handle duplicate detection and merging
        
        // For now, we'll just mark all as synced
        await MainActor.run {
            syncProgress = 0.9
        }
    }
}

// MARK: - Contact Sync Errors

enum ContactSyncError: LocalizedError {
    case accessDenied
    case contactNotFound
    case saveFailed(String)
    case deleteFailed(String)
    case syncFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .accessDenied:
            return "Contacts access denied. Please enable contacts access in Settings."
        case .contactNotFound:
            return "Contact not found."
        case .saveFailed(let message):
            return "Failed to save contact: \(message)"
        case .deleteFailed(let message):
            return "Failed to delete contact: \(message)"
        case .syncFailed(let message):
            return "Failed to sync contacts: \(message)"
        }
    }
}
