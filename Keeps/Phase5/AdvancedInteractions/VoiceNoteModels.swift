//
//  VoiceNoteModels.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import SwiftUI
import AVFoundation

// MARK: - Voice Note Models

/// Represents a voice note recording with metadata and playback information
struct VoiceNote: Identifiable, Codable, Equatable {
    let id = UUID()
    let title: String
    let fileName: String
    let filePath: String
    let duration: TimeInterval
    let fileSize: Int64
    let createdAt: Date
    let updatedAt: Date
    let associatedEntityId: UUID?
    let associatedEntityType: AssociatedEntityType
    let transcription: String?
    let tags: [String]
    let isBookmarked: Bool
    let quality: AudioQuality
    
    enum AssociatedEntityType: String, CaseIterable, Codable {
        case person = "person"
        case team = "team"
        case timelineEntry = "timelineEntry"
        case interaction = "interaction"
        case note = "note"
        case standalone = "standalone"
        
        var displayName: String {
            switch self {
            case .person: return "Person"
            case .team: return "Team"
            case .timelineEntry: return "Timeline Entry"
            case .interaction: return "Interaction"
            case .note: return "Note"
            case .standalone: return "Voice Note"
            }
        }
        
        var icon: String {
            switch self {
            case .person: return "person.circle"
            case .team: return "person.3"
            case .timelineEntry: return "timeline.selection"
            case .interaction: return "message"
            case .note: return "note.text"
            case .standalone: return "mic"
            }
        }
    }
    
    enum AudioQuality: String, CaseIterable, Codable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        case lossless = "lossless"
        
        var displayName: String {
            switch self {
            case .low: return "Low (32 kbps)"
            case .medium: return "Medium (64 kbps)"
            case .high: return "High (128 kbps)"
            case .lossless: return "Lossless (256 kbps)"
            }
        }
        
        var bitRate: Int {
            switch self {
            case .low: return 32000
            case .medium: return 64000
            case .high: return 128000
            case .lossless: return 256000
            }
        }
        
        var sampleRate: Double {
            switch self {
            case .low: return 22050.0
            case .medium: return 44100.0
            case .high: return 44100.0
            case .lossless: return 48000.0
            }
        }
    }
    
    init(title: String, fileName: String, filePath: String, duration: TimeInterval, fileSize: Int64, associatedEntityId: UUID? = nil, associatedEntityType: AssociatedEntityType = .standalone, quality: AudioQuality = .medium) {
        self.title = title
        self.fileName = fileName
        self.filePath = filePath
        self.duration = duration
        self.fileSize = fileSize
        self.createdAt = Date()
        self.updatedAt = Date()
        self.associatedEntityId = associatedEntityId
        self.associatedEntityType = associatedEntityType
        self.transcription = nil
        self.tags = []
        self.isBookmarked = false
        self.quality = quality
    }
    
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    var fileURL: URL {
        return URL(fileURLWithPath: filePath)
    }
}

// MARK: - Recording State

/// Represents the current state of voice recording
enum VoiceRecordingState: Equatable {
    case idle
    case preparing
    case recording(duration: TimeInterval)
    case paused(duration: TimeInterval)
    case finished(voiceNote: VoiceNote)
    case error(VoiceRecordingError)
    
    var isRecording: Bool {
        switch self {
        case .recording: return true
        default: return false
        }
    }
    
    var isPaused: Bool {
        switch self {
        case .paused: return true
        default: return false
        }
    }
    
    var canRecord: Bool {
        switch self {
        case .idle, .finished, .error: return true
        default: return false
        }
    }
    
    var canPause: Bool {
        switch self {
        case .recording: return true
        default: return false
        }
    }
    
    var canResume: Bool {
        switch self {
        case .paused: return true
        default: return false
        }
    }
    
    var currentDuration: TimeInterval {
        switch self {
        case .recording(let duration), .paused(let duration):
            return duration
        default:
            return 0
        }
    }
}

// MARK: - Playback State

/// Represents the current state of voice note playback
enum VoicePlaybackState: Equatable {
    case idle
    case loading
    case playing(currentTime: TimeInterval, duration: TimeInterval)
    case paused(currentTime: TimeInterval, duration: TimeInterval)
    case finished
    case error(VoicePlaybackError)
    
    var isPlaying: Bool {
        switch self {
        case .playing: return true
        default: return false
        }
    }
    
    var isPaused: Bool {
        switch self {
        case .paused: return true
        default: return false
        }
    }
    
    var canPlay: Bool {
        switch self {
        case .idle, .paused, .finished: return true
        default: return false
        }
    }
    
    var canPause: Bool {
        switch self {
        case .playing: return true
        default: return false
        }
    }
    
    var currentTime: TimeInterval {
        switch self {
        case .playing(let currentTime, _), .paused(let currentTime, _):
            return currentTime
        default:
            return 0
        }
    }
    
    var duration: TimeInterval {
        switch self {
        case .playing(_, let duration), .paused(_, let duration):
            return duration
        default:
            return 0
        }
    }
    
    var progress: Double {
        guard duration > 0 else { return 0 }
        return currentTime / duration
    }
}

// MARK: - Error Types

/// Errors that can occur during voice recording
enum VoiceRecordingError: LocalizedError, Equatable {
    case permissionDenied
    case audioSessionSetupFailed
    case recordingFailed(String)
    case fileCreationFailed
    case diskSpaceInsufficient
    case recordingTooShort
    case recordingTooLong
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "Microphone permission is required to record voice notes."
        case .audioSessionSetupFailed:
            return "Failed to set up audio session for recording."
        case .recordingFailed(let message):
            return "Recording failed: \(message)"
        case .fileCreationFailed:
            return "Failed to create recording file."
        case .diskSpaceInsufficient:
            return "Insufficient disk space for recording."
        case .recordingTooShort:
            return "Recording is too short. Minimum duration is 1 second."
        case .recordingTooLong:
            return "Recording is too long. Maximum duration is 10 minutes."
        case .unknown(let message):
            return "Unknown error: \(message)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .permissionDenied:
            return "Please enable microphone access in Settings > Privacy & Security > Microphone."
        case .audioSessionSetupFailed:
            return "Try closing other audio apps and try again."
        case .recordingFailed:
            return "Please try recording again."
        case .fileCreationFailed:
            return "Check available storage space and try again."
        case .diskSpaceInsufficient:
            return "Free up some storage space and try again."
        case .recordingTooShort:
            return "Record for at least 1 second."
        case .recordingTooLong:
            return "Keep recordings under 10 minutes."
        case .unknown:
            return "Please try again or restart the app."
        }
    }
}

/// Errors that can occur during voice note playback
enum VoicePlaybackError: LocalizedError, Equatable {
    case fileNotFound
    case fileCorrupted
    case audioSessionSetupFailed
    case playbackFailed(String)
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "Voice note file not found."
        case .fileCorrupted:
            return "Voice note file is corrupted or unreadable."
        case .audioSessionSetupFailed:
            return "Failed to set up audio session for playback."
        case .playbackFailed(let message):
            return "Playback failed: \(message)"
        case .unknown(let message):
            return "Unknown playback error: \(message)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .fileNotFound:
            return "The voice note may have been deleted or moved."
        case .fileCorrupted:
            return "Try recording a new voice note."
        case .audioSessionSetupFailed:
            return "Try closing other audio apps and try again."
        case .playbackFailed:
            return "Please try playing again."
        case .unknown:
            return "Please try again or restart the app."
        }
    }
}

// MARK: - Audio Settings

/// Configuration settings for voice recording
struct VoiceRecordingSettings {
    let quality: VoiceNote.AudioQuality
    let maxDuration: TimeInterval
    let minDuration: TimeInterval
    let autoStop: Bool
    let enableNoiseReduction: Bool
    let enableEchoCancellation: Bool
    
    static let `default` = VoiceRecordingSettings(
        quality: .medium,
        maxDuration: 600, // 10 minutes
        minDuration: 1, // 1 second
        autoStop: true,
        enableNoiseReduction: true,
        enableEchoCancellation: true
    )
    
    var audioSettings: [String: Any] {
        return [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: quality.sampleRate,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue,
            AVEncoderBitRateKey: quality.bitRate
        ]
    }
}

// MARK: - Waveform Data

/// Represents audio waveform data for visualization
struct WaveformData {
    let samples: [Float]
    let duration: TimeInterval
    let sampleRate: Double
    
    init(samples: [Float] = [], duration: TimeInterval = 0, sampleRate: Double = 44100) {
        self.samples = samples
        self.duration = duration
        self.sampleRate = sampleRate
    }
    
    var normalizedSamples: [Float] {
        guard !samples.isEmpty else { return [] }
        let maxSample = samples.max() ?? 1.0
        return samples.map { $0 / maxSample }
    }
    
    func sampleAt(time: TimeInterval) -> Float {
        guard duration > 0, !samples.isEmpty else { return 0 }
        let index = Int((time / duration) * Double(samples.count))
        let clampedIndex = max(0, min(samples.count - 1, index))
        return samples[clampedIndex]
    }
}

// MARK: - Voice Note Collection

/// Manages a collection of voice notes with filtering and sorting
struct VoiceNoteCollection {
    private var voiceNotes: [VoiceNote] = []
    
    var count: Int {
        return voiceNotes.count
    }
    
    var isEmpty: Bool {
        return voiceNotes.isEmpty
    }
    
    var totalDuration: TimeInterval {
        return voiceNotes.reduce(0) { $0 + $1.duration }
    }
    
    var totalFileSize: Int64 {
        return voiceNotes.reduce(0) { $0 + $1.fileSize }
    }
    
    mutating func add(_ voiceNote: VoiceNote) {
        voiceNotes.append(voiceNote)
    }
    
    mutating func remove(_ voiceNote: VoiceNote) {
        voiceNotes.removeAll { $0.id == voiceNote.id }
    }
    
    func filtered(by entityType: VoiceNote.AssociatedEntityType) -> [VoiceNote] {
        return voiceNotes.filter { $0.associatedEntityType == entityType }
    }
    
    func filtered(by entityId: UUID) -> [VoiceNote] {
        return voiceNotes.filter { $0.associatedEntityId == entityId }
    }
    
    func bookmarked() -> [VoiceNote] {
        return voiceNotes.filter { $0.isBookmarked }
    }
    
    func sorted(by sortOption: SortOption) -> [VoiceNote] {
        switch sortOption {
        case .dateCreated:
            return voiceNotes.sorted { $0.createdAt > $1.createdAt }
        case .duration:
            return voiceNotes.sorted { $0.duration > $1.duration }
        case .title:
            return voiceNotes.sorted { $0.title < $1.title }
        case .fileSize:
            return voiceNotes.sorted { $0.fileSize > $1.fileSize }
        }
    }
    
    enum SortOption: String, CaseIterable {
        case dateCreated = "dateCreated"
        case duration = "duration"
        case title = "title"
        case fileSize = "fileSize"
        
        var displayName: String {
            switch self {
            case .dateCreated: return "Date Created"
            case .duration: return "Duration"
            case .title: return "Title"
            case .fileSize: return "File Size"
            }
        }
    }
}
