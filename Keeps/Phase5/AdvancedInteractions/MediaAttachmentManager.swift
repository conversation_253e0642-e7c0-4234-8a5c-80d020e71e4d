//
//  MediaAttachmentManager.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI
import PhotosUI
import UniformTypeIdentifiers
import AVFoundation

// MARK: - Media Attachment Models

/// Represents a media attachment (photo, document, etc.)
struct MediaAttachment: Identifiable, Codable {
    let id = UUID()
    let fileName: String
    let filePath: String
    let fileSize: Int64
    let mimeType: String
    let attachmentType: AttachmentType
    let thumbnailPath: String?
    let createdAt: Date
    let associatedEntityId: UUID?
    let associatedEntityType: AssociatedEntityType
    let metadata: AttachmentMetadata
    
    enum AttachmentType: String, CaseIterable, Codable {
        case photo = "photo"
        case video = "video"
        case document = "document"
        case audio = "audio"
        case other = "other"
        
        var displayName: String {
            switch self {
            case .photo: return "Photo"
            case .video: return "Video"
            case .document: return "Document"
            case .audio: return "Audio"
            case .other: return "File"
            }
        }
        
        var icon: String {
            switch self {
            case .photo: return "photo"
            case .video: return "video"
            case .document: return "doc"
            case .audio: return "waveform"
            case .other: return "paperclip"
            }
        }
        
        var color: Color {
            switch self {
            case .photo: return .green
            case .video: return .blue
            case .document: return .orange
            case .audio: return .purple
            case .other: return .gray
            }
        }
    }
    
    enum AssociatedEntityType: String, CaseIterable, Codable {
        case person = "person"
        case team = "team"
        case timelineEntry = "timelineEntry"
        case interaction = "interaction"
        case note = "note"
        case voiceNote = "voiceNote"
        
        var displayName: String {
            switch self {
            case .person: return "Person"
            case .team: return "Team"
            case .timelineEntry: return "Timeline Entry"
            case .interaction: return "Interaction"
            case .note: return "Note"
            case .voiceNote: return "Voice Note"
            }
        }
    }
    
    var fileURL: URL {
        return URL(fileURLWithPath: filePath)
    }
    
    var thumbnailURL: URL? {
        guard let thumbnailPath = thumbnailPath else { return nil }
        return URL(fileURLWithPath: thumbnailPath)
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
}

/// Metadata for media attachments
struct AttachmentMetadata: Codable {
    let width: Int?
    let height: Int?
    let duration: TimeInterval?
    let location: LocationData?
    let cameraInfo: CameraInfo?
    let tags: [String]
    let description: String?
    
    struct LocationData: Codable {
        let latitude: Double
        let longitude: Double
        let address: String?
    }
    
    struct CameraInfo: Codable {
        let make: String?
        let model: String?
        let lensModel: String?
        let focalLength: Double?
        let aperture: Double?
        let iso: Int?
        let shutterSpeed: Double?
    }
    
    init(width: Int? = nil, height: Int? = nil, duration: TimeInterval? = nil, location: LocationData? = nil, cameraInfo: CameraInfo? = nil, tags: [String] = [], description: String? = nil) {
        self.width = width
        self.height = height
        self.duration = duration
        self.location = location
        self.cameraInfo = cameraInfo
        self.tags = tags
        self.description = description
    }
}

// MARK: - Media Attachment Manager

/// Comprehensive media attachment manager with photo/document picking, thumbnail generation, and file management
class MediaAttachmentManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var attachments: [MediaAttachment] = []
    @Published var isLoading = false
    @Published var uploadProgress: Double = 0.0
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    
    private let fileManager = FileManager.default
    private let attachmentsDirectory: URL
    private let thumbnailsDirectory: URL
    private let maxFileSize: Int64 = 100 * 1024 * 1024 // 100MB
    private let supportedImageTypes: Set<UTType> = [.jpeg, .png, .heic, .gif, .webP]
    private let supportedVideoTypes: Set<UTType> = [.movie, .quickTimeMovie, .mpeg4Movie]
    private let supportedDocumentTypes: Set<UTType> = [.pdf, .text, .rtf, .spreadsheet, .presentation]
    
    // MARK: - Initialization
    
    init() {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        attachmentsDirectory = documentsPath.appendingPathComponent("Attachments", isDirectory: true)
        thumbnailsDirectory = documentsPath.appendingPathComponent("Thumbnails", isDirectory: true)
        
        createDirectoriesIfNeeded()
        loadAttachments()
    }
    
    // MARK: - Public Methods
    
    /// Add photo attachment from PhotosPicker
    func addPhotoAttachment(from photoPickerItem: PhotosPickerItem, associatedEntityId: UUID? = nil, associatedEntityType: MediaAttachment.AssociatedEntityType = .person) async {
        await MainActor.run {
            isLoading = true
            uploadProgress = 0.0
        }
        
        do {
            guard let data = try await photoPickerItem.loadTransferable(type: Data.self) else {
                throw MediaAttachmentError.failedToLoadData
            }
            
            await MainActor.run {
                uploadProgress = 0.3
            }
            
            let attachment = try await createPhotoAttachment(
                from: data,
                fileName: photoPickerItem.itemIdentifier ?? "photo_\(Date().timeIntervalSince1970).jpg",
                associatedEntityId: associatedEntityId,
                associatedEntityType: associatedEntityType
            )
            
            await MainActor.run {
                uploadProgress = 1.0
                attachments.append(attachment)
                saveAttachments()
                isLoading = false
            }
            
        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
                isLoading = false
            }
        }
    }
    
    /// Add document attachment from DocumentPicker
    func addDocumentAttachment(from url: URL, associatedEntityId: UUID? = nil, associatedEntityType: MediaAttachment.AssociatedEntityType = .person) async {
        await MainActor.run {
            isLoading = true
            uploadProgress = 0.0
        }
        
        do {
            // Start accessing security-scoped resource
            guard url.startAccessingSecurityScopedResource() else {
                throw MediaAttachmentError.accessDenied
            }
            defer { url.stopAccessingSecurityScopedResource() }
            
            let data = try Data(contentsOf: url)
            
            await MainActor.run {
                uploadProgress = 0.5
            }
            
            let attachment = try await createDocumentAttachment(
                from: data,
                fileName: url.lastPathComponent,
                mimeType: url.mimeType,
                associatedEntityId: associatedEntityId,
                associatedEntityType: associatedEntityType
            )
            
            await MainActor.run {
                uploadProgress = 1.0
                attachments.append(attachment)
                saveAttachments()
                isLoading = false
            }
            
        } catch {
            await MainActor.run {
                errorMessage = error.localizedDescription
                isLoading = false
            }
        }
    }
    
    /// Remove attachment
    func removeAttachment(_ attachment: MediaAttachment) {
        // Remove files
        try? fileManager.removeItem(at: attachment.fileURL)
        if let thumbnailURL = attachment.thumbnailURL {
            try? fileManager.removeItem(at: thumbnailURL)
        }
        
        // Remove from array
        attachments.removeAll { $0.id == attachment.id }
        saveAttachments()
    }
    
    /// Get attachments for specific entity
    func attachments(for entityId: UUID, type: MediaAttachment.AssociatedEntityType) -> [MediaAttachment] {
        return attachments.filter { 
            $0.associatedEntityId == entityId && $0.associatedEntityType == type 
        }
    }
    
    /// Get attachments by type
    func attachments(ofType type: MediaAttachment.AttachmentType) -> [MediaAttachment] {
        return attachments.filter { $0.attachmentType == type }
    }
    
    /// Generate thumbnail for attachment
    func generateThumbnail(for attachment: MediaAttachment) async -> UIImage? {
        switch attachment.attachmentType {
        case .photo:
            return await generatePhotoThumbnail(for: attachment)
        case .video:
            return await generateVideoThumbnail(for: attachment)
        case .document:
            return await generateDocumentThumbnail(for: attachment)
        default:
            return nil
        }
    }
    
    // MARK: - Private Methods
    
    private func createDirectoriesIfNeeded() {
        try? fileManager.createDirectory(at: attachmentsDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: thumbnailsDirectory, withIntermediateDirectories: true)
    }
    
    private func createPhotoAttachment(from data: Data, fileName: String, associatedEntityId: UUID?, associatedEntityType: MediaAttachment.AssociatedEntityType) async throws -> MediaAttachment {
        // Validate file size
        guard data.count <= maxFileSize else {
            throw MediaAttachmentError.fileTooLarge
        }
        
        // Create unique file name
        let uniqueFileName = "\(UUID().uuidString)_\(fileName)"
        let fileURL = attachmentsDirectory.appendingPathComponent(uniqueFileName)
        
        // Save file
        try data.write(to: fileURL)
        
        // Extract metadata
        let image = UIImage(data: data)
        let metadata = AttachmentMetadata(
            width: Int(image?.size.width ?? 0),
            height: Int(image?.size.height ?? 0)
        )
        
        // Generate thumbnail
        let thumbnailPath = try await generateAndSaveThumbnail(from: data, fileName: uniqueFileName)
        
        return MediaAttachment(
            fileName: uniqueFileName,
            filePath: fileURL.path,
            fileSize: Int64(data.count),
            mimeType: "image/jpeg",
            attachmentType: .photo,
            thumbnailPath: thumbnailPath,
            createdAt: Date(),
            associatedEntityId: associatedEntityId,
            associatedEntityType: associatedEntityType,
            metadata: metadata
        )
    }
    
    private func createDocumentAttachment(from data: Data, fileName: String, mimeType: String, associatedEntityId: UUID?, associatedEntityType: MediaAttachment.AssociatedEntityType) async throws -> MediaAttachment {
        // Validate file size
        guard data.count <= maxFileSize else {
            throw MediaAttachmentError.fileTooLarge
        }
        
        // Create unique file name
        let uniqueFileName = "\(UUID().uuidString)_\(fileName)"
        let fileURL = attachmentsDirectory.appendingPathComponent(uniqueFileName)
        
        // Save file
        try data.write(to: fileURL)
        
        // Determine attachment type
        let attachmentType = determineAttachmentType(from: mimeType)
        
        // Create metadata
        let metadata = AttachmentMetadata()
        
        return MediaAttachment(
            fileName: uniqueFileName,
            filePath: fileURL.path,
            fileSize: Int64(data.count),
            mimeType: mimeType,
            attachmentType: attachmentType,
            thumbnailPath: nil,
            createdAt: Date(),
            associatedEntityId: associatedEntityId,
            associatedEntityType: associatedEntityType,
            metadata: metadata
        )
    }
    
    private func generateAndSaveThumbnail(from data: Data, fileName: String) async throws -> String {
        guard let image = UIImage(data: data) else {
            throw MediaAttachmentError.failedToGenerateThumbnail
        }
        
        let thumbnailSize = CGSize(width: 200, height: 200)
        let thumbnail = image.resized(to: thumbnailSize)
        
        guard let thumbnailData = thumbnail.jpegData(compressionQuality: 0.8) else {
            throw MediaAttachmentError.failedToGenerateThumbnail
        }
        
        let thumbnailFileName = "thumb_\(fileName)"
        let thumbnailURL = thumbnailsDirectory.appendingPathComponent(thumbnailFileName)
        
        try thumbnailData.write(to: thumbnailURL)
        
        return thumbnailURL.path
    }
    
    private func generatePhotoThumbnail(for attachment: MediaAttachment) async -> UIImage? {
        guard let data = try? Data(contentsOf: attachment.fileURL),
              let image = UIImage(data: data) else {
            return nil
        }
        
        let thumbnailSize = CGSize(width: 200, height: 200)
        return image.resized(to: thumbnailSize)
    }
    
    private func generateVideoThumbnail(for attachment: MediaAttachment) async -> UIImage? {
        let asset = AVURLAsset(url: attachment.fileURL)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        
        do {
            let cgImage = try imageGenerator.copyCGImage(at: .zero, actualTime: nil)
            return UIImage(cgImage: cgImage)
        } catch {
            return nil
        }
    }
    
    private func generateDocumentThumbnail(for attachment: MediaAttachment) async -> UIImage? {
        // For PDF documents, generate thumbnail from first page
        if attachment.mimeType == "application/pdf" {
            return generatePDFThumbnail(for: attachment)
        }
        
        // For other documents, return a generic icon
        return UIImage(systemName: attachment.attachmentType.icon)
    }
    
    private func generatePDFThumbnail(for attachment: MediaAttachment) -> UIImage? {
        guard let document = CGPDFDocument(attachment.fileURL as CFURL),
              let page = document.page(at: 1) else {
            return nil
        }
        
        let pageRect = page.getBoxRect(.mediaBox)
        let renderer = UIGraphicsImageRenderer(size: pageRect.size)
        
        return renderer.image { context in
            UIColor.white.set()
            context.fill(pageRect)
            
            context.cgContext.translateBy(x: 0.0, y: pageRect.size.height)
            context.cgContext.scaleBy(x: 1.0, y: -1.0)
            context.cgContext.drawPDFPage(page)
        }
    }
    
    private func determineAttachmentType(from mimeType: String) -> MediaAttachment.AttachmentType {
        if mimeType.hasPrefix("image/") {
            return .photo
        } else if mimeType.hasPrefix("video/") {
            return .video
        } else if mimeType.hasPrefix("audio/") {
            return .audio
        } else if mimeType == "application/pdf" || mimeType.contains("document") || mimeType.contains("text") {
            return .document
        } else {
            return .other
        }
    }
    
    private func loadAttachments() {
        // In a real implementation, this would load from Core Data or persistent storage
        // For now, we'll start with an empty array
        attachments = []
    }
    
    private func saveAttachments() {
        // In a real implementation, this would save to Core Data or persistent storage
        // For now, we'll just keep them in memory
    }
}

// MARK: - Media Attachment Errors

enum MediaAttachmentError: LocalizedError {
    case failedToLoadData
    case fileTooLarge
    case unsupportedFileType
    case accessDenied
    case failedToGenerateThumbnail
    case storageError
    
    var errorDescription: String? {
        switch self {
        case .failedToLoadData:
            return "Failed to load file data"
        case .fileTooLarge:
            return "File is too large (maximum 100MB)"
        case .unsupportedFileType:
            return "Unsupported file type"
        case .accessDenied:
            return "Access to file denied"
        case .failedToGenerateThumbnail:
            return "Failed to generate thumbnail"
        case .storageError:
            return "Storage error occurred"
        }
    }
}

// MARK: - Extensions

extension URL {
    var mimeType: String {
        if let uti = try? resourceValues(forKeys: [.typeIdentifierKey]).typeIdentifier,
           let mimeType = UTType(uti)?.preferredMIMEType {
            return mimeType
        }
        return "application/octet-stream"
    }
}

extension UIImage {
    func resized(to size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: size))
        }
    }
}
