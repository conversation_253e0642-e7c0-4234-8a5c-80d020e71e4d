//
//  VoiceNotePlayer.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import AVFoundation
import SwiftUI
import Combine

/// Advanced voice note player with precise playback control and real-time visualization
class VoiceNotePlayer: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var playbackState: VoicePlaybackState = .idle
    @Published var currentVoiceNote: VoiceNote?
    @Published var playbackSpeed: Float = 1.0
    @Published var volume: Float = 1.0
    @Published var isLooping = false
    @Published var waveformData: WaveformData = WaveformData()
    
    // MARK: - Private Properties
    
    private var audioPlayer: AVAudioPlayer?
    private var audioSession: AVAudioSession = AVAudioSession.sharedInstance()
    private var playbackTimer: Timer?
    private var displayLink: CADisplayLink?
    
    // Playback control
    private var targetTime: TimeInterval?
    private var fadeInDuration: TimeInterval = 0.1
    private var fadeOutDuration: TimeInterval = 0.1
    
    // MARK: - Constants
    
    private let playbackUpdateInterval: TimeInterval = 0.02 // 50 FPS for smooth updates
    private let supportedSpeeds: [Float] = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
    
    // MARK: - Initialization
    
    override init() {
        super.init()
        setupAudioSession()
        setupDisplayLink()
    }
    
    deinit {
        stopPlayback()
        displayLink?.invalidate()
    }
    
    // MARK: - Public Methods
    
    /// Load and prepare a voice note for playback
    func loadVoiceNote(_ voiceNote: VoiceNote) async {
        await MainActor.run {
            playbackState = .loading
            currentVoiceNote = voiceNote
        }
        
        do {
            try await prepareAudioPlayer(for: voiceNote)
            await generateWaveformData(for: voiceNote)
            
            await MainActor.run {
                playbackState = .idle
            }
        } catch {
            await MainActor.run {
                playbackState = .error(mapError(error))
            }
        }
    }
    
    /// Start or resume playback
    func play() {
        guard let player = audioPlayer else { return }
        
        switch playbackState {
        case .idle, .paused:
            player.play()
            startPlaybackTimer()
            updatePlaybackState()
            
        case .finished:
            // Restart from beginning
            player.currentTime = 0
            player.play()
            startPlaybackTimer()
            updatePlaybackState()
            
        default:
            break
        }
    }
    
    /// Pause playback
    func pause() {
        guard playbackState.isPlaying else { return }
        
        audioPlayer?.pause()
        stopPlaybackTimer()
        updatePlaybackState()
    }
    
    /// Stop playback and reset to beginning
    func stop() {
        audioPlayer?.stop()
        audioPlayer?.currentTime = 0
        stopPlaybackTimer()
        playbackState = .idle
    }
    
    /// Seek to specific time
    func seek(to time: TimeInterval) {
        guard let player = audioPlayer else { return }
        
        let clampedTime = max(0, min(time, player.duration))
        player.currentTime = clampedTime
        updatePlaybackState()
    }
    
    /// Seek by relative amount (positive for forward, negative for backward)
    func seek(by interval: TimeInterval) {
        guard let player = audioPlayer else { return }
        
        let newTime = player.currentTime + interval
        seek(to: newTime)
    }
    
    /// Skip forward by 15 seconds
    func skipForward() {
        seek(by: 15.0)
    }
    
    /// Skip backward by 15 seconds
    func skipBackward() {
        seek(by: -15.0)
    }
    
    /// Set playback speed
    func setPlaybackSpeed(_ speed: Float) {
        guard supportedSpeeds.contains(speed) else { return }
        
        playbackSpeed = speed
        audioPlayer?.rate = speed
        
        // Enable rate change if not already enabled
        audioPlayer?.enableRate = true
    }
    
    /// Set volume with optional fade
    func setVolume(_ volume: Float, animated: Bool = false) {
        let clampedVolume = max(0.0, min(1.0, volume))
        self.volume = clampedVolume
        
        if animated {
            fadeVolume(to: clampedVolume)
        } else {
            audioPlayer?.volume = clampedVolume
        }
    }
    
    /// Toggle looping mode
    func toggleLooping() {
        isLooping.toggle()
        audioPlayer?.numberOfLoops = isLooping ? -1 : 0
    }
    
    /// Get current playback progress (0.0 - 1.0)
    var playbackProgress: Double {
        guard let player = audioPlayer, player.duration > 0 else { return 0 }
        return player.currentTime / player.duration
    }
    
    /// Get formatted current time string
    var formattedCurrentTime: String {
        guard let player = audioPlayer else { return "0:00" }
        return formatTime(player.currentTime)
    }
    
    /// Get formatted duration string
    var formattedDuration: String {
        guard let player = audioPlayer else { return "0:00" }
        return formatTime(player.duration)
    }
    
    /// Get formatted remaining time string
    var formattedRemainingTime: String {
        guard let player = audioPlayer else { return "0:00" }
        let remaining = player.duration - player.currentTime
        return "-\(formatTime(remaining))"
    }
    
    // MARK: - Private Methods
    
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playback, mode: .default, options: [.allowBluetooth, .allowAirPlay])
            try audioSession.setActive(true)
        } catch {
            print("Failed to set up audio session for playback: \(error)")
        }
    }
    
    private func setupDisplayLink() {
        displayLink = CADisplayLink(target: self, selector: #selector(updatePlaybackDisplay))
        displayLink?.preferredFramesPerSecond = 60
        displayLink?.add(to: .main, forMode: .common)
        displayLink?.isPaused = true
    }
    
    private func prepareAudioPlayer(for voiceNote: VoiceNote) async throws {
        let url = voiceNote.fileURL
        
        guard FileManager.default.fileExists(atPath: url.path) else {
            throw VoicePlaybackError.fileNotFound
        }
        
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            audioPlayer?.enableRate = true
            audioPlayer?.rate = playbackSpeed
            audioPlayer?.volume = volume
            audioPlayer?.numberOfLoops = isLooping ? -1 : 0
        } catch {
            throw VoicePlaybackError.playbackFailed(error.localizedDescription)
        }
    }
    
    private func generateWaveformData(for voiceNote: VoiceNote) async {
        // In a real implementation, this would analyze the audio file
        // and generate actual waveform data. For now, we'll create sample data.
        let sampleCount = 200
        let samples = (0..<sampleCount).map { _ in Float.random(in: 0.1...1.0) }
        
        await MainActor.run {
            self.waveformData = WaveformData(
                samples: samples,
                duration: voiceNote.duration,
                sampleRate: voiceNote.quality.sampleRate
            )
        }
    }
    
    private func startPlaybackTimer() {
        stopPlaybackTimer()
        
        playbackTimer = Timer.scheduledTimer(withTimeInterval: playbackUpdateInterval, repeats: true) { _ in
            self.updatePlaybackState()
        }
        
        displayLink?.isPaused = false
    }
    
    private func stopPlaybackTimer() {
        playbackTimer?.invalidate()
        playbackTimer = nil
        displayLink?.isPaused = true
    }
    
    @objc private func updatePlaybackDisplay() {
        // High-frequency updates for smooth UI animations
        guard playbackState.isPlaying else { return }
        updatePlaybackState()
    }
    
    private func updatePlaybackState() {
        guard let player = audioPlayer else { return }
        
        let currentTime = player.currentTime
        let duration = player.duration
        
        if player.isPlaying {
            playbackState = .playing(currentTime: currentTime, duration: duration)
        } else {
            if currentTime >= duration - 0.1 { // Near end
                playbackState = .finished
            } else {
                playbackState = .paused(currentTime: currentTime, duration: duration)
            }
        }
    }
    
    private func fadeVolume(to targetVolume: Float) {
        guard let player = audioPlayer else { return }
        
        let startVolume = player.volume
        let volumeDifference = targetVolume - startVolume
        let steps = 20
        let stepDuration = fadeInDuration / Double(steps)
        
        var currentStep = 0
        
        Timer.scheduledTimer(withTimeInterval: stepDuration, repeats: true) { timer in
            currentStep += 1
            let progress = Float(currentStep) / Float(steps)
            let newVolume = startVolume + (volumeDifference * progress)
            
            player.volume = newVolume
            
            if currentStep >= steps {
                timer.invalidate()
                player.volume = targetVolume
            }
        }
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let totalSeconds = Int(time)
        let minutes = totalSeconds / 60
        let seconds = totalSeconds % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    private func mapError(_ error: Error) -> VoicePlaybackError {
        if let playbackError = error as? VoicePlaybackError {
            return playbackError
        }
        
        if let avError = error as? AVError {
            switch avError.code {
            case .fileFormatNotRecognized:
                return .fileCorrupted
            case .mediaServicesWereReset:
                return .audioSessionSetupFailed
            default:
                return .playbackFailed(avError.localizedDescription)
            }
        }
        
        return .unknown(error.localizedDescription)
    }
    
    private func stopPlayback() {
        audioPlayer?.stop()
        stopPlaybackTimer()
        playbackState = .idle
    }
}

// MARK: - AVAudioPlayerDelegate

extension VoiceNotePlayer: AVAudioPlayerDelegate {
    
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        stopPlaybackTimer()
        
        if flag {
            if isLooping {
                // Restart playback for looping
                player.currentTime = 0
                player.play()
                startPlaybackTimer()
                updatePlaybackState()
            } else {
                playbackState = .finished
            }
        } else {
            playbackState = .error(.playbackFailed("Playback finished unsuccessfully"))
        }
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        stopPlaybackTimer()
        
        let playbackError = error.map(mapError) ?? .unknown("Unknown decode error")
        playbackState = .error(playbackError)
    }
}

// MARK: - Audio Session Notifications

extension VoiceNotePlayer {
    
    private func setupAudioSessionNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(audioSessionInterruption),
            name: AVAudioSession.interruptionNotification,
            object: audioSession
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(audioSessionRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: audioSession
        )
    }
    
    @objc private func audioSessionInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            if playbackState.isPlaying {
                pause()
            }
        case .ended:
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) && playbackState.isPaused {
                    play()
                }
            }
        @unknown default:
            break
        }
    }
    
    @objc private func audioSessionRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        
        switch reason {
        case .oldDeviceUnavailable:
            // Audio device was disconnected (e.g., headphones unplugged)
            if playbackState.isPlaying {
                pause()
            }
        case .newDeviceAvailable:
            // New audio device connected - could auto-resume if desired
            break
        default:
            break
        }
    }
}
