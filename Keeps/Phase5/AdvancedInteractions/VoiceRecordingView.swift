//
//  VoiceRecordingView.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import SwiftUI
import SwiftUIX

/// Revolutionary voice recording interface with real-time waveform visualization and intuitive controls
struct VoiceRecordingView: View {
    
    // MARK: - Properties
    
    @StateObject private var recorder = VoiceNoteRecorder()
    @State private var showingPermissionAlert = false
    @State private var showingErrorAlert = false
    @State private var errorMessage = ""
    @State private var recordingTitle = ""
    @State private var showingSaveDialog = false
    @State private var isMinimized = false
    
    // Animation states
    @State private var pulseAnimation = false
    @State private var waveformAnimation = false
    @State private var recordButtonScale: CGFloat = 1.0
    
    // Callbacks
    let onVoiceNoteSaved: ((VoiceNote) -> Void)?
    let onCancel: (() -> Void)?
    
    // MARK: - Initialization
    
    init(onVoiceNoteSaved: ((VoiceNote) -> Void)? = nil, onCancel: (() -> Void)? = nil) {
        self.onVoiceNoteSaved = onVoiceNoteSaved
        self.onCancel = onCancel
    }
    
    var body: some View {
        ZStack {
            // Background
            backgroundGradient
            
            VStack(spacing: 0) {
                if !isMinimized {
                    // Header
                    headerSection
                    
                    Spacer()
                    
                    // Waveform Visualization
                    waveformSection
                    
                    Spacer()
                    
                    // Recording Info
                    recordingInfoSection
                    
                    Spacer()
                }
                
                // Controls
                controlsSection
                    .padding(.bottom, isMinimized ? 20 : 40)
            }
            .padding(.horizontal, 20)
            .padding(.top, isMinimized ? 10 : 20)
        }
        .alert("Microphone Permission Required", isPresented: $showingPermissionAlert) {
            Button("Settings") {
                openAppSettings()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Please enable microphone access in Settings to record voice notes.")
        }
        .alert("Recording Error", isPresented: $showingErrorAlert) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .sheet(isPresented: $showingSaveDialog) {
            if case .finished(let voiceNote) = recorder.recordingState {
                SaveVoiceNoteView(voiceNote: voiceNote) { savedNote in
                    onVoiceNoteSaved?(savedNote)
                    showingSaveDialog = false
                }
            }
        }
        .onReceive(recorder.$recordingState) { state in
            handleRecordingStateChange(state)
        }
        .onAppear {
            startAnimations()
        }
    }
    
    // MARK: - Background
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color.black.opacity(0.9),
                Color.blue.opacity(0.3),
                Color.purple.opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        .overlay(
            // Animated particles
            ForEach(0..<20, id: \.self) { _ in
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: CGFloat.random(in: 2...6))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
                    .animation(
                        .easeInOut(duration: Double.random(in: 3...8))
                        .repeatForever(autoreverses: true),
                        value: pulseAnimation
                    )
            }
        )
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        HStack {
            Button(action: cancelRecording) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(Color.white.opacity(0.2)))
            }
            
            Spacer()
            
            Text("Voice Note")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Spacer()
            
            Button(action: { isMinimized.toggle() }) {
                Image(systemName: isMinimized ? "arrow.up.right.and.arrow.down.left" : "arrow.down.left.and.arrow.up.right")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(Color.white.opacity(0.2)))
            }
        }
    }
    
    // MARK: - Waveform Section
    
    private var waveformSection: some View {
        VStack(spacing: 20) {
            // Audio Level Indicator
            HStack(spacing: 4) {
                ForEach(0..<40, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(
                            LinearGradient(
                                colors: [.green, .yellow, .red],
                                startPoint: .bottom,
                                endPoint: .top
                            )
                        )
                        .frame(width: 3, height: audioLevelHeight(for: index))
                        .animation(
                            .easeInOut(duration: 0.1),
                            value: recorder.audioLevel
                        )
                }
            }
            .frame(height: 60)
            
            // Waveform Visualization
            WaveformVisualizationView(
                waveformData: recorder.currentWaveform,
                isRecording: recorder.recordingState.isRecording,
                currentTime: recorder.recordingDuration
            )
            .frame(height: 120)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Recording Info Section
    
    private var recordingInfoSection: some View {
        VStack(spacing: 16) {
            // Recording Duration
            Text(formatDuration(recorder.recordingDuration))
                .font(.system(size: 48, weight: .light, design: .monospaced))
                .foregroundColor(.white)
                .contentTransition(.numericText())
            
            // Recording State
            Text(recordingStateText)
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .animation(.easeInOut, value: recorder.recordingState)
            
            // Recording Quality Indicator
            HStack(spacing: 8) {
                Image(systemName: "waveform")
                    .foregroundColor(.white.opacity(0.6))
                
                Text("High Quality")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))
            }
        }
    }
    
    // MARK: - Controls Section
    
    private var controlsSection: some View {
        HStack(spacing: isMinimized ? 20 : 40) {
            if !isMinimized {
                // Delete/Cancel Button
                Button(action: cancelRecording) {
                    Image(systemName: "trash")
                        .font(.title2)
                        .foregroundColor(.red)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .overlay(
                                    Circle()
                                        .stroke(Color.red.opacity(0.5), lineWidth: 2)
                                )
                        )
                }
                .disabled(!canCancel)
                .opacity(canCancel ? 1.0 : 0.5)
            }
            
            // Main Record Button
            Button(action: toggleRecording) {
                ZStack {
                    // Outer pulse ring
                    Circle()
                        .stroke(Color.red.opacity(0.3), lineWidth: 4)
                        .scaleEffect(recorder.recordingState.isRecording ? 1.3 : 1.0)
                        .opacity(recorder.recordingState.isRecording ? 0.0 : 1.0)
                        .animation(
                            .easeInOut(duration: 1.0)
                            .repeatForever(autoreverses: false),
                            value: recorder.recordingState.isRecording
                        )
                    
                    // Main button
                    Circle()
                        .fill(
                            recorder.recordingState.isRecording
                            ? Color.red
                            : Color.white.opacity(0.9)
                        )
                        .frame(width: isMinimized ? 50 : 80, height: isMinimized ? 50 : 80)
                        .overlay(
                            Image(systemName: recordButtonIcon)
                                .font(.system(size: isMinimized ? 20 : 28, weight: .medium))
                                .foregroundColor(
                                    recorder.recordingState.isRecording
                                    ? .white
                                    : .red
                                )
                        )
                        .scaleEffect(recordButtonScale)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: recordButtonScale)
                }
            }
            .disabled(!canRecord)
            .onTapGesture {
                recordButtonScale = 0.9
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    recordButtonScale = 1.0
                }
            }
            
            if !isMinimized {
                // Save/Stop Button
                Button(action: stopRecording) {
                    Image(systemName: "checkmark")
                        .font(.title2)
                        .foregroundColor(.green)
                        .frame(width: 60, height: 60)
                        .background(
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .overlay(
                                    Circle()
                                        .stroke(Color.green.opacity(0.5), lineWidth: 2)
                                )
                        )
                }
                .disabled(!canStop)
                .opacity(canStop ? 1.0 : 0.5)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var recordingStateText: String {
        switch recorder.recordingState {
        case .idle:
            return "Tap to start recording"
        case .preparing:
            return "Preparing..."
        case .recording:
            return "Recording..."
        case .paused:
            return "Paused"
        case .finished:
            return "Recording complete"
        case .error:
            return "Error occurred"
        }
    }
    
    private var recordButtonIcon: String {
        switch recorder.recordingState {
        case .idle, .finished, .error:
            return "mic"
        case .preparing:
            return "ellipsis"
        case .recording:
            return "pause"
        case .paused:
            return "play"
        }
    }
    
    private var canRecord: Bool {
        return recorder.isPermissionGranted && recorder.recordingState.canRecord
    }
    
    private var canStop: Bool {
        return recorder.recordingState.isRecording || recorder.recordingState.isPaused
    }
    
    private var canCancel: Bool {
        return recorder.recordingState != .idle
    }
    
    // MARK: - Helper Methods
    
    private func audioLevelHeight(for index: Int) -> CGFloat {
        let normalizedLevel = CGFloat(recorder.audioLevel)
        let barThreshold = CGFloat(index) / 40.0
        
        if normalizedLevel > barThreshold {
            return 4 + (normalizedLevel * 56) // 4-60 range
        } else {
            return 4
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    private func startAnimations() {
        withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
            pulseAnimation = true
        }
    }
    
    private func toggleRecording() {
        switch recorder.recordingState {
        case .idle, .finished, .error:
            if recorder.isPermissionGranted {
                Task {
                    await recorder.startRecording()
                }
            } else {
                showingPermissionAlert = true
            }
            
        case .recording:
            recorder.pauseRecording()
            
        case .paused:
            recorder.resumeRecording()
            
        default:
            break
        }
    }
    
    private func stopRecording() {
        Task {
            await recorder.stopRecording()
        }
    }
    
    private func cancelRecording() {
        recorder.cancelRecording()
        onCancel?()
    }
    
    private func handleRecordingStateChange(_ state: VoiceRecordingState) {
        switch state {
        case .finished:
            showingSaveDialog = true
            
        case .error(let error):
            errorMessage = error.localizedDescription
            showingErrorAlert = true
            
        default:
            break
        }
    }
    
    private func openAppSettings() {
        if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsURL)
        }
    }
}

// MARK: - Waveform Visualization View

struct WaveformVisualizationView: View {
    let waveformData: WaveformData
    let isRecording: Bool
    let currentTime: TimeInterval
    
    var body: some View {
        GeometryReader { geometry in
            Canvas { context, size in
                drawWaveform(context: context, size: size)
            }
        }
        .animation(.easeInOut(duration: 0.1), value: waveformData.samples)
    }
    
    private func drawWaveform(context: GraphicsContext, size: CGSize) {
        let samples = waveformData.normalizedSamples
        guard !samples.isEmpty else { return }
        
        let barWidth = size.width / CGFloat(samples.count)
        let centerY = size.height / 2
        
        for (index, sample) in samples.enumerated() {
            let x = CGFloat(index) * barWidth
            let barHeight = CGFloat(sample) * size.height * 0.8
            
            let rect = CGRect(
                x: x,
                y: centerY - barHeight / 2,
                width: max(1, barWidth - 1),
                height: barHeight
            )
            
            // Color based on recording state and position
            let color: Color
            if isRecording {
                let progress = currentTime / waveformData.duration
                let sampleProgress = Double(index) / Double(samples.count)
                
                if sampleProgress <= progress {
                    color = .red
                } else {
                    color = .white.opacity(0.3)
                }
            } else {
                color = .white.opacity(0.6)
            }
            
            context.fill(
                Path(rect),
                with: .color(color)
            )
        }
    }
}

// MARK: - Save Voice Note View

struct SaveVoiceNoteView: View {
    let voiceNote: VoiceNote
    let onSave: (VoiceNote) -> Void
    
    @State private var title: String
    @State private var tags: String = ""
    @State private var isBookmarked = false
    @Environment(\.dismiss) private var dismiss
    
    init(voiceNote: VoiceNote, onSave: @escaping (VoiceNote) -> Void) {
        self.voiceNote = voiceNote
        self.onSave = onSave
        self._title = State(initialValue: voiceNote.title)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Voice Note Preview
                VStack(spacing: 12) {
                    Image(systemName: "waveform")
                        .font(.system(size: 40))
                        .foregroundColor(.blue)
                    
                    Text(voiceNote.formattedDuration)
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text(voiceNote.formattedFileSize)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                )
                
                // Title Input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Title")
                        .font(.headline)
                    
                    TextField("Enter title", text: $title)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // Tags Input
                VStack(alignment: .leading, spacing: 8) {
                    Text("Tags (optional)")
                        .font(.headline)
                    
                    TextField("Enter tags separated by commas", text: $tags)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // Bookmark Toggle
                Toggle("Bookmark this voice note", isOn: $isBookmarked)
                
                Spacer()
            }
            .padding(20)
            .navigationTitle("Save Voice Note")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveVoiceNote()
                    }
                    .disabled(title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
    
    private func saveVoiceNote() {
        // Create updated voice note with user input
        var updatedNote = voiceNote
        // In a real implementation, we would update the voice note properties
        // For now, we'll just call the save callback
        onSave(updatedNote)
        dismiss()
    }
}

// MARK: - Preview

struct VoiceRecordingView_Previews: PreviewProvider {
    static var previews: some View {
        VoiceRecordingView()
    }
}
