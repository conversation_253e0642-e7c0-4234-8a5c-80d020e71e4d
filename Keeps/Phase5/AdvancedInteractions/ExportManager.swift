//
//  ExportManager.swift
//  Keeps
//
//  Created by SwiftMaster on Phase 5 Implementation
//

import Foundation
import SwiftUI
import PDFKit
import UniformTypeIdentifiers

// MARK: - Export Models

/// Represents an export configuration
struct ExportConfiguration {
    let format: ExportFormat
    let includePhotos: Bool
    let includeVoiceNotes: Bool
    let includeAttachments: Bool
    let dateRange: DateInterval?
    let entities: Set<ExportEntity>
    let compressionLevel: CompressionLevel
    let password: String?
    
    enum ExportFormat: String, CaseIterable {
        case json = "json"
        case csv = "csv"
        case pdf = "pdf"
        case html = "html"
        case markdown = "markdown"
        case zip = "zip"
        
        var displayName: String {
            switch self {
            case .json: return "JSON"
            case .csv: return "CSV"
            case .pdf: return "PDF"
            case .html: return "HTML"
            case .markdown: return "Markdown"
            case .zip: return "ZIP Archive"
            }
        }
        
        var fileExtension: String {
            return rawValue
        }
        
        var mimeType: String {
            switch self {
            case .json: return "application/json"
            case .csv: return "text/csv"
            case .pdf: return "application/pdf"
            case .html: return "text/html"
            case .markdown: return "text/markdown"
            case .zip: return "application/zip"
            }
        }
        
        var utType: UTType {
            switch self {
            case .json: return .json
            case .csv: return .commaSeparatedText
            case .pdf: return .pdf
            case .html: return .html
            case .markdown: return .plainText
            case .zip: return .zip
            }
        }
    }
    
    enum ExportEntity: String, CaseIterable {
        case people = "people"
        case teams = "teams"
        case timeline = "timeline"
        case interactions = "interactions"
        case voiceNotes = "voiceNotes"
        case attachments = "attachments"
        case analytics = "analytics"
        
        var displayName: String {
            switch self {
            case .people: return "People"
            case .teams: return "Teams"
            case .timeline: return "Timeline"
            case .interactions: return "Interactions"
            case .voiceNotes: return "Voice Notes"
            case .attachments: return "Attachments"
            case .analytics: return "Analytics"
            }
        }
        
        var icon: String {
            switch self {
            case .people: return "person.2"
            case .teams: return "person.3"
            case .timeline: return "timeline.selection"
            case .interactions: return "message"
            case .voiceNotes: return "waveform"
            case .attachments: return "paperclip"
            case .analytics: return "chart.bar"
            }
        }
    }
    
    enum CompressionLevel: String, CaseIterable {
        case none = "none"
        case low = "low"
        case medium = "medium"
        case high = "high"
        
        var displayName: String {
            switch self {
            case .none: return "No Compression"
            case .low: return "Low Compression"
            case .medium: return "Medium Compression"
            case .high: return "High Compression"
            }
        }
        
        var compressionRatio: Float {
            switch self {
            case .none: return 1.0
            case .low: return 0.8
            case .medium: return 0.6
            case .high: return 0.4
            }
        }
    }
    
    static let `default` = ExportConfiguration(
        format: .json,
        includePhotos: true,
        includeVoiceNotes: true,
        includeAttachments: true,
        dateRange: nil,
        entities: Set(ExportEntity.allCases),
        compressionLevel: .medium,
        password: nil
    )
}

/// Represents an export result
struct ExportResult {
    let fileURL: URL
    let fileName: String
    let fileSize: Int64
    let format: ExportConfiguration.ExportFormat
    let createdAt: Date
    let configuration: ExportConfiguration
    let metadata: ExportMetadata
    
    struct ExportMetadata {
        let totalRecords: Int
        let includedEntities: [ExportConfiguration.ExportEntity]
        let dateRange: DateInterval?
        let exportDuration: TimeInterval
        let compressionRatio: Double?
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
}

// MARK: - Export Manager

/// Comprehensive data export manager with multiple format support and sharing capabilities
class ExportManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isExporting = false
    @Published var exportProgress: Double = 0.0
    @Published var currentExportStep = ""
    @Published var exportHistory: [ExportResult] = []
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    
    private let fileManager = FileManager.default
    private let exportsDirectory: URL
    private let tempDirectory: URL
    
    // MARK: - Initialization
    
    init() {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        exportsDirectory = documentsPath.appendingPathComponent("Exports", isDirectory: true)
        tempDirectory = fileManager.temporaryDirectory.appendingPathComponent("KeepsExports", isDirectory: true)
        
        createDirectoriesIfNeeded()
        loadExportHistory()
    }
    
    // MARK: - Public Methods
    
    /// Export data with the specified configuration
    func exportData(configuration: ExportConfiguration) async -> ExportResult? {
        await MainActor.run {
            isExporting = true
            exportProgress = 0.0
            currentExportStep = "Preparing export..."
        }
        
        let startTime = Date()
        
        do {
            // Step 1: Gather data (30%)
            await updateProgress(0.1, "Gathering data...")
            let exportData = try await gatherExportData(configuration: configuration)
            
            // Step 2: Process data (60%)
            await updateProgress(0.3, "Processing data...")
            let processedData = try await processExportData(exportData, configuration: configuration)
            
            // Step 3: Generate file (90%)
            await updateProgress(0.6, "Generating export file...")
            let fileURL = try await generateExportFile(processedData, configuration: configuration)
            
            // Step 4: Finalize (100%)
            await updateProgress(0.9, "Finalizing export...")
            let result = try await finalizeExport(fileURL: fileURL, configuration: configuration, startTime: startTime, totalRecords: exportData.totalRecords)
            
            await MainActor.run {
                exportProgress = 1.0
                currentExportStep = "Export completed"
                exportHistory.append(result)
                saveExportHistory()
                isExporting = false
            }
            
            return result
            
        } catch {
            await MainActor.run {
                errorMessage = "Export failed: \(error.localizedDescription)"
                isExporting = false
            }
            return nil
        }
    }
    
    /// Share an export result
    func shareExport(_ result: ExportResult) -> UIActivityViewController {
        let activityViewController = UIActivityViewController(
            activityItems: [result.fileURL],
            applicationActivities: nil
        )
        
        activityViewController.setValue("Keeps Export - \(result.fileName)", forKey: "subject")
        
        return activityViewController
    }
    
    /// Delete an export file
    func deleteExport(_ result: ExportResult) {
        try? fileManager.removeItem(at: result.fileURL)
        exportHistory.removeAll { $0.fileURL == result.fileURL }
        saveExportHistory()
    }
    
    /// Get estimated export size
    func estimateExportSize(configuration: ExportConfiguration) async -> Int64 {
        // This would calculate an estimated file size based on the configuration
        // For now, return a placeholder value
        return 1024 * 1024 // 1MB
    }
    
    /// Validate export configuration
    func validateConfiguration(_ configuration: ExportConfiguration) -> [String] {
        var issues: [String] = []
        
        if configuration.entities.isEmpty {
            issues.append("No entities selected for export")
        }
        
        if let password = configuration.password, password.count < 6 {
            issues.append("Password must be at least 6 characters")
        }
        
        if configuration.format == .pdf && configuration.entities.count > 3 {
            issues.append("PDF export is limited to 3 entity types for performance")
        }
        
        return issues
    }
    
    // MARK: - Private Methods
    
    private func createDirectoriesIfNeeded() {
        try? fileManager.createDirectory(at: exportsDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: tempDirectory, withIntermediateDirectories: true)
    }
    
    private func updateProgress(_ progress: Double, _ step: String) async {
        await MainActor.run {
            exportProgress = progress
            currentExportStep = step
        }
    }
    
    private func gatherExportData(configuration: ExportConfiguration) async throws -> ExportData {
        var exportData = ExportData()
        
        // Gather people data
        if configuration.entities.contains(.people) {
            exportData.people = try await gatherPeopleData(configuration: configuration)
        }
        
        // Gather teams data
        if configuration.entities.contains(.teams) {
            exportData.teams = try await gatherTeamsData(configuration: configuration)
        }
        
        // Gather timeline data
        if configuration.entities.contains(.timeline) {
            exportData.timeline = try await gatherTimelineData(configuration: configuration)
        }
        
        // Gather interactions data
        if configuration.entities.contains(.interactions) {
            exportData.interactions = try await gatherInteractionsData(configuration: configuration)
        }
        
        // Gather voice notes data
        if configuration.entities.contains(.voiceNotes) && configuration.includeVoiceNotes {
            exportData.voiceNotes = try await gatherVoiceNotesData(configuration: configuration)
        }
        
        // Gather attachments data
        if configuration.entities.contains(.attachments) && configuration.includeAttachments {
            exportData.attachments = try await gatherAttachmentsData(configuration: configuration)
        }
        
        // Gather analytics data
        if configuration.entities.contains(.analytics) {
            exportData.analytics = try await gatherAnalyticsData(configuration: configuration)
        }
        
        return exportData
    }
    
    private func processExportData(_ exportData: ExportData, configuration: ExportConfiguration) async throws -> ProcessedExportData {
        switch configuration.format {
        case .json:
            return try await processJSONExport(exportData, configuration: configuration)
        case .csv:
            return try await processCSVExport(exportData, configuration: configuration)
        case .pdf:
            return try await processPDFExport(exportData, configuration: configuration)
        case .html:
            return try await processHTMLExport(exportData, configuration: configuration)
        case .markdown:
            return try await processMarkdownExport(exportData, configuration: configuration)
        case .zip:
            return try await processZIPExport(exportData, configuration: configuration)
        }
    }
    
    private func generateExportFile(_ processedData: ProcessedExportData, configuration: ExportConfiguration) async throws -> URL {
        let fileName = generateFileName(configuration: configuration)
        let fileURL = exportsDirectory.appendingPathComponent(fileName)
        
        switch configuration.format {
        case .json, .csv, .html, .markdown:
            try processedData.textContent?.write(to: fileURL, atomically: true, encoding: .utf8)
        case .pdf:
            try processedData.pdfData?.write(to: fileURL)
        case .zip:
            try processedData.zipData?.write(to: fileURL)
        }
        
        return fileURL
    }
    
    private func finalizeExport(fileURL: URL, configuration: ExportConfiguration, startTime: Date, totalRecords: Int) async throws -> ExportResult {
        let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
        let fileSize = attributes[.size] as? Int64 ?? 0
        let exportDuration = Date().timeIntervalSince(startTime)
        
        let metadata = ExportResult.ExportMetadata(
            totalRecords: totalRecords,
            includedEntities: Array(configuration.entities),
            dateRange: configuration.dateRange,
            exportDuration: exportDuration,
            compressionRatio: configuration.compressionLevel != .none ? Double(configuration.compressionLevel.compressionRatio) : nil
        )
        
        return ExportResult(
            fileURL: fileURL,
            fileName: fileURL.lastPathComponent,
            fileSize: fileSize,
            format: configuration.format,
            createdAt: Date(),
            configuration: configuration,
            metadata: metadata
        )
    }
    
    private func generateFileName(configuration: ExportConfiguration) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let timestamp = dateFormatter.string(from: Date())
        
        let entityNames = configuration.entities.map { $0.rawValue }.joined(separator: "-")
        return "keeps_export_\(entityNames)_\(timestamp).\(configuration.format.fileExtension)"
    }
    
    // MARK: - Data Gathering Methods
    
    private func gatherPeopleData(configuration: ExportConfiguration) async throws -> [Any] {
        // In a real implementation, this would fetch from Core Data
        return []
    }
    
    private func gatherTeamsData(configuration: ExportConfiguration) async throws -> [Any] {
        // In a real implementation, this would fetch from Core Data
        return []
    }
    
    private func gatherTimelineData(configuration: ExportConfiguration) async throws -> [Any] {
        // In a real implementation, this would fetch from Core Data
        return []
    }
    
    private func gatherInteractionsData(configuration: ExportConfiguration) async throws -> [Any] {
        // In a real implementation, this would fetch from Core Data
        return []
    }
    
    private func gatherVoiceNotesData(configuration: ExportConfiguration) async throws -> [Any] {
        // In a real implementation, this would fetch voice notes
        return []
    }
    
    private func gatherAttachmentsData(configuration: ExportConfiguration) async throws -> [Any] {
        // In a real implementation, this would fetch attachments
        return []
    }
    
    private func gatherAnalyticsData(configuration: ExportConfiguration) async throws -> [Any] {
        // In a real implementation, this would fetch analytics data
        return []
    }
    
    // MARK: - Format Processing Methods
    
    private func processJSONExport(_ exportData: ExportData, configuration: ExportConfiguration) async throws -> ProcessedExportData {
        let jsonData = try JSONSerialization.data(withJSONObject: exportData.toDictionary(), options: .prettyPrinted)
        let jsonString = String(data: jsonData, encoding: .utf8)
        return ProcessedExportData(textContent: jsonString)
    }
    
    private func processCSVExport(_ exportData: ExportData, configuration: ExportConfiguration) async throws -> ProcessedExportData {
        // Generate CSV content for each entity type
        var csvContent = ""
        
        // Add people CSV
        if !exportData.people.isEmpty {
            csvContent += "People\n"
            csvContent += "Name,Email,Phone,Organization\n"
            // Add people data rows
            csvContent += "\n"
        }
        
        // Add teams CSV
        if !exportData.teams.isEmpty {
            csvContent += "Teams\n"
            csvContent += "Name,Description,Members\n"
            // Add teams data rows
            csvContent += "\n"
        }
        
        return ProcessedExportData(textContent: csvContent)
    }
    
    private func processPDFExport(_ exportData: ExportData, configuration: ExportConfiguration) async throws -> ProcessedExportData {
        let pdfDocument = PDFDocument()
        
        // Create PDF pages for each entity type
        // This is a simplified implementation
        let page = PDFPage()
        pdfDocument.insert(page, at: 0)
        
        let pdfData = pdfDocument.dataRepresentation()
        return ProcessedExportData(pdfData: pdfData)
    }
    
    private func processHTMLExport(_ exportData: ExportData, configuration: ExportConfiguration) async throws -> ProcessedExportData {
        var htmlContent = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Keeps Export</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333; }
                table { border-collapse: collapse; width: 100%; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <h1>Keeps Export</h1>
            <p>Generated on \(Date())</p>
        """
        
        // Add content for each entity type
        htmlContent += "</body></html>"
        
        return ProcessedExportData(textContent: htmlContent)
    }
    
    private func processMarkdownExport(_ exportData: ExportData, configuration: ExportConfiguration) async throws -> ProcessedExportData {
        var markdownContent = "# Keeps Export\n\n"
        markdownContent += "Generated on \(Date())\n\n"
        
        // Add content for each entity type
        
        return ProcessedExportData(textContent: markdownContent)
    }
    
    private func processZIPExport(_ exportData: ExportData, configuration: ExportConfiguration) async throws -> ProcessedExportData {
        // Create a ZIP archive with multiple files
        // This would use a ZIP library to create the archive
        let zipData = Data() // Placeholder
        return ProcessedExportData(zipData: zipData)
    }
    
    private func loadExportHistory() {
        // In a real implementation, this would load from persistent storage
        exportHistory = []
    }
    
    private func saveExportHistory() {
        // In a real implementation, this would save to persistent storage
    }
}

// MARK: - Supporting Data Structures

struct ExportData {
    var people: [Any] = []
    var teams: [Any] = []
    var timeline: [Any] = []
    var interactions: [Any] = []
    var voiceNotes: [Any] = []
    var attachments: [Any] = []
    var analytics: [Any] = []
    
    var totalRecords: Int {
        return people.count + teams.count + timeline.count + interactions.count + voiceNotes.count + attachments.count + analytics.count
    }
    
    func toDictionary() -> [String: Any] {
        return [
            "people": people,
            "teams": teams,
            "timeline": timeline,
            "interactions": interactions,
            "voiceNotes": voiceNotes,
            "attachments": attachments,
            "analytics": analytics,
            "exportInfo": [
                "totalRecords": totalRecords,
                "exportDate": Date(),
                "version": "1.0"
            ]
        ]
    }
}

struct ProcessedExportData {
    let textContent: String?
    let pdfData: Data?
    let zipData: Data?
    
    init(textContent: String? = nil, pdfData: Data? = nil, zipData: Data? = nil) {
        self.textContent = textContent
        self.pdfData = pdfData
        self.zipData = zipData
    }
}

// MARK: - Export Errors

enum ExportError: LocalizedError {
    case noDataToExport
    case invalidConfiguration
    case fileCreationFailed
    case compressionFailed
    case encryptionFailed
    case insufficientStorage
    
    var errorDescription: String? {
        switch self {
        case .noDataToExport:
            return "No data available to export"
        case .invalidConfiguration:
            return "Invalid export configuration"
        case .fileCreationFailed:
            return "Failed to create export file"
        case .compressionFailed:
            return "Failed to compress export data"
        case .encryptionFailed:
            return "Failed to encrypt export file"
        case .insufficientStorage:
            return "Insufficient storage space for export"
        }
    }
}
