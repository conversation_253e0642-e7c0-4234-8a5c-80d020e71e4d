//
//  NavigationCoordinator.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import Foundation
import SwiftUI
import Combine

/// Central navigation coordinator for seamless cross-section navigation
/// Manages deep linking between People, Teams, and Timeline sections
class NavigationCoordinator: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var selectedTab: Int = 0
    @Published var navigationPath: [NavigationDestination] = []
    @Published var currentContext: NavigationContext?
    @Published var showingDeepLink = false
    @Published var pendingDeepLink: DeepLink?
    
    // MARK: - Navigation State
    
    @Published var selectedPersonId: UUID?
    @Published var selectedTeamId: UUID?
    @Published var selectedWeekNumber: Int?
    @Published var showingPersonDetail = false
    @Published var showingTeamDetail = false
    @Published var showingWeekDetail = false
    
    // MARK: - Breadcrumb Navigation
    
    @Published var breadcrumbs: [Breadcrumb] = []
    @Published var showingBreadcrumbs = false
    
    // MARK: - Initialization
    
    init() {
        setupNavigationObservers()
    }
    
    // MARK: - Deep Link Navigation
    
    /// Navigate to a person from any section
    func navigateToPerson(_ personId: UUID, context: NavigationContext? = nil) {
        let deepLink = DeepLink(
            destination: .person(personId),
            sourceTab: selectedTab,
            context: context
        )
        
        executeDeepLink(deepLink)
    }
    
    /// Navigate to a team from any section
    func navigateToTeam(_ teamId: UUID, context: NavigationContext? = nil) {
        let deepLink = DeepLink(
            destination: .team(teamId),
            sourceTab: selectedTab,
            context: context
        )
        
        executeDeepLink(deepLink)
    }
    
    /// Navigate to a timeline week from any section
    func navigateToWeek(_ weekNumber: Int, context: NavigationContext? = nil) {
        let deepLink = DeepLink(
            destination: .timeline(weekNumber),
            sourceTab: selectedTab,
            context: context
        )
        
        executeDeepLink(deepLink)
    }
    
    /// Navigate to related content based on current context
    func navigateToRelated(_ relatedContent: RelatedContent) {
        switch relatedContent {
        case .personTeams(let personId):
            navigateToPersonTeams(personId)
        case .personTimeline(let personId):
            navigateToPersonTimeline(personId)
        case .teamMembers(let teamId):
            navigateToTeamMembers(teamId)
        case .teamTimeline(let teamId):
            navigateToTeamTimeline(teamId)
        case .timelinePeople(let weekNumber):
            navigateToTimelinePeople(weekNumber)
        case .timelineTeams(let weekNumber):
            navigateToTimelineTeams(weekNumber)
        }
    }
    
    // MARK: - Contextual Navigation
    
    /// Navigate to teams associated with a person
    private func navigateToPersonTeams(_ personId: UUID) {
        let context = NavigationContext(
            title: "Teams",
            subtitle: "Related to person",
            sourceSection: .people,
            targetSection: .teams,
            relatedId: personId
        )
        
        selectedTab = 0 // Teams tab
        currentContext = context
        addBreadcrumb(Breadcrumb(title: "Person Teams", destination: .teams))
    }
    
    /// Navigate to timeline entries for a person
    private func navigateToPersonTimeline(_ personId: UUID) {
        let context = NavigationContext(
            title: "Timeline",
            subtitle: "Related to person",
            sourceSection: .people,
            targetSection: .timeline,
            relatedId: personId
        )
        
        selectedTab = 2 // Timeline tab
        currentContext = context
        addBreadcrumb(Breadcrumb(title: "Person Timeline", destination: .timeline))
    }
    
    /// Navigate to team members
    private func navigateToTeamMembers(_ teamId: UUID) {
        let context = NavigationContext(
            title: "Team Members",
            subtitle: "People in this team",
            sourceSection: .teams,
            targetSection: .people,
            relatedId: teamId
        )
        
        selectedTab = 1 // People tab
        currentContext = context
        addBreadcrumb(Breadcrumb(title: "Team Members", destination: .people))
    }
    
    /// Navigate to timeline entries for a team
    private func navigateToTeamTimeline(_ teamId: UUID) {
        let context = NavigationContext(
            title: "Team Timeline",
            subtitle: "Project milestones and progress",
            sourceSection: .teams,
            targetSection: .timeline,
            relatedId: teamId
        )
        
        selectedTab = 2 // Timeline tab
        currentContext = context
        addBreadcrumb(Breadcrumb(title: "Team Timeline", destination: .timeline))
    }
    
    /// Navigate to people associated with a timeline week
    private func navigateToTimelinePeople(_ weekNumber: Int) {
        let context = NavigationContext(
            title: "Related People",
            subtitle: "Connected to week \(weekNumber)",
            sourceSection: .timeline,
            targetSection: .people,
            relatedWeek: weekNumber
        )
        
        selectedTab = 1 // People tab
        currentContext = context
        addBreadcrumb(Breadcrumb(title: "Week People", destination: .people))
    }
    
    /// Navigate to teams associated with a timeline week
    private func navigateToTimelineTeams(_ weekNumber: Int) {
        let context = NavigationContext(
            title: "Related Teams",
            subtitle: "Connected to week \(weekNumber)",
            sourceSection: .timeline,
            targetSection: .teams,
            relatedWeek: weekNumber
        )
        
        selectedTab = 0 // Teams tab
        currentContext = context
        addBreadcrumb(Breadcrumb(title: "Week Teams", destination: .teams))
    }
    
    // MARK: - Deep Link Execution
    
    private func executeDeepLink(_ deepLink: DeepLink) {
        pendingDeepLink = deepLink
        
        switch deepLink.destination {
        case .person(let personId):
            selectedTab = 1 // People tab
            selectedPersonId = personId
            showingPersonDetail = true
            
        case .team(let teamId):
            selectedTab = 0 // Teams tab
            selectedTeamId = teamId
            showingTeamDetail = true
            
        case .timeline(let weekNumber):
            selectedTab = 2 // Timeline tab
            selectedWeekNumber = weekNumber
            showingWeekDetail = true
        }
        
        currentContext = deepLink.context
        
        // Add breadcrumb if coming from different section
        if deepLink.sourceTab != selectedTab {
            addDeepLinkBreadcrumb(deepLink)
        }
    }
    
    // MARK: - Breadcrumb Management
    
    private func addBreadcrumb(_ breadcrumb: Breadcrumb) {
        breadcrumbs.append(breadcrumb)
        showingBreadcrumbs = true
    }
    
    private func addDeepLinkBreadcrumb(_ deepLink: DeepLink) {
        let sourceSection = AppSection.fromTabIndex(deepLink.sourceTab)
        let targetSection = AppSection.fromTabIndex(selectedTab)
        
        let breadcrumb = Breadcrumb(
            title: "\(sourceSection.displayName) → \(targetSection.displayName)",
            destination: NavigationDestination.fromDeepLink(deepLink.destination)
        )
        
        addBreadcrumb(breadcrumb)
    }
    
    /// Clear current navigation context and breadcrumbs
    func clearNavigationContext() {
        currentContext = nil
        breadcrumbs.removeAll()
        showingBreadcrumbs = false
        pendingDeepLink = nil
    }
    
    /// Navigate back using breadcrumbs
    func navigateBack() {
        guard !breadcrumbs.isEmpty else { return }
        
        breadcrumbs.removeLast()
        
        if breadcrumbs.isEmpty {
            clearNavigationContext()
        }
    }
    
    /// Navigate to a specific breadcrumb
    func navigateToBreadcrumb(_ breadcrumb: Breadcrumb) {
        // Remove breadcrumbs after the selected one
        if let index = breadcrumbs.firstIndex(where: { $0.id == breadcrumb.id }) {
            breadcrumbs = Array(breadcrumbs.prefix(index + 1))
        }
        
        // Navigate to the breadcrumb destination
        switch breadcrumb.destination {
        case .people:
            selectedTab = 1
        case .teams:
            selectedTab = 0
        case .timeline:
            selectedTab = 2
        }
    }
    
    // MARK: - Navigation Observers
    
    private func setupNavigationObservers() {
        // Clear context when tab changes manually
        $selectedTab
            .dropFirst()
            .sink { [weak self] _ in
                // Only clear if not part of a deep link navigation
                if self?.pendingDeepLink == nil {
                    self?.clearNavigationContext()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
}

// MARK: - Helper Extensions

extension AppSection {
    static func fromTabIndex(_ index: Int) -> AppSection {
        switch index {
        case 0: return .teams
        case 1: return .people
        case 2: return .timeline
        default: return .people
        }
    }
}

extension NavigationDestination {
    static func fromDeepLink(_ deepLinkDestination: DeepLinkDestination) -> NavigationDestination {
        switch deepLinkDestination {
        case .person: return .people
        case .team: return .teams
        case .timeline: return .timeline
        }
    }
}
