//
//  DeepLinkModels.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Deep Link Models

/// Represents a deep link navigation request between sections
struct DeepLink: Identifiable {
    let id = UUID()
    let destination: DeepLinkDestination
    let sourceTab: Int
    let context: NavigationContext?
    let timestamp: Date
    
    init(destination: DeepLinkDestination, sourceTab: Int, context: NavigationContext? = nil) {
        self.destination = destination
        self.sourceTab = sourceTab
        self.context = context
        self.timestamp = Date()
    }
}

/// Possible deep link destinations
enum DeepLinkDestination: Hashable {
    case person(UUID)
    case team(UUID)
    case timeline(Int) // week number
    
    var displayName: String {
        switch self {
        case .person: return "Person"
        case .team: return "Team"
        case .timeline(let week): return "Week \(week)"
        }
    }
}

/// Navigation context for cross-section navigation
struct NavigationContext: Identifiable {
    let id = UUID()
    let title: String
    let subtitle: String?
    let sourceSection: AppSection
    let targetSection: AppSection
    let relatedId: UUID?
    let relatedWeek: Int?
    let timestamp: Date
    
    init(title: String, subtitle: String? = nil, sourceSection: AppSection, targetSection: AppSection, relatedId: UUID? = nil, relatedWeek: Int? = nil) {
        self.title = title
        self.subtitle = subtitle
        self.sourceSection = sourceSection
        self.targetSection = targetSection
        self.relatedId = relatedId
        self.relatedWeek = relatedWeek
        self.timestamp = Date()
    }
    
    var displayText: String {
        if let subtitle = subtitle {
            return "\(title) • \(subtitle)"
        }
        return title
    }
}

/// App sections for navigation
enum AppSection: String, CaseIterable, Codable {
    case people = "people"
    case teams = "teams"
    case timeline = "timeline"
    case workflow = "workflow"
    case general = "general"

    var displayName: String {
        switch self {
        case .people: return "People"
        case .teams: return "Teams"
        case .timeline: return "Timeline"
        case .workflow: return "Workflow"
        case .general: return "General"
        }
    }

    var icon: String {
        switch self {
        case .people: return "person.crop.circle"
        case .teams: return "person.3"
        case .timeline: return "timeline.selection"
        case .workflow: return "arrow.triangle.branch"
        case .general: return "app.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .people: return .blue
        case .teams: return .green
        case .timeline: return .purple
        case .workflow: return .indigo
        case .general: return .gray
        }
    }
    
    var tabIndex: Int {
        switch self {
        case .teams: return 0
        case .people: return 1
        case .timeline: return 2
        case .workflow: return -1 // Not a main tab
        case .general: return -1 // Not a main tab
        }
    }
}

/// Navigation destinations for breadcrumbs
enum NavigationDestination: Hashable {
    case people
    case teams
    case timeline
    
    var displayName: String {
        switch self {
        case .people: return "People"
        case .teams: return "Teams"
        case .timeline: return "Timeline"
        }
    }
    
    var icon: String {
        switch self {
        case .people: return "person.crop.circle"
        case .teams: return "person.3"
        case .timeline: return "timeline.selection"
        }
    }
}

/// Breadcrumb for navigation history
struct Breadcrumb: Identifiable, Hashable {
    let id = UUID()
    let title: String
    let destination: NavigationDestination
    let timestamp: Date
    
    init(title: String, destination: NavigationDestination) {
        self.title = title
        self.destination = destination
        self.timestamp = Date()
    }
}

/// Related content types for contextual navigation
enum RelatedContent {
    case personTeams(UUID)      // Teams associated with a person
    case personTimeline(UUID)   // Timeline entries for a person
    case teamMembers(UUID)      // People in a team
    case teamTimeline(UUID)     // Timeline entries for a team
    case timelinePeople(Int)    // People associated with a week
    case timelineTeams(Int)     // Teams associated with a week
    
    var displayName: String {
        switch self {
        case .personTeams: return "Related Teams"
        case .personTimeline: return "Timeline Entries"
        case .teamMembers: return "Team Members"
        case .teamTimeline: return "Project Timeline"
        case .timelinePeople: return "Related People"
        case .timelineTeams: return "Related Teams"
        }
    }
    
    var icon: String {
        switch self {
        case .personTeams: return "person.3"
        case .personTimeline: return "timeline.selection"
        case .teamMembers: return "person.crop.circle"
        case .teamTimeline: return "timeline.selection"
        case .timelinePeople: return "person.crop.circle"
        case .timelineTeams: return "person.3"
        }
    }
    
    var targetSection: AppSection {
        switch self {
        case .personTeams, .timelineTeams: return .teams
        case .teamMembers, .timelinePeople: return .people
        case .personTimeline, .teamTimeline: return .timeline
        }
    }
}

// MARK: - Navigation Action Models

/// Actions that can be performed from contextual navigation
enum NavigationAction: Identifiable {
    case viewInTimeline(UUID)           // View person/team in timeline
    case viewTeamMembers(UUID)          // View team members
    case viewRelatedPeople(UUID)        // View people related to team
    case viewPersonTeams(UUID)          // View teams for person
    case viewWeekPeople(Int)            // View people in timeline week
    case viewWeekTeams(Int)             // View teams in timeline week
    case createConnection(UUID, UUID)   // Create connection between person and team
    case addToTimeline(UUID, Int)       // Add person/team to timeline week
    
    var id: String {
        switch self {
        case .viewInTimeline(let id): return "viewInTimeline_\(id)"
        case .viewTeamMembers(let id): return "viewTeamMembers_\(id)"
        case .viewRelatedPeople(let id): return "viewRelatedPeople_\(id)"
        case .viewPersonTeams(let id): return "viewPersonTeams_\(id)"
        case .viewWeekPeople(let week): return "viewWeekPeople_\(week)"
        case .viewWeekTeams(let week): return "viewWeekTeams_\(week)"
        case .createConnection(let id1, let id2): return "createConnection_\(id1)_\(id2)"
        case .addToTimeline(let id, let week): return "addToTimeline_\(id)_\(week)"
        }
    }
    
    var displayName: String {
        switch self {
        case .viewInTimeline: return "View in Timeline"
        case .viewTeamMembers: return "View Team Members"
        case .viewRelatedPeople: return "View Related People"
        case .viewPersonTeams: return "View Person's Teams"
        case .viewWeekPeople: return "View Week People"
        case .viewWeekTeams: return "View Week Teams"
        case .createConnection: return "Create Connection"
        case .addToTimeline: return "Add to Timeline"
        }
    }
    
    var icon: String {
        switch self {
        case .viewInTimeline: return "timeline.selection"
        case .viewTeamMembers: return "person.crop.circle"
        case .viewRelatedPeople: return "person.2"
        case .viewPersonTeams: return "person.3"
        case .viewWeekPeople: return "person.crop.circle"
        case .viewWeekTeams: return "person.3"
        case .createConnection: return "link"
        case .addToTimeline: return "plus.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .viewInTimeline: return .purple
        case .viewTeamMembers, .viewRelatedPeople, .viewWeekPeople: return .blue
        case .viewPersonTeams, .viewWeekTeams: return .green
        case .createConnection: return .orange
        case .addToTimeline: return .indigo
        }
    }
}

// MARK: - Cross-Reference Models

/// Cross-reference between different sections
struct CrossReference: Identifiable {
    let id = UUID()
    let sourceType: AppSection
    let sourceId: UUID
    let targetType: AppSection
    let targetId: UUID
    let relationshipType: CrossReferenceType
    let strength: Double // 0.0 to 1.0
    let lastInteraction: Date
    let createdDate: Date
    
    init(sourceType: AppSection, sourceId: UUID, targetType: AppSection, targetId: UUID, relationshipType: CrossReferenceType, strength: Double = 0.5) {
        self.sourceType = sourceType
        self.sourceId = sourceId
        self.targetType = targetType
        self.targetId = targetId
        self.relationshipType = relationshipType
        self.strength = max(0.0, min(1.0, strength))
        self.lastInteraction = Date()
        self.createdDate = Date()
    }
}

/// Types of cross-references between sections
enum CrossReferenceType: String, CaseIterable {
    case teamMember = "team_member"
    case timelineEntry = "timeline_entry"
    case collaboration = "collaboration"
    case mentorship = "mentorship"
    case project = "project"
    case milestone = "milestone"
    case accomplishment = "accomplishment"
    case reflection = "reflection"
    
    var displayName: String {
        switch self {
        case .teamMember: return "Team Member"
        case .timelineEntry: return "Timeline Entry"
        case .collaboration: return "Collaboration"
        case .mentorship: return "Mentorship"
        case .project: return "Project"
        case .milestone: return "Milestone"
        case .accomplishment: return "Accomplishment"
        case .reflection: return "Reflection"
        }
    }
    
    var icon: String {
        switch self {
        case .teamMember: return "person.badge.plus"
        case .timelineEntry: return "timeline.selection"
        case .collaboration: return "person.2.wave.2"
        case .mentorship: return "graduationcap"
        case .project: return "folder"
        case .milestone: return "flag"
        case .accomplishment: return "star"
        case .reflection: return "lightbulb"
        }
    }
    
    var color: Color {
        switch self {
        case .teamMember: return .green
        case .timelineEntry: return .purple
        case .collaboration: return .blue
        case .mentorship: return .orange
        case .project: return .indigo
        case .milestone: return .red
        case .accomplishment: return .yellow
        case .reflection: return .mint
        }
    }
}

// MARK: - Navigation Analytics

/// Analytics for navigation patterns
struct NavigationAnalytics {
    var totalNavigations: Int = 0
    var crossSectionNavigations: Int = 0
    var mostUsedPaths: [String: Int] = [:]
    var averageSessionDepth: Double = 0.0
    var lastUpdated: Date = Date()
    
    mutating func recordNavigation(from source: AppSection, to target: AppSection) {
        totalNavigations += 1
        
        if source != target {
            crossSectionNavigations += 1
        }
        
        let pathKey = "\(source.rawValue)_to_\(target.rawValue)"
        mostUsedPaths[pathKey, default: 0] += 1
        
        lastUpdated = Date()
    }
    
    var crossSectionPercentage: Double {
        guard totalNavigations > 0 else { return 0.0 }
        return Double(crossSectionNavigations) / Double(totalNavigations) * 100.0
    }
}
