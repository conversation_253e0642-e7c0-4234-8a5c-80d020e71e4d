//
//  Persistence.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import CoreData

struct PersistenceController {
    static let shared = PersistenceController()

    @MainActor
    static let preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext

        // Create sample data for preview
        let samplePerson = PersonEntity(context: viewContext)
        samplePerson.id = UUID()
        samplePerson.name = "<PERSON>"
        samplePerson.role = "Software Engineer"
        samplePerson.company = "Tech Corp"
        samplePerson.email = "<EMAIL>"
        samplePerson.relationshipType = "colleague"
        samplePerson.lastSeen = Date()
        samplePerson.isOnline = true
        samplePerson.isFavorite = false

        let sampleTeam = TeamEntity(context: viewContext)
        sampleTeam.id = UUID()
        sampleTeam.name = "iOS Development"
        sampleTeam.teamDescription = "Building amazing iOS apps"
        sampleTeam.category = "development"
        sampleTeam.projectStatus = "active"
        sampleTeam.isActive = true
        sampleTeam.lastActivity = Date()
        sampleTeam.createdDate = Date()
        sampleTeam.progress = 0.75

        let sampleTimelineEntry = TimelineEntryEntity(context: viewContext)
        sampleTimelineEntry.id = UUID()
        sampleTimelineEntry.weekNumber = 1300
        sampleTimelineEntry.startDate = Date()
        sampleTimelineEntry.endDate = Calendar.current.date(byAdding: .day, value: 6, to: Date()) ?? Date()
        sampleTimelineEntry.title = "Great week of progress"
        sampleTimelineEntry.insight = "Learned a lot about SwiftUI"
        sampleTimelineEntry.emotionalTag = "growth"
        sampleTimelineEntry.isCompleted = true
        sampleTimelineEntry.isCurrentWeek = true
        sampleTimelineEntry.createdDate = Date()
        sampleTimelineEntry.lastModified = Date()

        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    let container: NSPersistentContainer

    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "Keeps")
        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        }
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                // Replace this implementation with code to handle the error appropriately.
                // fatalError() causes the application to generate a crash log and terminate. You should not use this function in a shipping application, although it may be useful during development.

                /*
                 Typical reasons for an error here include:
                 * The parent directory does not exist, cannot be created, or disallows writing.
                 * The persistent store is not accessible, due to permissions or data protection when the device is locked.
                 * The device is out of space.
                 * The store could not be migrated to the current model version.
                 Check the error message to determine what the actual problem was.
                 */
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        })
        container.viewContext.automaticallyMergesChangesFromParent = true
    }
}
