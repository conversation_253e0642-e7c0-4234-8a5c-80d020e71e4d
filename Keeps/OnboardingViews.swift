//
//  OnboardingViews.swift
//  Keeps
//
//  Revolutionary onboarding views with Apple-style animations and interactions
//

import SwiftUI

// MARK: - Main Onboarding View

/// Main onboarding container view with step navigation
struct OnboardingView: View {
    @StateObject private var onboardingManager = OnboardingManager()
    @Environment(\.managedObjectContext) private var viewContext
    
    var body: some View {
        ZStack {
            // Animated background
            OnboardingBackground()
            
            VStack(spacing: 0) {
                // Progress bar
                OnboardingProgressBar(progress: onboardingManager.progress)
                    .padding(.top, 20)
                    .padding(.horizontal, 40)
                
                // Current step view
                currentStepView
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                
                // Navigation buttons
                navigationButtons
                    .padding(.bottom, 40)
            }
        }
        .preferredColorScheme(.dark)
    }
    
    @ViewBuilder
    private var currentStepView: some View {
        switch onboardingManager.currentStep {
        case .welcome:
            WelcomeStepView()
        case .concept:
            ConceptStepView()
        case .personalInfo:
            PersonalInfoStepView(onboardingManager: onboardingManager)
        case .relationships:
            RelationshipsStepView(onboardingManager: onboardingManager)
        case .teams:
            TeamsStepView(onboardingManager: onboardingManager)
        case .timeline:
            TimelineStepView(onboardingManager: onboardingManager)
        }
    }
    
    private var navigationButtons: some View {
        HStack(spacing: 20) {
            // Back button
            if onboardingManager.currentStep != .welcome {
                Button(action: onboardingManager.previousStep) {
                    HStack {
                        Image(systemName: "chevron.left")
                        Text("Back")
                    }
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(25)
                }
            }
            
            Spacer()
            
            // Next/Complete button
            Button(action: onboardingManager.nextStep) {
                HStack {
                    Text(onboardingManager.currentStep == .timeline ? "Complete" : "Continue")
                    if onboardingManager.currentStep != .timeline {
                        Image(systemName: "chevron.right")
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, 30)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(25)
            }
            .disabled(onboardingManager.isAnimating)
        }
        .padding(.horizontal, 40)
    }
}

// MARK: - Welcome Step

/// Welcome step introducing the app
struct WelcomeStepView: View {
    @State private var animateIcon = false
    @State private var animateText = false
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // App icon with animation
            VStack(spacing: 20) {
                Image(systemName: "person.3.sequence.fill")
                    .font(.system(size: 100))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple, .pink],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .scaleEffect(animateIcon ? 1.0 : 0.8)
                    .rotationEffect(.degrees(animateIcon ? 0 : -10))
                    .animation(.spring(response: 0.8, dampingFraction: 0.6), value: animateIcon)
                
                Text("KEEPS")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            }
            
            // Welcome text
            VStack(spacing: 16) {
                Text("Welcome to Keeps")
                    .font(.title)
                    .fontWeight(.semibold)
                    .opacity(animateText ? 1 : 0)
                    .offset(y: animateText ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.3), value: animateText)
                
                Text("Clarity and control of your life through understanding relationships, organizing projects with the right people, and staying motivated through achievement tracking.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .opacity(animateText ? 1 : 0)
                    .offset(y: animateText ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.5), value: animateText)
            }
            .padding(.horizontal, 40)
            
            Spacer()
        }
        .onAppear {
            animateIcon = true
            animateText = true
        }
    }
}

// MARK: - Concept Step

/// Concept step explaining the unified approach
struct ConceptStepView: View {
    @State private var animateConnections = false
    @State private var currentHighlight = 0
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // Title
            VStack(spacing: 16) {
                Text("Your Life, Connected")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("Keeps unifies three essential aspects of your life")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 40)
            
            // Connection diagram
            connectionDiagram
            
            // Description
            VStack(spacing: 12) {
                Text("Everything is connected:")
                    .font(.headline)
                    .fontWeight(.medium)
                
                VStack(alignment: .leading, spacing: 8) {
                    connectionPoint("People", "The relationships that matter", .pink)
                    connectionPoint("Teams", "Collaborative projects and goals", .blue)
                    connectionPoint("Timeline", "Your journey and achievements", .green)
                }
            }
            .padding(.horizontal, 40)
            
            Spacer()
        }
        .onAppear {
            startConnectionAnimation()
        }
    }
    
    private var connectionDiagram: some View {
        ZStack {
            // Connection lines
            Path { path in
                let center = CGPoint(x: 150, y: 100)
                let radius: CGFloat = 60
                
                // Triangle connections
                let point1 = CGPoint(x: center.x, y: center.y - radius)
                let point2 = CGPoint(x: center.x - radius * cos(.pi/6), y: center.y + radius * sin(.pi/6))
                let point3 = CGPoint(x: center.x + radius * cos(.pi/6), y: center.y + radius * sin(.pi/6))
                
                path.move(to: point1)
                path.addLine(to: point2)
                path.addLine(to: point3)
                path.addLine(to: point1)
            }
            .stroke(Color.blue.opacity(0.5), lineWidth: 2)
            .opacity(animateConnections ? 1 : 0)
            .animation(.easeInOut(duration: 1.0).delay(0.5), value: animateConnections)
            
            // Nodes
            VStack(spacing: 40) {
                // People
                conceptNode("People", "person.crop.circle.fill", .pink, isHighlighted: currentHighlight == 0)
                
                HStack(spacing: 80) {
                    // Teams
                    conceptNode("Teams", "person.3.fill", .blue, isHighlighted: currentHighlight == 1)
                    
                    // Timeline
                    conceptNode("Timeline", "timeline.selection", .green, isHighlighted: currentHighlight == 2)
                }
            }
        }
        .frame(height: 200)
    }
    
    private func conceptNode(_ title: String, _ icon: String, _ color: Color, isHighlighted: Bool) -> some View {
        VStack(spacing: 8) {
            Circle()
                .fill(color.opacity(0.2))
                .frame(width: 60, height: 60)
                .overlay(
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(color)
                )
                .scaleEffect(isHighlighted ? 1.2 : 1.0)
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isHighlighted)
            
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isHighlighted ? color : .secondary)
        }
    }
    
    private func connectionPoint(_ title: String, _ description: String, _ color: Color) -> some View {
        HStack(spacing: 12) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
    
    private func startConnectionAnimation() {
        animateConnections = true
        
        Timer.scheduledTimer(withTimeInterval: 1.5, repeats: true) { _ in
            withAnimation(.easeInOut(duration: 0.5)) {
                currentHighlight = (currentHighlight + 1) % 3
            }
        }
    }
}

// MARK: - Preview

struct OnboardingViews_Previews: PreviewProvider {
    static var previews: some View {
        OnboardingView()
            .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
    }
}
