//
//  EnhancedTimelineInterface.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Enhanced Timeline Interface with People & Team Integration
/// Revolutionary timeline interface that integrates people interactions, team achievements, and social context

struct EnhancedTimelineInterface: View {
    @StateObject private var timelineManager = EvolutionTimelineManager()
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var teamManager: TeamManager
    @State private var selectedWeekNumber: Int?
    @State private var showingEnhancedWeekDetail = false
    @State private var showingPeopleInteractions = true
    @State private var showingTeamAchievements = true
    @State private var showingCrossSectionMilestones = true
    @State private var selectedInteractionFilter: InteractionFilter = .all
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Enhanced timeline controls
                enhancedTimelineControls
                
                // Main timeline view
                GeometryReader { geometry in
                    HStack(spacing: 0) {
                        // Enhanced timeline scrubber with interaction markers
                        EnhancedTimelineScrubber(
                            timelineManager: timelineManager,
                            peopleManager: peopleManager,
                            teamManager: teamManager,
                            selectedWeekNumber: $selectedWeekNumber,
                            showingPeopleInteractions: showingPeopleInteractions,
                            showingTeamAchievements: showingTeamAchievements,
                            showingCrossSectionMilestones: showingCrossSectionMilestones,
                            interactionFilter: selectedInteractionFilter,
                            geometry: geometry
                        )
                        .frame(width: 120)
                        
                        // Enhanced week content with social context
                        EnhancedWeekContentArea(
                            selectedWeekNumber: selectedWeekNumber,
                            timelineManager: timelineManager,
                            peopleManager: peopleManager,
                            teamManager: teamManager,
                            showingEnhancedWeekDetail: $showingEnhancedWeekDetail
                        )
                    }
                }
            }
            .background(KeepsDesignSystem.Colors.groupedBackground)
            .navigationTitle("Enhanced Timeline")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Current Week") {
                            withAnimation(KeepsDesignSystem.Animations.spring) {
                                timelineManager.navigateToCurrentWeek()
                                selectedWeekNumber = timelineManager.currentWeekNumber
                            }
                        }
                        
                        Divider()
                        
                        Toggle("People Interactions", isOn: $showingPeopleInteractions)
                        Toggle("Team Achievements", isOn: $showingTeamAchievements)
                        Toggle("Cross-Section Milestones", isOn: $showingCrossSectionMilestones)
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
        }
        .sheet(isPresented: $showingEnhancedWeekDetail) {
            if let weekNumber = selectedWeekNumber {
                EnhancedWeekDetailSheet(
                    weekNumber: weekNumber,
                    timelineManager: timelineManager,
                    peopleManager: peopleManager,
                    teamManager: teamManager,
                    isPresented: $showingEnhancedWeekDetail
                )
            }
        }
        .onAppear {
            selectedWeekNumber = timelineManager.currentWeekNumber
        }
    }
    
    // MARK: - Enhanced Timeline Controls
    private var enhancedTimelineControls: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.md) {
            // Filter controls
            HStack {
                Text("Show:")
                    .font(KeepsDesignSystem.Typography.subheadline)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                
                Picker("Interaction Filter", selection: $selectedInteractionFilter) {
                    ForEach(InteractionFilter.allCases, id: \.self) { filter in
                        Text(filter.displayName)
                            .tag(filter)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                
                Spacer()
            }
            
            // Legend
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: KeepsDesignSystem.Spacing.md) {
                    if showingPeopleInteractions {
                        LegendItem(
                            icon: "person.2.fill",
                            title: "People",
                            color: KeepsDesignSystem.Colors.primary
                        )
                    }
                    
                    if showingTeamAchievements {
                        LegendItem(
                            icon: "trophy.fill",
                            title: "Achievements",
                            color: KeepsDesignSystem.Colors.success
                        )
                    }
                    
                    if showingCrossSectionMilestones {
                        LegendItem(
                            icon: "star.fill",
                            title: "Milestones",
                            color: KeepsDesignSystem.Colors.accent
                        )
                    }
                }
                .padding(.horizontal, KeepsDesignSystem.Spacing.lg)
            }
        }
        .padding(KeepsDesignSystem.Spacing.lg)
        .background(KeepsDesignSystem.Colors.background)
    }
}

// MARK: - Interaction Filter
enum InteractionFilter: String, CaseIterable {
    case all = "All"
    case significant = "Significant"
    case recent = "Recent"
    case people = "People"
    case teams = "Teams"
    
    var displayName: String {
        return rawValue
    }
}

// MARK: - Legend Item
struct LegendItem: View {
    let icon: String
    let title: String
    let color: Color
    
    var body: some View {
        HStack(spacing: KeepsDesignSystem.Spacing.xs) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(color)
            
            Text(title)
                .font(KeepsDesignSystem.Typography.caption)
                .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
        }
        .padding(.horizontal, KeepsDesignSystem.Spacing.sm)
        .padding(.vertical, KeepsDesignSystem.Spacing.xs)
        .background(
            RoundedRectangle(cornerRadius: KeepsDesignSystem.CornerRadius.xs)
                .fill(color.opacity(0.1))
        )
    }
}

// MARK: - Enhanced Timeline Scrubber
struct EnhancedTimelineScrubber: View {
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var teamManager: TeamManager
    @Binding var selectedWeekNumber: Int?
    let showingPeopleInteractions: Bool
    let showingTeamAchievements: Bool
    let showingCrossSectionMilestones: Bool
    let interactionFilter: InteractionFilter
    let geometry: GeometryProxy
    
    @State private var sliderValue: Double = 0
    @State private var isExpanded: Bool = false
    @State private var peopleInteractions: [PeopleInteractionMarker] = []
    @State private var teamAchievements: [TeamAchievementMilestone] = []
    @State private var crossSectionMilestones: [CrossSectionMilestone] = []
    
    var body: some View {
        VStack(spacing: 0) {
            // Week indicator
            weekIndicatorView
            
            // Enhanced slider with markers
            GeometryReader { sliderGeometry in
                ZStack {
                    // Base slider track
                    Rectangle()
                        .fill(KeepsDesignSystem.Colors.tertiaryText.opacity(0.3))
                        .frame(width: 6)
                    
                    // Progress fill
                    VStack {
                        Rectangle()
                            .fill(KeepsDesignSystem.Colors.primary)
                            .frame(
                                width: 6,
                                height: sliderGeometry.size.height * CGFloat(sliderValue / Double(TimelineConfiguration.totalLifeWeeks))
                            )
                        Spacer()
                    }
                    
                    // Interaction markers
                    if showingPeopleInteractions {
                        ForEach(filteredPeopleInteractions, id: \.id) { interaction in
                            InteractionMarkerView(
                                interaction: interaction,
                                sliderHeight: sliderGeometry.size.height,
                                totalWeeks: TimelineConfiguration.totalLifeWeeks
                            )
                        }
                    }
                    
                    // Team achievement markers
                    if showingTeamAchievements {
                        ForEach(filteredTeamAchievements, id: \.id) { achievement in
                            TeamAchievementMarkerView(
                                achievement: achievement,
                                sliderHeight: sliderGeometry.size.height,
                                totalWeeks: TimelineConfiguration.totalLifeWeeks
                            )
                        }
                    }
                    
                    // Cross-section milestone markers
                    if showingCrossSectionMilestones {
                        ForEach(filteredCrossSectionMilestones, id: \.id) { milestone in
                            CrossSectionMilestoneMarkerView(
                                milestone: milestone,
                                sliderHeight: sliderGeometry.size.height,
                                totalWeeks: TimelineConfiguration.totalLifeWeeks
                            )
                        }
                    }
                    
                    // Current position thumb
                    Circle()
                        .fill(KeepsDesignSystem.Colors.primary)
                        .frame(width: 16, height: 16)
                        .offset(
                            y: sliderGeometry.size.height * CGFloat(sliderValue / Double(TimelineConfiguration.totalLifeWeeks)) - sliderGeometry.size.height/2
                        )
                        .gesture(
                            DragGesture()
                                .onChanged { value in
                                    isExpanded = true
                                    let newValue = Double(value.location.y / sliderGeometry.size.height) * Double(TimelineConfiguration.totalLifeWeeks)
                                    sliderValue = max(1, min(Double(TimelineConfiguration.totalLifeWeeks), newValue))
                                    selectedWeekNumber = Int(sliderValue)
                                }
                                .onEnded { _ in
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                        isExpanded = false
                                    }
                                }
                        )
                }
                .frame(maxWidth: .infinity)
            }
            .frame(height: geometry.size.height * 0.7)
            .padding(.horizontal, KeepsDesignSystem.Spacing.md)
            
            Spacer()
        }
        .frame(width: isExpanded ? 120 : 80)
        .background(KeepsDesignSystem.Colors.secondaryBackground.opacity(0.8))
        .clipShape(RoundedRectangle(cornerRadius: KeepsDesignSystem.CornerRadius.md))
        .animation(KeepsDesignSystem.Animations.spring, value: isExpanded)
        .onAppear {
            loadInteractionData()
            if let currentWeek = selectedWeekNumber {
                sliderValue = Double(currentWeek)
            } else {
                sliderValue = Double(timelineManager.currentWeekNumber)
            }
        }
        .onChange(of: selectedWeekNumber) { _, newWeek in
            if let week = newWeek {
                sliderValue = Double(week)
            }
        }
    }
    
    // MARK: - Week Indicator
    private var weekIndicatorView: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.xs) {
            Text("Week")
                .font(KeepsDesignSystem.Typography.caption2)
                .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
            
            Text("\(Int(sliderValue))")
                .font(KeepsDesignSystem.Typography.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(KeepsDesignSystem.Colors.primary)
                .frame(minWidth: 50)
            
            Text("\(Int((sliderValue / Double(TimelineConfiguration.totalLifeWeeks)) * 100))%")
                .font(KeepsDesignSystem.Typography.caption2)
                .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
        }
        .padding(.vertical, KeepsDesignSystem.Spacing.sm)
    }
    
    // MARK: - Filtered Data
    private var filteredPeopleInteractions: [PeopleInteractionMarker] {
        switch interactionFilter {
        case .all:
            return peopleInteractions
        case .significant:
            return peopleInteractions.filter { $0.isSignificant }
        case .recent:
            return peopleInteractions.filter { Calendar.current.isDate($0.date, equalTo: Date(), toGranularity: .month) }
        case .people:
            return peopleInteractions
        case .teams:
            return []
        }
    }
    
    private var filteredTeamAchievements: [TeamAchievementMilestone] {
        switch interactionFilter {
        case .all, .teams:
            return teamAchievements
        case .significant:
            return teamAchievements.filter { $0.impact.priority >= 3 }
        case .recent:
            return teamAchievements.filter { Calendar.current.isDate($0.date, equalTo: Date(), toGranularity: .month) }
        case .people:
            return []
        }
    }
    
    private var filteredCrossSectionMilestones: [CrossSectionMilestone] {
        switch interactionFilter {
        case .all:
            return crossSectionMilestones
        case .significant:
            return crossSectionMilestones.filter { $0.significance.priority >= 3 }
        case .recent:
            return crossSectionMilestones.filter { Calendar.current.isDate($0.date, equalTo: Date(), toGranularity: .month) }
        case .people:
            return crossSectionMilestones.filter { $0.connectedSections.contains(.people) }
        case .teams:
            return crossSectionMilestones.filter { $0.connectedSections.contains(.teams) }
        }
    }
    
    // MARK: - Data Loading
    private func loadInteractionData() {
        // Generate sample people interactions
        peopleInteractions = generateSamplePeopleInteractions()
        
        // Generate sample team achievements
        teamAchievements = generateSampleTeamAchievements()
        
        // Generate sample cross-section milestones
        crossSectionMilestones = generateSampleCrossSectionMilestones()
    }
    
    private func generateSamplePeopleInteractions() -> [PeopleInteractionMarker] {
        return [
            PeopleInteractionMarker(
                personId: UUID(),
                personName: "Sarah Chen",
                interactionType: .collaboration,
                date: Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date(),
                weekNumber: timelineManager.currentWeekNumber - 1,
                description: "Project brainstorming session",
                emotionalContext: .inspiring,
                duration: 3600,
                location: "Office",
                isSignificant: true
            ),
            PeopleInteractionMarker(
                personId: UUID(),
                personName: "Mike Johnson",
                interactionType: .milestone,
                date: Calendar.current.date(byAdding: .day, value: -14, to: Date()) ?? Date(),
                weekNumber: timelineManager.currentWeekNumber - 2,
                description: "Completed major project together",
                emotionalContext: .transformative,
                duration: nil,
                location: nil,
                isSignificant: true
            )
        ]
    }
    
    private func generateSampleTeamAchievements() -> [TeamAchievementMilestone] {
        return [
            TeamAchievementMilestone(
                teamId: UUID(),
                teamName: "Development Team",
                achievementTitle: "Product Launch",
                description: "Successfully launched new product feature",
                date: Calendar.current.date(byAdding: .day, value: -21, to: Date()) ?? Date(),
                weekNumber: timelineManager.currentWeekNumber - 3,
                achievementType: .launch,
                participants: [UUID(), UUID(), UUID()],
                impact: .organizational,
                celebrationStyle: .fireworks,
                isSharedMilestone: true
            )
        ]
    }
    
    private func generateSampleCrossSectionMilestones() -> [CrossSectionMilestone] {
        return [
            CrossSectionMilestone(
                title: "Team Leadership Role",
                description: "Promoted to team lead position",
                date: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
                weekNumber: timelineManager.currentWeekNumber - 4,
                category: .career,
                connectedSections: [.people, .teams, .timeline],
                significance: .major,
                visualStyle: .celebration
            )
        ]
    }
}
