//
//  ProjectManagementComponents.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Project Management Enhancement Components
/// Enhanced project management components for the Teams interface
/// Provides visual timelines, collaborative tools, and analytics

// MARK: - Project Dashboard Card
struct ProjectDashboardCard: View {
    @ObservedObject var team: Team
    @ObservedObject var teamManager: TeamManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            // Header with project info
            HStack {
                VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.xs) {
                    Text(team.name)
                        .font(KeepsDesignSystem.Typography.headline)
                        .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                    
                    Text(team.description)
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // Project status indicator
                Circle()
                    .fill(team.isActive ? KeepsDesignSystem.Colors.success : KeepsDesignSystem.Colors.secondary)
                    .frame(width: 12, height: 12)
            }
            
            // Team members preview
            HStack {
                Text("Team Members")
                    .font(KeepsDesignSystem.Typography.caption)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                
                Spacer()
                
                HStack(spacing: -6) {
                    ForEach(team.members.prefix(4), id: \.id) { member in
                        Circle()
                            .fill(LinearGradient(
                                colors: [Color.blue, Color.purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 24, height: 24)
                            .overlay(
                                Circle()
                                    .stroke(KeepsDesignSystem.Colors.background, lineWidth: 1)
                            )
                    }
                    
                    if team.members.count > 4 {
                        Circle()
                            .fill(KeepsDesignSystem.Colors.secondaryBackground)
                            .frame(width: 24, height: 24)
                            .overlay(
                                Text("+\(team.members.count - 4)")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                            )
                    }
                }
            }
            
            // Quick actions
            HStack(spacing: KeepsDesignSystem.Spacing.sm) {
                PolishedButton(
                    action: { /* View details */ },
                    style: .secondary,
                    accessibilityLabel: "View Project Details"
                ) {
                    HStack(spacing: KeepsDesignSystem.Spacing.xs) {
                        Image(systemName: "eye")
                        Text("View")
                    }
                    .font(KeepsDesignSystem.Typography.caption)
                }
                
                PolishedButton(
                    action: { /* Edit project */ },
                    style: .primary,
                    accessibilityLabel: "Edit Project"
                ) {
                    HStack(spacing: KeepsDesignSystem.Spacing.xs) {
                        Image(systemName: "pencil")
                        Text("Edit")
                    }
                    .font(KeepsDesignSystem.Typography.caption)
                }
            }
        }
        .polishedCard()
    }
}

// MARK: - Project Timeline Component
struct ProjectTimelineComponent: View {
    @ObservedObject var teamManager: TeamManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            PolishedSectionHeader(
                title: "Project Timeline",
                subtitle: "Recent project activities"
            )
            
            LazyVStack(spacing: KeepsDesignSystem.Spacing.sm) {
                ForEach(generateTimelineEvents(), id: \.id) { event in
                    TimelineEventCard(event: event)
                }
            }
        }
    }
    
    private func generateTimelineEvents() -> [TimelineEvent] {
        return [
            TimelineEvent(
                id: UUID(),
                title: "Project Alpha milestone completed",
                description: "Phase 1 development finished",
                timestamp: Calendar.current.date(byAdding: .hour, value: -2, to: Date()) ?? Date(),
                type: .milestone
            ),
            TimelineEvent(
                id: UUID(),
                title: "New team member added",
                description: "Sarah joined the development team",
                timestamp: Calendar.current.date(byAdding: .hour, value: -6, to: Date()) ?? Date(),
                type: .member
            ),
            TimelineEvent(
                id: UUID(),
                title: "Project Beta launched",
                description: "Successfully deployed to production",
                timestamp: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                type: .project
            )
        ]
    }
}

// MARK: - Timeline Event Card
struct TimelineEventCard: View {
    let event: TimelineEvent
    
    var body: some View {
        HStack(spacing: KeepsDesignSystem.Spacing.md) {
            // Event type indicator
            Circle()
                .fill(event.type.color.opacity(0.1))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: event.type.icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(event.type.color)
                )
            
            // Event details
            VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.xs) {
                Text(event.title)
                    .font(KeepsDesignSystem.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                
                Text(event.description)
                    .font(KeepsDesignSystem.Typography.caption)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                
                Text(event.timestamp.timeAgoDisplay)
                    .font(KeepsDesignSystem.Typography.caption2)
                    .foregroundColor(KeepsDesignSystem.Colors.tertiaryText)
            }
            
            Spacer()
        }
        .polishedCard()
    }
}

// MARK: - Project Analytics Summary
struct ProjectAnalyticsSummary: View {
    @ObservedObject var teamManager: TeamManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            PolishedSectionHeader(
                title: "Project Analytics",
                subtitle: "Performance overview"
            )
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: KeepsDesignSystem.Spacing.md) {
                AnalyticsCard(
                    title: "Active Projects",
                    value: "\(teamManager.teams.filter { $0.isActive }.count)",
                    icon: "folder.fill",
                    color: KeepsDesignSystem.Colors.primary
                )
                
                AnalyticsCard(
                    title: "Team Members",
                    value: "\(teamManager.teams.flatMap { $0.members }.count)",
                    icon: "person.3.fill",
                    color: KeepsDesignSystem.Colors.success
                )
                
                AnalyticsCard(
                    title: "Completed",
                    value: "\(teamManager.teams.filter { !$0.isActive }.count)",
                    icon: "checkmark.circle.fill",
                    color: KeepsDesignSystem.Colors.accent
                )
                
                AnalyticsCard(
                    title: "In Progress",
                    value: "\(teamManager.teams.filter { $0.isActive }.count)",
                    icon: "clock.fill",
                    color: KeepsDesignSystem.Colors.info
                )
            }
        }
    }
}

// MARK: - Analytics Card
struct AnalyticsCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.sm) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(color)
                
                Spacer()
            }
            
            Text(value)
                .font(KeepsDesignSystem.Typography.displaySmall)
                .fontWeight(.bold)
                .foregroundColor(KeepsDesignSystem.Colors.primaryText)
            
            Text(title)
                .font(KeepsDesignSystem.Typography.caption)
                .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .polishedCard()
    }
}

// MARK: - Collaborative Tools Section
struct CollaborativeToolsSection: View {
    @ObservedObject var teamManager: TeamManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            PolishedSectionHeader(
                title: "Collaborative Tools",
                subtitle: "Team collaboration features"
            )
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: KeepsDesignSystem.Spacing.md) {
                CollaborativeToolCard(
                    title: "Team Chat",
                    description: "Real-time messaging",
                    icon: "message.fill",
                    color: KeepsDesignSystem.Colors.primary
                ) {
                    // Handle team chat action
                }
                
                CollaborativeToolCard(
                    title: "Video Call",
                    description: "Start team meeting",
                    icon: "video.fill",
                    color: KeepsDesignSystem.Colors.success
                ) {
                    // Handle video call action
                }
                
                CollaborativeToolCard(
                    title: "File Sharing",
                    description: "Share documents",
                    icon: "doc.fill",
                    color: KeepsDesignSystem.Colors.accent
                ) {
                    // Handle file sharing action
                }
                
                CollaborativeToolCard(
                    title: "Task Board",
                    description: "Manage tasks",
                    icon: "list.bullet.rectangle",
                    color: KeepsDesignSystem.Colors.info
                ) {
                    // Handle task board action
                }
            }
        }
    }
}

// MARK: - Collaborative Tool Card
struct CollaborativeToolCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.sm) {
                Image(systemName: icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(color)
                
                Text(title)
                    .font(KeepsDesignSystem.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                
                Text(description)
                    .font(KeepsDesignSystem.Typography.caption)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .polishedCard()
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Models
struct TimelineEvent: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let timestamp: Date
    let type: EventType
    
    enum EventType {
        case milestone, member, project, meeting
        
        var icon: String {
            switch self {
            case .milestone: return "flag.checkered"
            case .member: return "person.badge.plus"
            case .project: return "folder.badge.plus"
            case .meeting: return "video"
            }
        }
        
        var color: Color {
            switch self {
            case .milestone: return KeepsDesignSystem.Colors.success
            case .member: return KeepsDesignSystem.Colors.primary
            case .project: return KeepsDesignSystem.Colors.accent
            case .meeting: return KeepsDesignSystem.Colors.info
            }
        }
    }
}

// MARK: - Date Extension
extension Date {
    var timeAgoDisplay: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: self, relativeTo: Date())
    }
}
