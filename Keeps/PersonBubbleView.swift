//
//  PersonBubbleView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Individual person bubble component with floating animations and interactions
/// Features dynamic sizing, status indicators, and smooth animations
struct PersonBubbleView: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager
    let index: Int
    let isSelected: Bool
    let isGroupingActive: Bool
    
    @State private var showingQuickActions = false
    
    var bubbleSize: CGFloat {
        let baseSize: CGFloat = 70
        return baseSize * person.interactionFrequency.bubbleScale
    }
    
    var body: some View {
        ZStack {
            // Apple Watch style circular icon
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            person.relationshipType.color.opacity(0.9),
                            person.relationshipType.color,
                            person.relationshipType.color.opacity(0.8)
                        ],
                        center: UnitPoint(x: 0.3, y: 0.3),
                        startRadius: bubbleSize * 0.1,
                        endRadius: bubbleSize * 0.6
                    )
                )
                .frame(width: bubbleSize, height: bubbleSize)
                .overlay(
                    Circle()
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.4),
                                    Color.white.opacity(0.1),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1.5
                        )
                )
                .shadow(
                    color: Color.black.opacity(0.25),
                    radius: isSelected ? 8 : 4,
                    x: 0,
                    y: isSelected ? 4 : 2
                )
                .scaleEffect(isSelected ? 1.2 : 1.0)
            
            // Apple Watch app icon content
            Group {
                if person.avatarImageName.contains("person") {
                    Image(systemName: person.avatarImageName)
                        .font(.system(size: bubbleSize * 0.4, weight: .medium))
                        .foregroundColor(.white)
                } else {
                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: bubbleSize * 0.3, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                }
            }
            .shadow(color: .black.opacity(0.4), radius: 2, x: 0, y: 1)
            
            // Status indicators for hexagon
            ZStack {
                // Note: Removed online status indicator as online tracking was removed

                // Favorite indicator
                if person.isFavorite {
                    VStack {
                        HStack {
                            Circle()
                                .fill(Color.yellow)
                                .frame(width: 8, height: 8)
                                .overlay(
                                    Image(systemName: "star.fill")
                                        .font(.system(size: 4, weight: .bold))
                                        .foregroundColor(.white)
                                )
                            Spacer()
                        }
                        Spacer()
                    }
                    .frame(width: bubbleSize, height: bubbleSize)
                    .padding(6)
                }
            }
        }
        .modifier(FloatingBubbleAnimation(index: index, isSelected: isSelected))
        .modifier(MagneticGroupingEffect(
            relationshipType: person.relationshipType,
            isGroupingActive: isGroupingActive
        ))
        .modifier(BubbleAppearanceAnimation(index: index))
        .modifier(BubbleInteractionEffect(
            onTap: {
                peopleManager.selectedPerson = person
            },
            onLongPress: {
                showingQuickActions = true
            }
        ))
        // NEW EMOTIONAL ANIMATIONS
        .modifier(HeartbeatAnimation(isFavorite: person.isFavorite))
        // Note: Removed BreathingAnimation as online tracking was removed
        .modifier(EmotionalStateIndicator(
            interactionFrequency: person.interactionFrequency,
            relationshipType: person.relationshipType
        ))
        .modifier(ConnectionWebEffect(
            relationshipType: person.relationshipType,
            isActive: isGroupingActive
        ))
        .contextMenu {
            PersonContextMenu(person: person, peopleManager: peopleManager)
        }
        .confirmationDialog("Quick Actions", isPresented: $showingQuickActions) {
            PersonQuickActionsButtons(person: person, peopleManager: peopleManager)
        }
    }
    

}

/// Context menu for person bubble
struct PersonContextMenu: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager
    
    var body: some View {
        Group {
            Button(action: {
                peopleManager.toggleFavorite(for: person)
            }) {
                Label(
                    person.isFavorite ? "Remove from Favorites" : "Add to Favorites",
                    systemImage: person.isFavorite ? "star.slash" : "star"
                )
            }
            
            Button(action: {
                // Open contact details
                peopleManager.selectedPerson = person
            }) {
                Label("View Details", systemImage: "person.crop.circle")
            }
            
            if !person.email.isEmpty {
                Button(action: {
                    // Open email
                    if let url = URL(string: "mailto:\(person.email)") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Label("Send Email", systemImage: "envelope")
                }
            }
            
            if !person.phone.isEmpty {
                Button(action: {
                    // Open phone
                    let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                    if let url = URL(string: "tel:\(cleanPhone)") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Label("Call", systemImage: "phone")
                }
            }
            
            Divider()
            
            Button(action: {
                peopleManager.removePerson(person)
            }) {
                Label("Remove Contact", systemImage: "trash")
            }
            .foregroundColor(.red)
        }
    }
}

/// Quick actions buttons for person
struct PersonQuickActionsButtons: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager

    var body: some View {
        Group {
            // Favorite toggle
            Button(person.isFavorite ? "Remove from Favorites" : "Add to Favorites") {
                peopleManager.toggleFavorite(for: person)
            }

            // Contact actions
            if !person.email.isEmpty {
                Button("Send Email") {
                    if let url = URL(string: "mailto:\(person.email)") {
                        UIApplication.shared.open(url)
                    }
                }
            }

            if !person.phone.isEmpty {
                Button("Call") {
                    let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                    if let url = URL(string: "tel:\(cleanPhone)") {
                        UIApplication.shared.open(url)
                    }
                }
            }

            // View details
            Button("View Details") {
                peopleManager.selectedPerson = person
            }

            // Cancel
            Button("Cancel", role: .cancel) { }
        }
    }
}





struct PersonBubbleView_Previews: PreviewProvider {
    static var previews: some View {
        let samplePerson = Person(
            name: "John Doe",
            role: "Developer",
            company: "TechCorp",
            email: "<EMAIL>",
            relationshipType: .colleague,
            interactionFrequency: .daily,
            isFavorite: true
        )

        let peopleManager = PeopleManager()

        return PersonBubbleView(
            person: samplePerson,
            peopleManager: peopleManager,
            index: 0,
            isSelected: false,
            isGroupingActive: false
        )
        .frame(width: 100, height: 100)
        .background(Color.gray.opacity(0.1))
    }
}
