//
//  EnhancedWeekDetailSheet.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Enhanced Week Detail Sheet
/// Revolutionary week detail interface with people interactions, team achievements, and collaborative features

struct EnhancedWeekDetailSheet: View {
    let weekNumber: Int
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var teamManager: TeamManager
    @Binding var isPresented: Bool
    
    @State private var selectedTab: DetailTab = .reflection
    @State private var weekEntry: WeekEntry
    @State private var showingPeopleSelector = false
    @State private var showingTeamSelector = false
    @State private var showingCollaborationInvite = false
    
    init(weekNumber: Int, timelineManager: EvolutionTimelineManager, peopleManager: PeopleManager, teamManager: TeamManager, isPresented: Binding<Bool>) {
        self.weekNumber = weekNumber
        self.timelineManager = timelineManager
        self.peopleManager = peopleManager
        self.teamManager = teamManager
        self._isPresented = isPresented
        self._weekEntry = State(initialValue: timelineManager.getWeekEntry(for: weekNumber))
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Enhanced header
                enhancedHeaderView
                
                // Tab navigation
                tabNavigationView
                
                // Content area
                TabView(selection: $selectedTab) {
                    // Personal reflection
                    PersonalReflectionView(weekEntry: $weekEntry)
                        .tag(DetailTab.reflection)
                    
                    // People interactions
                    PlaceholderPeopleInteractionsView(
                        weekNumber: weekNumber,
                        showingPeopleSelector: $showingPeopleSelector
                    )
                    .tag(DetailTab.people)

                    // Team achievements
                    PlaceholderTeamAchievementsView(
                        weekNumber: weekNumber,
                        showingTeamSelector: $showingTeamSelector
                    )
                    .tag(DetailTab.teams)

                    // Collaborative features
                    PlaceholderCollaborativeView(
                        weekNumber: weekNumber,
                        showingCollaborationInvite: $showingCollaborationInvite
                    )
                    .tag(DetailTab.collaborative)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .background(KeepsDesignSystem.Colors.groupedBackground)
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingPeopleSelector) {
            PlaceholderSheet(title: "People Selector")
        }
        .sheet(isPresented: $showingTeamSelector) {
            PlaceholderSheet(title: "Team Selector")
        }
        .sheet(isPresented: $showingCollaborationInvite) {
            PlaceholderSheet(title: "Collaboration Invite")
        }
    }
    
    // MARK: - Enhanced Header
    private var enhancedHeaderView: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.md) {
            // Top bar with close and save
            HStack {
                Button("Cancel") {
                    isPresented = false
                }
                .foregroundColor(KeepsDesignSystem.Colors.error)
                
                Spacer()
                
                Text("Week \(weekNumber)")
                    .font(KeepsDesignSystem.Typography.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                
                Spacer()
                
                Button("Save") {
                    saveWeekEntry()
                    isPresented = false
                }
                .foregroundColor(KeepsDesignSystem.Colors.primary)
                .fontWeight(.semibold)
            }
            
            // Week info
            VStack(spacing: KeepsDesignSystem.Spacing.xs) {
                Text(weekEntry.startDate, format: .dateTime.weekday(.wide).month(.wide).day().year())
                    .font(KeepsDesignSystem.Typography.subheadline)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                
                // Connection status
                HStack(spacing: KeepsDesignSystem.Spacing.md) {
                    ConnectionStatusIndicator(
                        icon: "person.2.fill",
                        count: weekEntry.linkedPeople.count,
                        color: KeepsDesignSystem.Colors.primary,
                        title: "People"
                    )
                    
                    ConnectionStatusIndicator(
                        icon: "briefcase.fill",
                        count: weekEntry.linkedProjects.count,
                        color: KeepsDesignSystem.Colors.success,
                        title: "Projects"
                    )
                    
                    ConnectionStatusIndicator(
                        icon: "heart.fill",
                        count: 0, // Social events count
                        color: KeepsDesignSystem.Colors.accent,
                        title: "Social"
                    )
                }
            }
        }
        .padding(KeepsDesignSystem.Spacing.lg)
        .background(KeepsDesignSystem.Colors.background)
    }
    
    // MARK: - Tab Navigation
    private var tabNavigationView: some View {
        HStack(spacing: 0) {
            ForEach(DetailTab.allCases, id: \.self) { tab in
                DetailTabButton(
                    tab: tab,
                    isSelected: selectedTab == tab
                ) {
                    withAnimation(KeepsDesignSystem.Animations.spring) {
                        selectedTab = tab
                    }
                }
            }
        }
        .background(KeepsDesignSystem.Colors.secondaryBackground)
    }
    
    // MARK: - Helper Methods
    private func saveWeekEntry() {
        weekEntry.lastModified = Date()
        weekEntry.updateCompletionStatus()
        // Save the week entry using the timeline manager
        // timelineManager.saveWeekEntry(weekEntry) // TODO: Implement save method
    }
}

// MARK: - Detail Tabs
enum DetailTab: String, CaseIterable {
    case reflection = "Reflection"
    case people = "People"
    case teams = "Teams"
    case collaborative = "Collaborate"
    
    var icon: String {
        switch self {
        case .reflection: return "book.fill"
        case .people: return "person.2.fill"
        case .teams: return "person.3.fill"
        case .collaborative: return "person.3.sequence.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .reflection: return KeepsDesignSystem.Colors.primary
        case .people: return KeepsDesignSystem.Colors.success
        case .teams: return KeepsDesignSystem.Colors.accent
        case .collaborative: return KeepsDesignSystem.Colors.info
        }
    }
}

// MARK: - Detail Tab Button
struct DetailTabButton: View {
    let tab: DetailTab
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: KeepsDesignSystem.Spacing.xs) {
                Image(systemName: tab.icon)
                    .font(.system(size: 16, weight: .medium))
                
                Text(tab.rawValue)
                    .font(KeepsDesignSystem.Typography.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? tab.color : KeepsDesignSystem.Colors.secondaryText)
            .frame(maxWidth: .infinity)
            .padding(.vertical, KeepsDesignSystem.Spacing.sm)
            .background(
                RoundedRectangle(cornerRadius: KeepsDesignSystem.CornerRadius.sm)
                    .fill(isSelected ? tab.color.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Connection Status Indicator
struct ConnectionStatusIndicator: View {
    let icon: String
    let count: Int
    let color: Color
    let title: String
    
    var body: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.xs) {
            HStack(spacing: KeepsDesignSystem.Spacing.xs) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(color)
                
                Text("\(count)")
                    .font(KeepsDesignSystem.Typography.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }
            
            Text(title)
                .font(KeepsDesignSystem.Typography.caption2)
                .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
        }
        .padding(.horizontal, KeepsDesignSystem.Spacing.sm)
        .padding(.vertical, KeepsDesignSystem.Spacing.xs)
        .background(
            RoundedRectangle(cornerRadius: KeepsDesignSystem.CornerRadius.sm)
                .fill(color.opacity(0.1))
        )
    }
}

// MARK: - Personal Reflection View
struct PersonalReflectionView: View {
    @Binding var weekEntry: WeekEntry
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.lg) {
                // Week title
                VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.sm) {
                    Text("Week Title")
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                    
                    TextField("What defined this week?", text: $weekEntry.title)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                // Key insight
                VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.sm) {
                    Text("Key Insight")
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                    
                    TextField("What did you learn or realize?", text: $weekEntry.insight, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3...6)
                }
                
                // Accomplishments
                VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.sm) {
                    HStack {
                        Text("Accomplishments")
                            .font(KeepsDesignSystem.Typography.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                        
                        Spacer()
                        
                        Button(action: addAccomplishment) {
                            Image(systemName: "plus.circle.fill")
                                .foregroundColor(KeepsDesignSystem.Colors.primary)
                        }
                    }
                    
                    ForEach(Array(weekEntry.accomplishments.enumerated()), id: \.offset) { index, accomplishment in
                        HStack {
                            TextField("Accomplishment", text: Binding(
                                get: { weekEntry.accomplishments[index] },
                                set: { weekEntry.accomplishments[index] = $0 }
                            ))
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            
                            Button(action: { removeAccomplishment(at: index) }) {
                                Image(systemName: "minus.circle.fill")
                                    .foregroundColor(KeepsDesignSystem.Colors.error)
                            }
                        }
                    }
                }
                
                // Emotional tag
                VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.sm) {
                    Text("Emotional Tag")
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: KeepsDesignSystem.Spacing.sm) {
                        ForEach(EmotionalTag.allCases, id: \.self) { tag in
                            EmotionalTagButton(
                                tag: tag,
                                isSelected: weekEntry.emotionalTag == tag
                            ) {
                                weekEntry.emotionalTag = tag
                            }
                        }
                    }
                }
                
                Spacer(minLength: 100)
            }
            .padding(KeepsDesignSystem.Spacing.lg)
        }
    }
    
    private func addAccomplishment() {
        weekEntry.accomplishments.append("")
    }
    
    private func removeAccomplishment(at index: Int) {
        weekEntry.accomplishments.remove(at: index)
    }
}

// MARK: - Emotional Tag Button
struct EmotionalTagButton: View {
    let tag: EmotionalTag
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: KeepsDesignSystem.Spacing.xs) {
                Text(tag.emoji)
                    .font(.system(size: 20))
                
                Text(tag.rawValue)
                    .font(KeepsDesignSystem.Typography.caption2)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : tag.color)
            .frame(maxWidth: .infinity)
            .padding(.vertical, KeepsDesignSystem.Spacing.sm)
            .background(
                RoundedRectangle(cornerRadius: KeepsDesignSystem.CornerRadius.sm)
                    .fill(isSelected ? tag.color : tag.color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Placeholder Views
struct PlaceholderPeopleInteractionsView: View {
    let weekNumber: Int
    @Binding var showingPeopleSelector: Bool

    var body: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.lg) {
            Image(systemName: "person.2.fill")
                .font(.system(size: 48))
                .foregroundColor(KeepsDesignSystem.Colors.primary.opacity(0.6))

            Text("People Interactions")
                .font(KeepsDesignSystem.Typography.headline)
                .foregroundColor(KeepsDesignSystem.Colors.primaryText)

            Text("Track interactions with people during week \(weekNumber)")
                .font(KeepsDesignSystem.Typography.body)
                .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                .multilineTextAlignment(.center)

            PolishedButton(
                action: { showingPeopleSelector = true },
                style: .primary,
                accessibilityLabel: "Add People Interaction"
            ) {
                Text("Add Interaction")
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(KeepsDesignSystem.Spacing.lg)
    }
}

struct PlaceholderTeamAchievementsView: View {
    let weekNumber: Int
    @Binding var showingTeamSelector: Bool

    var body: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.lg) {
            Image(systemName: "trophy.fill")
                .font(.system(size: 48))
                .foregroundColor(KeepsDesignSystem.Colors.success.opacity(0.6))

            Text("Team Achievements")
                .font(KeepsDesignSystem.Typography.headline)
                .foregroundColor(KeepsDesignSystem.Colors.primaryText)

            Text("Record team milestones and achievements for week \(weekNumber)")
                .font(KeepsDesignSystem.Typography.body)
                .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                .multilineTextAlignment(.center)

            PolishedButton(
                action: { showingTeamSelector = true },
                style: .primary,
                accessibilityLabel: "Add Team Achievement"
            ) {
                Text("Add Achievement")
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(KeepsDesignSystem.Spacing.lg)
    }
}

struct PlaceholderCollaborativeView: View {
    let weekNumber: Int
    @Binding var showingCollaborationInvite: Bool

    var body: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.lg) {
            Image(systemName: "person.3.sequence.fill")
                .font(.system(size: 48))
                .foregroundColor(KeepsDesignSystem.Colors.info.opacity(0.6))

            Text("Collaborative Timeline")
                .font(KeepsDesignSystem.Typography.headline)
                .foregroundColor(KeepsDesignSystem.Colors.primaryText)

            Text("Share and collaborate on week \(weekNumber) with your team")
                .font(KeepsDesignSystem.Typography.body)
                .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                .multilineTextAlignment(.center)

            PolishedButton(
                action: { showingCollaborationInvite = true },
                style: .primary,
                accessibilityLabel: "Start Collaboration"
            ) {
                Text("Start Collaboration")
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(KeepsDesignSystem.Spacing.lg)
    }
}

struct PlaceholderSheet: View {
    let title: String
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: KeepsDesignSystem.Spacing.lg) {
                Image(systemName: "gear")
                    .font(.system(size: 48))
                    .foregroundColor(KeepsDesignSystem.Colors.secondary.opacity(0.6))

                Text("Coming Soon")
                    .font(KeepsDesignSystem.Typography.headline)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)

                Text("\(title) functionality will be available in a future update.")
                    .font(KeepsDesignSystem.Typography.body)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding(KeepsDesignSystem.Spacing.lg)
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
