//
//  EvolutionTimelineView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI
import SwiftUIX

/// Clean, professional Timeline interface focused on content and usability
/// Inspired by Apple's design principles with purposeful animations
struct EvolutionTimelineView: View {
    @StateObject private var timelineManager = EvolutionTimelineManager()
    @State private var selectedWeekNumber: Int?
    @State private var showingSearch = false
    @State private var showingBirthDateSetup = false
    @State private var showingWeekDetail = false
    @State private var dragOffset: CGFloat = 0
    @State private var isDragging = false

    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                HStack(spacing: 0) {
                    // Left: Clean Timeline Scrubber
                    TimelineScrubber(
                        timelineManager: timelineManager,
                        selectedWeekNumber: $selectedWeekNumber,
                        dragOffset: $dragOffset,
                        isDragging: $isDragging,
                        geometry: geometry
                    )
                    .frame(width: 80)

                    // Right: Week Content Area
                    WeekContentArea(
                        selectedWeekNumber: selectedWeekNumber,
                        timelineManager: timeline<PERSON>ana<PERSON>,
                        showingWeekDetail: $showingWeekDetail
                    )
                }
                .background(Color(.systemBackground))
            }
            .navigationTitle("Timeline")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 16) {
                        Button(action: { showingSearch = true }) {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.primary)
                        }

                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                timelineManager.navigateToCurrentWeek()
                                selectedWeekNumber = timelineManager.currentWeekNumber
                            }
                        }) {
                            Text("Today")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingSearch) {
            TimelineSearchView(timelineManager: timelineManager)
        }
        .sheet(isPresented: $showingBirthDateSetup) {
            BirthDateSetupView(timelineManager: timelineManager, isPresented: $showingBirthDateSetup)
        }
        .sheet(isPresented: $showingWeekDetail) {
            if let weekNumber = selectedWeekNumber {
                WeekDetailSheet(
                    weekNumber: weekNumber,
                    timelineManager: timelineManager,
                    isPresented: $showingWeekDetail
                )
            }
        }
        .onAppear {
            if timelineManager.needsBirthDateSetup {
                showingBirthDateSetup = true
            } else {
                // Use async dispatch to avoid state updates during view rendering
                DispatchQueue.main.async {
                    selectedWeekNumber = timelineManager.currentWeekNumber
                }
            }
        }
    }
}

// MARK: - Timeline Scrubber

/// Clean, Apple-inspired timeline scrubber with smooth interactions
struct TimelineScrubber: View {
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Binding var selectedWeekNumber: Int?
    @Binding var dragOffset: CGFloat
    @Binding var isDragging: Bool
    let geometry: GeometryProxy

    @State private var sliderValue: Double = 0
    @State private var isExpanded: Bool = false

    private let scrubberWidth: CGFloat = 60
    private let expandedWidth: CGFloat = 120

    var body: some View {
        VStack(spacing: 0) {
            // Compact timeline scrubber
            timelineScrubberView
        }
        .frame(width: isExpanded ? expandedWidth : scrubberWidth)
        .background(Color(.systemGray6).opacity(0.8))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isExpanded)
    }

    private var timelineScrubberView: some View {
        VStack(spacing: 12) {
            // Current week indicator
            currentWeekIndicator

            // Main slider
            VStack(spacing: 8) {
                // Progress indicator
                progressIndicator

                // Vertical slider
                GeometryReader { sliderGeometry in
                    VStack(spacing: 0) {
                        // Slider track
                        Rectangle()
                            .fill(Color(.systemGray4))
                            .frame(width: 4)
                            .overlay(
                                // Progress fill
                                VStack {
                                    Rectangle()
                                        .fill(Color.blue)
                                        .frame(width: 4, height: sliderGeometry.size.height * CGFloat(sliderValue / Double(TimelineConfiguration.totalLifeWeeks)))
                                    Spacer()
                                }
                            )
                            .overlay(
                                // Slider thumb
                                Circle()
                                    .fill(Color.blue)
                                    .frame(width: 12, height: 12)
                                    .offset(y: sliderGeometry.size.height * CGFloat(sliderValue / Double(TimelineConfiguration.totalLifeWeeks)) - sliderGeometry.size.height/2)
                                    .gesture(
                                        DragGesture()
                                            .onChanged { value in
                                                isExpanded = true
                                                let newValue = Double(value.location.y / sliderGeometry.size.height) * Double(TimelineConfiguration.totalLifeWeeks)
                                                sliderValue = max(1, min(Double(TimelineConfiguration.totalLifeWeeks), newValue))
                                                selectedWeekNumber = Int(sliderValue)
                                            }
                                            .onEnded { _ in
                                                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                                    isExpanded = false
                                                }
                                            }
                                    )
                            )
                    }
                }
                .frame(height: geometry.size.height * 0.6)

                // Year markers
                if isExpanded {
                    yearMarkersView
                }
            }
            .padding(.horizontal, 8)

            Spacer()
        }
        .padding(.vertical, 16)
        .onAppear {
            if let currentWeek = selectedWeekNumber {
                sliderValue = Double(currentWeek)
            } else {
                sliderValue = Double(timelineManager.currentWeekNumber)
            }
        }
        .onChange(of: selectedWeekNumber) { _, newWeek in
            if let week = newWeek {
                sliderValue = Double(week)
            }
        }
    }

    private var currentWeekIndicator: some View {
        VStack(spacing: 4) {
            Text("Week")
                .font(.caption2)
                .foregroundColor(.secondary)

            Text("\(Int(sliderValue))")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
                .frame(minWidth: 40)
        }
    }

    private var progressIndicator: some View {
        VStack(spacing: 2) {
            Text("\(Int((sliderValue / Double(TimelineConfiguration.totalLifeWeeks)) * 100))%")
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(.blue)

            Text("Life")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }

    private var yearMarkersView: some View {
        VStack(spacing: 4) {
            ForEach([0, 10, 20, 30, 40, 50, 60, 70], id: \.self) { year in
                HStack(spacing: 4) {
                    Text("\(year)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .frame(width: 20)

                    Rectangle()
                        .fill(Color(.systemGray4))
                        .frame(width: 20, height: 1)
                }
            }
        }
    }
}



// MARK: - Week Content Area

/// Clean content area showing week details with professional layout
struct WeekContentArea: View {
    let selectedWeekNumber: Int?
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Binding var showingWeekDetail: Bool

    var body: some View {
        Group {
            if let weekNumber = selectedWeekNumber {
                WeekContentView(
                    weekNumber: weekNumber,
                    timelineManager: timelineManager,
                    showingWeekDetail: $showingWeekDetail
                )
            } else {
                TimelineEmptyStateView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// MARK: - Week Content View

/// Professional week content display with clean typography and layout
struct WeekContentView: View {
    let weekNumber: Int
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Binding var showingWeekDetail: Bool

    private var weekEntry: WeekEntry {
        timelineManager.getWeekEntry(for: weekNumber)
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // Week Header
                weekHeaderView

                // Quick Stats
                weekStatsView

                // Content Preview
                if weekEntry.hasContent {
                    contentPreviewView
                } else {
                    emptyWeekPromptView
                }

                Spacer(minLength: 100)
            }
            .padding(.horizontal, 24)
            .padding(.top, 20)
        }
    }

    private var weekHeaderView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Week \(weekNumber)")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Spacer()

                if weekEntry.isCurrentWeek {
                    Text("Current")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(.blue, in: Capsule())
                }
            }

            Text(weekEntry.startDate, format: .dateTime.weekday(.wide).month(.wide).day().year())
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }

    private var weekStatsView: some View {
        HStack(spacing: 20) {
            StatItem(
                title: "Progress",
                value: "\(Int((Double(weekNumber) / Double(TimelineConfiguration.totalLifeWeeks)) * 100))%",
                color: .blue
            )

            StatItem(
                title: "Status",
                value: weekEntry.hasContent ? "Complete" : "Empty",
                color: weekEntry.hasContent ? .green : .orange
            )

            if !weekEntry.accomplishments.isEmpty {
                StatItem(
                    title: "Goals",
                    value: "\(weekEntry.accomplishments.count)",
                    color: .purple
                )
            }

            Spacer()
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(Color(.systemGray6).opacity(0.5), in: RoundedRectangle(cornerRadius: 12))
    }
}

extension WeekContentView {
    private var contentPreviewView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Title
            if !weekEntry.title.isEmpty {
                ContentCard(title: "Week Title", icon: "text.quote") {
                    Text(weekEntry.title)
                        .font(.body)
                        .foregroundColor(.primary)
                }
            }

            // Insight
            if !weekEntry.insight.isEmpty {
                ContentCard(title: "Key Insight", icon: "lightbulb") {
                    Text(weekEntry.insight)
                        .font(.body)
                        .foregroundColor(.primary)
                        .lineLimit(3)
                }
            }

            // Accomplishments
            if !weekEntry.accomplishments.isEmpty {
                ContentCard(title: "Accomplishments", icon: "checkmark.seal") {
                    VStack(alignment: .leading, spacing: 8) {
                        ForEach(Array(weekEntry.accomplishments.enumerated()), id: \.offset) { _, accomplishment in
                            HStack(spacing: 8) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.caption)

                                Text(accomplishment)
                                    .font(.body)
                                    .foregroundColor(.primary)

                                Spacer()
                            }
                        }
                    }
                }
            }

            // Edit button
            Button(action: { showingWeekDetail = true }) {
                HStack {
                    Image(systemName: "pencil")
                    Text("Edit Week")
                }
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.blue)
                .padding(.vertical, 12)
                .padding(.horizontal, 20)
                .background(Color.blue.opacity(0.1), in: RoundedRectangle(cornerRadius: 8))
            }
        }
    }

    private var emptyWeekPromptView: some View {
        VStack(spacing: 20) {
            Image(systemName: "plus.circle")
                .font(.system(size: 48))
                .foregroundColor(.blue.opacity(0.6))

            VStack(spacing: 8) {
                Text("Capture This Week")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("Add your thoughts, insights, and accomplishments to make this week memorable.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
            }

            Button(action: { showingWeekDetail = true }) {
                HStack {
                    Image(systemName: "pencil")
                    Text("Start Writing")
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.vertical, 12)
                .padding(.horizontal, 24)
                .background(.blue, in: Capsule())
            }
        }
        .padding(.vertical, 40)
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Supporting Components

/// Clean stat item for displaying metrics
struct StatItem: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)

            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

/// Clean content card for displaying week information
struct ContentCard<Content: View>: View {
    let title: String
    let icon: String
    @ViewBuilder let content: Content

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                    .font(.subheadline)

                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()
            }

            content
        }
        .padding(16)
        .background(Color(.systemGray6).opacity(0.5), in: RoundedRectangle(cornerRadius: 12))
    }
}

/// Clean empty state when no week is selected
struct TimelineEmptyStateView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "calendar")
                .font(.system(size: 64))
                .foregroundColor(.blue.opacity(0.4))

            VStack(spacing: 8) {
                Text("Select a Week")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("Choose a week from the timeline to view and edit your life's journey.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
            }
        }
        .padding(.horizontal, 40)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Week Detail Sheet

/// Professional full-screen week editing interface
struct WeekDetailSheet: View {
    let weekNumber: Int
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @Binding var isPresented: Bool

    // Local state for editing
    @State private var title: String = ""
    @State private var insight: String = ""
    @State private var accomplishments: [String] = []
    @State private var emotionalTag: EmotionalTag = .neutral

    private var weekEntry: WeekEntry {
        timelineManager.getWeekEntry(for: weekNumber)
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Week Header
                    weekHeaderSection

                    // Content Sections
                    VStack(alignment: .leading, spacing: 20) {
                        titleSection
                        insightSection
                        accomplishmentsSection
                        emotionalTagSection
                    }
                    .padding(.horizontal, 20)

                    Spacer(minLength: 100)
                }
                .padding(.top, 20)
            }
            .navigationTitle("Week \(weekNumber)")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        isPresented = false
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        saveChanges()
                        isPresented = false
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            loadWeekData()
        }
    }

    private func loadWeekData() {
        let entry = weekEntry
        title = entry.title
        insight = entry.insight
        accomplishments = entry.accomplishments
        emotionalTag = entry.emotionalTag
    }

    private func saveChanges() {
        // Ensure the week entry exists in the manager's storage
        let storedEntry = timelineManager.createWeekEntry(for: weekNumber)

        // Update the stored entry's properties
        storedEntry.title = title
        storedEntry.insight = insight
        storedEntry.accomplishments = accomplishments
        storedEntry.emotionalTag = emotionalTag
        storedEntry.lastModified = Date()
        storedEntry.updateCompletionStatus()

        timelineManager.updateWeekEntry(storedEntry)
    }

    private var weekHeaderSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(weekEntry.startDate, format: .dateTime.weekday(.wide).month(.wide).day().year())
                .font(.subheadline)
                .foregroundColor(.secondary)

            if weekEntry.isCurrentWeek {
                Text("Current Week")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(.blue, in: Capsule())
            }
        }
        .padding(.horizontal, 20)
    }
}

extension WeekDetailSheet {
    private var titleSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Week Title")
                .font(.headline)
                .fontWeight(.semibold)

            TextField("What defined this week?", text: $title)
                .textFieldStyle(.roundedBorder)
        }
    }

    private var insightSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Key Insight")
                .font(.headline)
                .fontWeight(.semibold)

            TextEditor(text: $insight)
                .frame(minHeight: 120)
                .padding(12)
                .background(Color(.systemGray6), in: RoundedRectangle(cornerRadius: 8))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
        }
    }

    private var accomplishmentsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Accomplishments")
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(Array(accomplishments.enumerated()), id: \.offset) { index, accomplishment in
                HStack {
                    TextField("Accomplishment", text: Binding(
                        get: { accomplishment },
                        set: { accomplishments[index] = $0 }
                    ))
                    .textFieldStyle(.roundedBorder)

                    Button(action: {
                        accomplishments.remove(at: index)
                    }) {
                        Image(systemName: "minus.circle.fill")
                            .foregroundColor(.red)
                    }
                }
            }

            Button(action: {
                accomplishments.append("")
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Add Accomplishment")
                }
                .foregroundColor(.blue)
            }
        }
    }

    private var emotionalTagSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Emotional Theme")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                ForEach(EmotionalTag.allCases, id: \.self) { tag in
                    Button(action: {
                        emotionalTag = tag
                    }) {
                        HStack(spacing: 4) {
                            Text(tag.emoji)
                            Text(tag.rawValue)
                                .font(.caption)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 6)
                        .background(
                            emotionalTag == tag ? tag.color.opacity(0.2) : Color(.systemGray6),
                            in: Capsule()
                        )
                        .overlay(
                            Capsule()
                                .stroke(emotionalTag == tag ? tag.color : Color.clear, lineWidth: 1)
                        )
                    }
                    .foregroundColor(.primary)
                }
            }
        }
    }
}





















