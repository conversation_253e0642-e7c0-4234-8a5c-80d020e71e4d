//
//  SmartTeamFormationView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI
import SwiftUIX

/// Smart team formation interface with AI-powered recommendations
struct SmartTeamFormationView: View {
    @StateObject private var integrationManager: ProjectPeopleIntegrationManager
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var teamManager: TeamManager
    
    @State private var projectName: String = ""
    @State private var projectDescription: String = ""
    @State private var selectedProjectType: ProjectType = .software
    @State private var requiredSkills: [String] = []
    @State private var newSkill: String = ""
    @State private var teamSize: Int = 5
    @State private var showingSuggestions = false
    @State private var selectedSuggestion: TeamFormationSuggestion?
    @State private var showingRoleAssignment = false
    
    @Environment(\.dismiss) private var dismiss
    
    init(peopleManager: PeopleManager, teamManager: TeamManager) {
        self.peopleManager = peopleManager
        self.teamManager = teamManager
        self._integrationManager = StateObject(wrappedValue: ProjectPeopleIntegrationManager(
            context: PersistenceController.shared.container.viewContext,
            peopleManager: peopleManager,
            teamManager: teamManager
        ))
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Project Details
                    projectDetailsSection
                    
                    // Skills & Requirements
                    skillsSection
                    
                    // Team Size
                    teamSizeSection
                    
                    // Generate Suggestions Button
                    generateSuggestionsButton
                    
                    // Team Formation Suggestions
                    if showingSuggestions {
                        suggestionsSection
                    }
                }
                .padding()
            }
            .navigationTitle("Smart Team Formation")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(item: $selectedSuggestion) { suggestion in
            TeamFormationDetailView(
                suggestion: suggestion,
                integrationManager: integrationManager,
                teamManager: teamManager,
                onCreateTeam: { team in
                    dismiss()
                }
            )
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("AI-Powered Team Formation")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("Get intelligent recommendations based on relationships, skills, and collaboration history")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }
    
    private var projectDetailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Project Details")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                TextField("Project Name", text: $projectName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                TextField("Project Description", text: $projectDescription, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
                
                Picker("Project Type", selection: $selectedProjectType) {
                    ForEach(ProjectType.allCases, id: \.self) { type in
                        HStack {
                            Image(systemName: type.icon)
                            Text(type.rawValue)
                        }
                        .tag(type)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var skillsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Required Skills")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack {
                TextField("Add skill", text: $newSkill)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                Button("Add") {
                    if !newSkill.isEmpty {
                        requiredSkills.append(newSkill)
                        newSkill = ""
                    }
                }
                .buttonStyle(.borderedProminent)
                .disabled(newSkill.isEmpty)
            }
            
            if !requiredSkills.isEmpty {
                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 100))
                ], spacing: 8) {
                    ForEach(requiredSkills, id: \.self) { skill in
                        HStack {
                            Text(skill)
                                .font(.caption)
                                .fontWeight(.medium)
                            
                            Button {
                                requiredSkills.removeAll { $0 == skill }
                            } label: {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.caption)
                            }
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(16)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var teamSizeSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Team Size")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack {
                Text("Members: \(teamSize)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Stepper("", value: $teamSize, in: 2...10)
            }
            
            Slider(value: Binding(
                get: { Double(teamSize) },
                set: { teamSize = Int($0) }
            ), in: 2...10, step: 1)
            .accentColor(.blue)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var generateSuggestionsButton: some View {
        Button {
            generateTeamSuggestions()
        } label: {
            HStack {
                Image(systemName: "sparkles")
                Text("Generate Team Suggestions")
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                LinearGradient(
                    colors: [.blue, .purple],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .disabled(projectName.isEmpty || requiredSkills.isEmpty)
        .opacity(projectName.isEmpty || requiredSkills.isEmpty ? 0.6 : 1.0)
    }
    
    private var suggestionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Team Formation Suggestions")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVStack(spacing: 12) {
                ForEach(integrationManager.teamFormationSuggestions) { suggestion in
                    TeamSuggestionCard(suggestion: suggestion) {
                        selectedSuggestion = suggestion
                    }
                }
            }
        }
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showingSuggestions)
    }
    
    // MARK: - Actions
    
    private func generateTeamSuggestions() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            let suggestions = integrationManager.generateTeamFormationSuggestions(
                for: selectedProjectType,
                requiredSkills: requiredSkills,
                teamSize: teamSize
            )
            showingSuggestions = !suggestions.isEmpty
        }
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Team Suggestion Card

struct TeamSuggestionCard: View {
    let suggestion: TeamFormationSuggestion
    let onSelect: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: suggestion.type.icon)
                    .foregroundColor(suggestion.type.color)
                
                Text(suggestion.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(Int(suggestion.score * 100))%")
                    .font(.caption)
                    .fontWeight(.bold)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(suggestion.type.color.opacity(0.2))
                    .foregroundColor(suggestion.type.color)
                    .cornerRadius(8)
            }
            
            Text(suggestion.reasoning)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(suggestion.members.prefix(5), id: \.id) { member in
                        VStack(spacing: 4) {
                            Circle()
                                .fill(Color.blue.opacity(0.2))
                                .frame(width: 40, height: 40)
                                .overlay(
                                    Text(String(member.name.prefix(1)))
                                        .font(.caption)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.blue)
                                )
                            
                            Text(member.name.components(separatedBy: " ").first ?? "")
                                .font(.caption2)
                                .lineLimit(1)
                        }
                    }
                    
                    if suggestion.members.count > 5 {
                        Text("+\(suggestion.members.count - 5)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 4)
            }
            
            Button("View Details") {
                onSelect()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.small)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    SmartTeamFormationView(
        peopleManager: PeopleManager(),
        teamManager: TeamManager()
    )
}
