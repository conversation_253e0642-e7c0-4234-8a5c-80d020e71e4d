//
//  FloatingBubbleLayout.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI

/// Custom layout for floating bubbles with physics-like behavior
/// Creates an organic, flowing arrangement of person bubbles
struct FloatingBubbleLayout: Layout {
    let containerSize: CGSize
    let bubbleSize: CGFloat = 80
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        return proposal.replacingUnspecifiedDimensions(by: containerSize)
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let positions = generateBubblePositions(count: subviews.count, in: bounds)
        
        for (index, subview) in subviews.enumerated() {
            if index < positions.count {
                let position = positions[index]
                subview.place(
                    at: position,
                    anchor: .center,
                    proposal: ProposedViewSize(width: bubbleSize, height: bubbleSize)
                )
            }
        }
    }
    
    /// Generate Apple Watch honeycomb positions
    private func generateBubblePositions(count: Int, in bounds: CGRect) -> [CGPoint] {
        var positions: [CGPoint] = []
        let center = CGPoint(x: bounds.midX, y: bounds.midY)

        if count == 0 { return positions }

        // Apple Watch honeycomb pattern
        let hexRadius: CGFloat = 85 // Distance between hexagon centers

        // Start with center hexagon
        positions.append(center)

        if count == 1 { return positions }

        // Ring 1: 6 hexagons around center
        for i in 0..<min(6, count - 1) {
            let angle = Double(i) * .pi / 3 // 60 degrees apart
            let x = center.x + cos(angle) * hexRadius
            let y = center.y + sin(angle) * hexRadius
            positions.append(CGPoint(x: x, y: y))
        }

        if count <= 7 { return clampedPositions(positions, in: bounds) }

        // Ring 2: 12 hexagons around ring 1
        for i in 0..<min(12, count - 7) {
            let angle = Double(i) * .pi / 6 // 30 degrees apart
            let x = center.x + cos(angle) * hexRadius * 2
            let y = center.y + sin(angle) * hexRadius * 2
            positions.append(CGPoint(x: x, y: y))
        }

        if count <= 19 { return clampedPositions(positions, in: bounds) }

        // Ring 3: 18 hexagons around ring 2
        for i in 0..<min(18, count - 19) {
            let angle = Double(i) * .pi / 9 // 20 degrees apart
            let x = center.x + cos(angle) * hexRadius * 3
            let y = center.y + sin(angle) * hexRadius * 3
            positions.append(CGPoint(x: x, y: y))
        }

        return clampedPositions(positions, in: bounds)
    }

    private func clampedPositions(_ positions: [CGPoint], in bounds: CGRect) -> [CGPoint] {
        return positions.map { position in
            CGPoint(
                x: max(bubbleSize/2, min(bounds.width - bubbleSize/2, position.x)),
                y: max(bubbleSize/2, min(bounds.height - bubbleSize/2, position.y))
            )
        }
    }
}

/// Apple Watch style subtle animation
struct FloatingBubbleAnimation: ViewModifier {
    @State private var scale: CGFloat = 1.0
    @State private var isAnimating = false

    let index: Int
    let isSelected: Bool

    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .scaleEffect(isAnimating ? 1.02 : 1.0)
            .onAppear {
                startSubtleAnimation()
            }
            .onChange(of: isSelected) { _, selected in
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    scale = selected ? 1.15 : 1.0
                }
            }
    }

    private func startSubtleAnimation() {
        let delay = Double(index) * 0.1

        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            withAnimation(
                .easeInOut(duration: Double.random(in: 3...5))
                .repeatForever(autoreverses: true)
            ) {
                isAnimating = true
            }
        }
    }
}

/// Bubble interaction effects
struct BubbleInteractionEffect: ViewModifier {
    @State private var isPressed = false
    @State private var rippleOffset: CGFloat = 0
    
    let onTap: () -> Void
    let onLongPress: () -> Void
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .overlay(
                Circle()
                    .stroke(Color.blue.opacity(0.3), lineWidth: 2)
                    .scaleEffect(rippleOffset)
                    .opacity(1 - rippleOffset)
                    .animation(.easeOut(duration: 0.6), value: rippleOffset)
            )
            .onTapGesture {
                // Haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
                
                // Ripple effect
                rippleOffset = 0
                withAnimation(.easeOut(duration: 0.6)) {
                    rippleOffset = 1.5
                }
                
                onTap()
            }
            .onLongPressGesture(minimumDuration: 0.5) {
                // Haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                
                onLongPress()
            }

    }
}

/// Magnetic grouping effect for related bubbles
struct MagneticGroupingEffect: ViewModifier {
    let relationshipType: Person.RelationshipType
    let isGroupingActive: Bool
    @State private var groupOffset: CGSize = .zero
    
    func body(content: Content) -> some View {
        content
            .offset(groupOffset)
            .onChange(of: isGroupingActive) { _, isActive in
                withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                    if isActive {
                        // Move towards relationship group center
                        groupOffset = calculateGroupOffset()
                    } else {
                        // Return to original position
                        groupOffset = .zero
                    }
                }
            }
    }
    
    private func calculateGroupOffset() -> CGSize {
        // Professional clustering with clean positioning
        return getBaseOffset()
    }

    private func getBaseOffset() -> CGSize {
        // Clean clustering patterns for different relationship types
        switch relationshipType {
        case .colleague:
            return CGSize(width: -40, height: -30) // Professional cluster
        case .friend:
            return CGSize(width: 50, height: -30) // Social cluster
        case .family:
            return CGSize(width: 0, height: -60) // Family cluster
        case .client:
            return CGSize(width: -50, height: 30) // Business cluster
        case .vendor:
            return CGSize(width: 60, height: 30) // External cluster
        case .mentor:
            return CGSize(width: 0, height: 60) // Mentor cluster
        case .other:
            return CGSize(width: 0, height: 0) // Neutral position
        }
    }
}

/// Staggered appearance animation for bubbles
struct BubbleAppearanceAnimation: ViewModifier {
    let index: Int
    @State private var hasAppeared = false
    
    func body(content: Content) -> some View {
        content
            .opacity(hasAppeared ? 1 : 0)
            .scaleEffect(hasAppeared ? 1 : 0.3)
            .offset(y: hasAppeared ? 0 : 50)
            .onAppear {
                let delay = Double(index) * 0.05
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    withAnimation(
                        .spring(response: 0.6, dampingFraction: 0.8)
                    ) {
                        hasAppeared = true
                    }
                }
            }
    }
}

/// Enhanced breathing animation for online status with emotional depth
struct BreathingAnimation: ViewModifier {
    @State private var isAnimating = false
    @State private var breathScale: CGFloat = 1.0
    @State private var glowOpacity: Double = 0.0
    let isOnline: Bool

    func body(content: Content) -> some View {
        content
            .scaleEffect(breathScale)
            .overlay(
                // Emotional glow effect for online contacts
                Circle()
                    .stroke(
                        LinearGradient(
                            colors: [Color.green.opacity(0.6), Color.blue.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 2
                    )
                    .scaleEffect(1.2)
                    .opacity(glowOpacity)
                    .blur(radius: 3)
            )
            .onAppear {
                startBreathingAnimation()
            }
            .onChange(of: isOnline) { _, online in
                if online {
                    startBreathingAnimation()
                } else {
                    stopBreathingAnimation()
                }
            }
    }

    private func startBreathingAnimation() {
        guard isOnline else { return }

        withAnimation(
            .easeInOut(duration: 2.5)
            .repeatForever(autoreverses: true)
        ) {
            breathScale = 1.08
            glowOpacity = 0.7
            isAnimating = true
        }
    }

    private func stopBreathingAnimation() {
        withAnimation(.easeOut(duration: 0.5)) {
            breathScale = 1.0
            glowOpacity = 0.0
            isAnimating = false
        }
    }
}

/// Heartbeat animation for favorite contacts - creates emotional connection
struct HeartbeatAnimation: ViewModifier {
    @State private var heartScale: CGFloat = 1.0
    @State private var heartOpacity: Double = 0.0
    @State private var isBeating = false
    let isFavorite: Bool

    func body(content: Content) -> some View {
        content
            .scaleEffect(heartScale)
            .overlay(
                // Heart pulse overlay
                Circle()
                    .stroke(
                        RadialGradient(
                            colors: [Color.red.opacity(0.8), Color.pink.opacity(0.4), Color.clear],
                            center: .center,
                            startRadius: 5,
                            endRadius: 25
                        ),
                        lineWidth: 3
                    )
                    .scaleEffect(heartScale)
                    .opacity(heartOpacity)
                    .blur(radius: 1)
            )
            .onAppear {
                if isFavorite {
                    startHeartbeat()
                }
            }
            .onChange(of: isFavorite) { _, favorite in
                if favorite {
                    startHeartbeat()
                } else {
                    stopHeartbeat()
                }
            }
    }

    private func startHeartbeat() {
        guard isFavorite else { return }

        // Create heartbeat pattern: beat-beat-pause
        let beatAnimation = Animation.easeInOut(duration: 0.15)
        let pauseAnimation = Animation.easeInOut(duration: 0.8)

        Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
            guard isFavorite else { return }

            // First beat
            withAnimation(beatAnimation) {
                heartScale = 1.15
                heartOpacity = 0.8
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                withAnimation(beatAnimation) {
                    heartScale = 1.0
                    heartOpacity = 0.3
                }
            }

            // Second beat
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                withAnimation(beatAnimation) {
                    heartScale = 1.12
                    heartOpacity = 0.6
                }
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.55) {
                withAnimation(pauseAnimation) {
                    heartScale = 1.0
                    heartOpacity = 0.0
                }
            }
        }
    }

    private func stopHeartbeat() {
        withAnimation(.easeOut(duration: 0.3)) {
            heartScale = 1.0
            heartOpacity = 0.0
            isBeating = false
        }
    }
}

/// Connection web visualization showing relationship networks
struct ConnectionWebEffect: ViewModifier {
    @State private var connectionOpacity: Double = 0.0
    @State private var connectionOffset: CGFloat = 0.0
    let relationshipType: Person.RelationshipType
    let isActive: Bool

    func body(content: Content) -> some View {
        content
            .overlay(
                // Connection lines radiating outward
                ForEach(0..<6, id: \.self) { index in
                    Path { path in
                        let angle = Double(index) * .pi / 3
                        let startRadius: CGFloat = 35
                        let endRadius: CGFloat = 80 + connectionOffset

                        let startX = cos(angle) * startRadius
                        let startY = sin(angle) * startRadius
                        let endX = cos(angle) * endRadius
                        let endY = sin(angle) * endRadius

                        path.move(to: CGPoint(x: startX, y: startY))
                        path.addLine(to: CGPoint(x: endX, y: endY))
                    }
                    .stroke(
                        LinearGradient(
                            colors: [
                                relationshipType.color.opacity(0.6),
                                relationshipType.color.opacity(0.0)
                            ],
                            startPoint: .center,
                            endPoint: .trailing
                        ),
                        style: StrokeStyle(lineWidth: 2, lineCap: .round)
                    )
                    .opacity(connectionOpacity)
                }
            )
            .onChange(of: isActive) { _, active in
                if active {
                    withAnimation(
                        .easeInOut(duration: 1.5)
                        .repeatForever(autoreverses: true)
                    ) {
                        connectionOpacity = 0.4
                        connectionOffset = 20
                    }
                } else {
                    withAnimation(.easeOut(duration: 0.5)) {
                        connectionOpacity = 0.0
                        connectionOffset = 0
                    }
                }
            }
    }
}

/// Emotional state indicator with color psychology
struct EmotionalStateIndicator: ViewModifier {
    @State private var pulseScale: CGFloat = 1.0
    @State private var colorShift: Double = 0.0
    let interactionFrequency: Person.InteractionFrequency
    let relationshipType: Person.RelationshipType

    var emotionalColor: Color {
        let baseColor = relationshipType.color
        let intensity = interactionFrequency.emotionalIntensity

        return baseColor.opacity(0.3 + intensity * 0.7)
    }

    func body(content: Content) -> some View {
        content
            .overlay(
                // Emotional aura
                Circle()
                    .fill(
                        RadialGradient(
                            colors: [
                                emotionalColor.opacity(0.8),
                                emotionalColor.opacity(0.3),
                                Color.clear
                            ],
                            center: .center,
                            startRadius: 20,
                            endRadius: 50
                        )
                    )
                    .scaleEffect(pulseScale)
                    .blur(radius: 8)
                    .opacity(0.6)
            )
            .onAppear {
                startEmotionalPulse()
            }
    }

    private func startEmotionalPulse() {
        let duration = 3.0 - (interactionFrequency.emotionalIntensity * 1.5)

        withAnimation(
            .easeInOut(duration: duration)
            .repeatForever(autoreverses: true)
        ) {
            pulseScale = 1.0 + (interactionFrequency.emotionalIntensity * 0.3)
        }
    }
}
