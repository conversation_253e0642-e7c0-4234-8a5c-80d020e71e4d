//
//  OfflineSyncQueue.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import Foundation
import SwiftUI
import Combine
import Network

/// Manages offline operations and queues them for sync when connection is restored
class OfflineSyncQueue: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var queuedOperations: [QueuedOperation] = []
    @Published var isProcessingQueue = false
    @Published var queueStatistics = QueueStatistics()
    @Published var networkStatus: NetworkStatus = .unknown
    
    // MARK: - Private Properties
    
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "OfflineQueue.NetworkMonitor")
    private var cancellables = Set<AnyCancellable>()
    
    // UserDefaults key
    private let queuedOperationsKey = "QueuedOperations"
    
    // MARK: - Models
    
    struct QueuedOperation: Identifiable, Codable {
        let id = UUID()
        let operation: SyncOperation
        let queuedAt: Date
        var attempts: Int
        var lastAttempt: Date?
        var estimatedDataSize: Int64
        var dependencies: [UUID] // Operations that must complete first
        var isHighPriority: Bool
        
        init(operation: SyncOperation, estimatedDataSize: Int64 = 0, dependencies: [UUID] = [], isHighPriority: Bool = false) {
            self.operation = operation
            self.queuedAt = Date()
            self.attempts = 0
            self.estimatedDataSize = estimatedDataSize
            self.dependencies = dependencies
            self.isHighPriority = isHighPriority
        }
        
        var waitTime: TimeInterval {
            return Date().timeIntervalSince(queuedAt)
        }
        
        var canExecute: Bool {
            return dependencies.isEmpty && attempts < 5
        }
    }
    
    struct QueueStatistics: Codable {
        var totalQueued: Int = 0
        var totalProcessed: Int = 0
        var totalFailed: Int = 0
        var averageWaitTime: TimeInterval = 0
        var largestQueueSize: Int = 0
        var totalDataQueued: Int64 = 0
        var lastQueueClear: Date?
        
        var successRate: Double {
            guard totalProcessed > 0 else { return 0 }
            return Double(totalProcessed - totalFailed) / Double(totalProcessed)
        }
        
        var currentQueueSize: Int = 0
    }
    
    enum NetworkStatus: String, CaseIterable {
        case online = "online"
        case offline = "offline"
        case limited = "limited"
        case unknown = "unknown"
        
        var description: String {
            switch self {
            case .online: return "Online"
            case .offline: return "Offline"
            case .limited: return "Limited Connection"
            case .unknown: return "Unknown"
            }
        }
        
        var color: Color {
            switch self {
            case .online: return .green
            case .offline: return .red
            case .limited: return .orange
            case .unknown: return .gray
            }
        }
        
        var icon: String {
            switch self {
            case .online: return "wifi"
            case .offline: return "wifi.slash"
            case .limited: return "wifi.exclamationmark"
            case .unknown: return "questionmark.circle"
            }
        }
    }
    
    // MARK: - Initialization
    
    init() {
        loadQueuedOperations()
        setupNetworkMonitoring()
        updateStatistics()
    }
    
    deinit {
        networkMonitor.cancel()
    }
    
    // MARK: - Public Methods
    
    /// Add an operation to the offline queue
    func enqueue(_ operation: SyncOperation, estimatedDataSize: Int64 = 0, dependencies: [UUID] = [], isHighPriority: Bool = false) {
        let queuedOperation = QueuedOperation(
            operation: operation,
            estimatedDataSize: estimatedDataSize,
            dependencies: dependencies,
            isHighPriority: isHighPriority
        )
        
        if isHighPriority {
            // Insert high priority operations at the beginning
            queuedOperations.insert(queuedOperation, at: 0)
        } else {
            queuedOperations.append(queuedOperation)
        }
        
        updateStatistics()
        saveQueuedOperations()
        
        // Try to process immediately if online
        if networkStatus == .online {
            Task {
                await processQueue()
            }
        }
    }
    
    /// Process all queued operations
    @MainActor
    func processQueue() async {
        guard !isProcessingQueue && networkStatus == .online else { return }
        
        isProcessingQueue = true
        
        let startTime = Date()
        var processedCount = 0
        var failedCount = 0
        
        // Sort operations by priority and dependencies
        let sortedOperations = sortOperationsByPriority()
        
        for queuedOperation in sortedOperations {
            guard queuedOperation.canExecute else { continue }
            
            do {
                try await processQueuedOperation(queuedOperation)
                
                // Remove from queue on success
                if let index = queuedOperations.firstIndex(where: { $0.id == queuedOperation.id }) {
                    queuedOperations.remove(at: index)
                }
                
                processedCount += 1
                
            } catch {
                print("❌ Failed to process queued operation: \(error)")
                
                // Update attempt count
                if let index = queuedOperations.firstIndex(where: { $0.id == queuedOperation.id }) {
                    queuedOperations[index].attempts += 1
                    queuedOperations[index].lastAttempt = Date()
                }
                
                failedCount += 1
            }
            
            // Check if we're still online
            if networkStatus != .online {
                break
            }
        }
        
        // Update statistics
        let processingTime = Date().timeIntervalSince(startTime)
        updateStatisticsAfterProcessing(processed: processedCount, failed: failedCount, time: processingTime)
        
        saveQueuedOperations()
        isProcessingQueue = false
    }
    
    /// Clear all queued operations
    func clearQueue() {
        queuedOperations.removeAll()
        queueStatistics.lastQueueClear = Date()
        updateStatistics()
        saveQueuedOperations()
    }
    
    /// Remove specific operation from queue
    func removeOperation(_ operationId: UUID) {
        queuedOperations.removeAll { $0.id == operationId }
        updateStatistics()
        saveQueuedOperations()
    }
    
    /// Get operations that are ready to execute
    func getExecutableOperations() -> [QueuedOperation] {
        return queuedOperations.filter { $0.canExecute }
    }
    
    /// Get operations waiting for dependencies
    func getDependentOperations() -> [QueuedOperation] {
        return queuedOperations.filter { !$0.dependencies.isEmpty }
    }
    
    /// Resolve dependencies when an operation completes
    func resolveDependencies(for completedOperationId: UUID) {
        for index in queuedOperations.indices {
            queuedOperations[index].dependencies.removeAll { $0 == completedOperationId }
        }
        saveQueuedOperations()
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path)
            }
        }
        networkMonitor.start(queue: networkQueue)
    }
    
    private func updateNetworkStatus(_ path: NWPath) {
        switch path.status {
        case .satisfied:
            if path.isExpensive {
                networkStatus = .limited
            } else {
                networkStatus = .online
                
                // Auto-process queue when coming online
                Task {
                    await processQueue()
                }
            }
        case .unsatisfied:
            networkStatus = .offline
        case .requiresConnection:
            networkStatus = .limited
        @unknown default:
            networkStatus = .unknown
        }
    }
    
    private func sortOperationsByPriority() -> [QueuedOperation] {
        return queuedOperations.sorted { lhs, rhs in
            // High priority first
            if lhs.isHighPriority != rhs.isHighPriority {
                return lhs.isHighPriority
            }
            
            // Then by operation priority
            if lhs.operation.priority != rhs.operation.priority {
                return lhs.operation.priority.rawValue > rhs.operation.priority.rawValue
            }
            
            // Then by queue time (older first)
            return lhs.queuedAt < rhs.queuedAt
        }
    }
    
    private func processQueuedOperation(_ queuedOperation: QueuedOperation) async throws {
        print("🔄 Processing queued operation: \(queuedOperation.operation.operationType) for \(queuedOperation.operation.entityType)")
        
        // Simulate processing time based on operation type
        let processingTime: UInt64 = switch queuedOperation.operation.operationType {
        case .create: 200_000_000 // 0.2 seconds
        case .update: 150_000_000 // 0.15 seconds
        case .delete: 100_000_000 // 0.1 seconds
        case .merge: 300_000_000 // 0.3 seconds
        case .restore: 500_000_000 // 0.5 seconds
        }
        
        try await Task.sleep(nanoseconds: processingTime)
        
        // Simulate potential failure
        if queuedOperation.attempts > 2 && Double.random(in: 0...1) < 0.1 {
            throw NSError(domain: "QueueProcessingError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Simulated processing failure"])
        }
    }
    
    private func updateStatistics() {
        queueStatistics.currentQueueSize = queuedOperations.count
        queueStatistics.largestQueueSize = max(queueStatistics.largestQueueSize, queuedOperations.count)
        queueStatistics.totalDataQueued = queuedOperations.reduce(0) { $0 + $1.estimatedDataSize }
        
        // Calculate average wait time
        if !queuedOperations.isEmpty {
            let totalWaitTime = queuedOperations.reduce(0) { $0 + $1.waitTime }
            queueStatistics.averageWaitTime = totalWaitTime / Double(queuedOperations.count)
        }
    }
    
    private func updateStatisticsAfterProcessing(processed: Int, failed: Int, time: TimeInterval) {
        queueStatistics.totalProcessed += processed
        queueStatistics.totalFailed += failed
        updateStatistics()
    }
    
    private func loadQueuedOperations() {
        if let data = UserDefaults.standard.data(forKey: queuedOperationsKey),
           let operations = try? JSONDecoder().decode([QueuedOperation].self, from: data) {
            queuedOperations = operations
        }
    }
    
    private func saveQueuedOperations() {
        if let data = try? JSONEncoder().encode(queuedOperations) {
            UserDefaults.standard.set(data, forKey: queuedOperationsKey)
        }
    }
    
    // MARK: - Queue Management
    
    /// Get queue health status
    func getQueueHealth() -> QueueHealth {
        let oldOperations = queuedOperations.filter { $0.waitTime > 3600 } // Older than 1 hour
        let failedOperations = queuedOperations.filter { $0.attempts >= 3 }
        let largeOperations = queuedOperations.filter { $0.estimatedDataSize > 1_000_000 } // > 1MB
        
        if failedOperations.count > queuedOperations.count / 2 {
            return .critical
        } else if oldOperations.count > 10 || largeOperations.count > 5 {
            return .warning
        } else if queuedOperations.count > 100 {
            return .attention
        } else {
            return .healthy
        }
    }
    
    enum QueueHealth: String, CaseIterable {
        case healthy = "healthy"
        case attention = "attention"
        case warning = "warning"
        case critical = "critical"
        
        var description: String {
            switch self {
            case .healthy: return "Healthy"
            case .attention: return "Needs Attention"
            case .warning: return "Warning"
            case .critical: return "Critical"
            }
        }
        
        var color: Color {
            switch self {
            case .healthy: return .green
            case .attention: return .blue
            case .warning: return .orange
            case .critical: return .red
            }
        }
        
        var icon: String {
            switch self {
            case .healthy: return "checkmark.circle"
            case .attention: return "info.circle"
            case .warning: return "exclamationmark.triangle"
            case .critical: return "xmark.circle"
            }
        }
    }
}
