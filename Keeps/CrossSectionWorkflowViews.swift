//
//  CrossSectionWorkflowViews.swift
//  Keeps
//
//  Revolutionary cross-section workflow UI with Apple-style animations
//

import SwiftUI

// MARK: - Main Workflow Interface

/// Main workflow interface with floating card design
struct CrossSectionWorkflowCard: View {
    @ObservedObject var workflowManager: CrossSectionWorkflowManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ZStack {
            // Background blur
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .onTapGesture {
                    workflowManager.cancelWorkflow()
                }
            
            // Workflow card
            VStack(spacing: 0) {
                // Header
                workflowHeader
                
                // Progress indicator
                workflowProgress
                
                // Current step content
                currentStepView
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                
                // Navigation buttons
                navigationButtons
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(.ultraThinMaterial)
                    .shadow(color: .black.opacity(0.2), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 20)
            .scaleEffect(workflowManager.showingWorkflowCard ? 1.0 : 0.8)
            .opacity(workflowManager.showingWorkflowCard ? 1.0 : 0.0)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: workflowManager.showingWorkflowCard)
        }
    }
    
    private var workflowHeader: some View {
        HStack {
            // Workflow icon and title
            HStack(spacing: 12) {
                Circle()
                    .fill(workflowManager.activeWorkflow?.type.color.opacity(0.2) ?? .gray.opacity(0.2))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(systemName: workflowManager.activeWorkflow?.type.icon ?? "questionmark")
                            .font(.headline)
                            .foregroundColor(workflowManager.activeWorkflow?.type.color ?? .gray)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(workflowManager.activeWorkflow?.type.rawValue ?? "Workflow")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(workflowManager.activeWorkflow?.type.description ?? "")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // Close button
            Button(action: workflowManager.cancelWorkflow) {
                Image(systemName: "xmark.circle.fill")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }
    
    private var workflowProgress: some View {
        VStack(spacing: 12) {
            // Progress bar
            ProgressView(value: workflowManager.workflowProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: workflowManager.activeWorkflow?.type.color ?? .blue))
                .scaleEffect(y: 2)
                .animation(.easeInOut(duration: 0.5), value: workflowManager.workflowProgress)
            
            // Step indicators
            if let workflow = workflowManager.activeWorkflow {
                HStack(spacing: 8) {
                    ForEach(Array(workflow.steps.enumerated()), id: \.element.id) { index, step in
                        stepIndicator(step: step, index: index, isActive: step.id == workflowManager.currentStep?.id)
                    }
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    private func stepIndicator(step: WorkflowStep, index: Int, isActive: Bool) -> some View {
        VStack(spacing: 4) {
            Circle()
                .fill(isActive ? (workflowManager.activeWorkflow?.type.color ?? .blue) : Color.gray.opacity(0.3))
                .frame(width: 12, height: 12)
                .scaleEffect(isActive ? 1.3 : 1.0)
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isActive)
            
            Text(step.primarySection.rawValue)
                .font(.caption2)
                .foregroundColor(isActive ? .primary : .secondary)
                .fontWeight(isActive ? .semibold : .regular)
        }
    }
    
    @ViewBuilder
    private var currentStepView: some View {
        if let step = workflowManager.currentStep {
            WorkflowStepView(
                step: step,
                workflowManager: workflowManager
            )
            .padding(.horizontal, 20)
        }
    }
    
    private var navigationButtons: some View {
        HStack(spacing: 16) {
            // Back button
            if let workflow = workflowManager.activeWorkflow,
               let currentIndex = workflow.steps.firstIndex(where: { $0.id == workflowManager.currentStep?.id }),
               currentIndex > 0 {
                Button(action: workflowManager.previousStep) {
                    HStack {
                        Image(systemName: "chevron.left")
                        Text("Back")
                    }
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(25)
                }
            }
            
            Spacer()
            
            // Next/Complete button
            Button(action: workflowManager.nextStep) {
                HStack {
                    Text(isLastStep ? "Complete" : "Continue")
                    if !isLastStep {
                        Image(systemName: "chevron.right")
                    }
                }
                .foregroundColor(.white)
                .padding(.horizontal, 30)
                .padding(.vertical, 12)
                .background(workflowManager.activeWorkflow?.type.color ?? .blue)
                .cornerRadius(25)
            }
            .disabled(workflowManager.isAnimating)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
    
    private var isLastStep: Bool {
        guard let workflow = workflowManager.activeWorkflow,
              let currentIndex = workflow.steps.firstIndex(where: { $0.id == workflowManager.currentStep?.id }) else {
            return false
        }
        return currentIndex == workflow.steps.count - 1
    }
}

// MARK: - Individual Step View

struct WorkflowStepView: View {
    let step: WorkflowStep
    @ObservedObject var workflowManager: CrossSectionWorkflowManager
    @State private var animateContent = false
    
    var body: some View {
        VStack(spacing: 24) {
            // Step header
            VStack(spacing: 12) {
                HStack {
                    Circle()
                        .fill(step.primarySection.color.opacity(0.2))
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: step.primarySection.icon)
                                .font(.title2)
                                .foregroundColor(step.primarySection.color)
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(step.title)
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text(step.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                
                // Section transition indicator
                SectionTransitionIndicator(
                    currentSection: step.primarySection,
                    workflowType: workflowManager.activeWorkflow?.type ?? .planWithPeople
                )
            }
            
            // Step content based on workflow type and step
            stepContent
                .opacity(animateContent ? 1 : 0)
                .offset(y: animateContent ? 0 : 20)
                .animation(.easeOut(duration: 0.6).delay(0.2), value: animateContent)
        }
        .frame(maxHeight: 300)
        .onAppear {
            animateContent = true
        }
        .onChange(of: step.id) {
            animateContent = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                animateContent = true
            }
        }
    }
    
    @ViewBuilder
    private var stepContent: some View {
        switch step.primarySection {
        case .people:
            PeopleStepContent(step: step, workflowManager: workflowManager)
        case .teams:
            TeamsStepContent(step: step, workflowManager: workflowManager)
        case .timeline:
            TimelineStepContent(step: step, workflowManager: workflowManager)
        case .workflow:
            WorkflowStepContent(step: step, workflowManager: workflowManager)
        case .general:
            GeneralStepContent(step: step, workflowManager: workflowManager)
        }
    }
}

// MARK: - Section Transition Indicator

struct SectionTransitionIndicator: View {
    let currentSection: AppSection
    let workflowType: WorkflowType
    @State private var animateTransition = false
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(AppSection.allCases, id: \.self) { section in
                HStack(spacing: 4) {
                    Circle()
                        .fill(section == currentSection ? section.color : Color.gray.opacity(0.3))
                        .frame(width: 8, height: 8)
                        .scaleEffect(section == currentSection ? 1.2 : 1.0)
                    
                    Text(section.rawValue)
                        .font(.caption)
                        .foregroundColor(section == currentSection ? section.color : .secondary)
                        .fontWeight(section == currentSection ? .semibold : .regular)
                }
                
                if section != AppSection.allCases.last {
                    Image(systemName: "arrow.right")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .opacity(animateTransition ? 1 : 0.3)
                }
            }
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: currentSection)
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                animateTransition = true
            }
        }
    }
}

// MARK: - Step Content Views

struct PeopleStepContent: View {
    let step: WorkflowStep
    @ObservedObject var workflowManager: CrossSectionWorkflowManager
    
    var body: some View {
        VStack(spacing: 16) {
            Text("👥 People Focus")
                .font(.headline)
                .foregroundColor(.blue)
            
            Text("Select people who can contribute to this workflow")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // Placeholder for people selection
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(0..<5, id: \.self) { index in
                        WorkflowPersonCard(index: index) {
                            // Add person logic
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
    }
}

struct TeamsStepContent: View {
    let step: WorkflowStep
    @ObservedObject var workflowManager: CrossSectionWorkflowManager
    
    var body: some View {
        VStack(spacing: 16) {
            Text("🤝 Teams Focus")
                .font(.headline)
                .foregroundColor(.green)
            
            Text("Form or join teams for collaborative success")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // Placeholder for team formation
            VStack(spacing: 12) {
                Button(action: {}) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Create New Team")
                    }
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.green)
                    .cornerRadius(12)
                }
                
                Button(action: {}) {
                    HStack {
                        Image(systemName: "person.3.fill")
                        Text("Join Existing Team")
                    }
                    .foregroundColor(.green)
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                }
            }
        }
    }
}

struct TimelineStepContent: View {
    let step: WorkflowStep
    @ObservedObject var workflowManager: CrossSectionWorkflowManager
    
    var body: some View {
        VStack(spacing: 16) {
            Text("📅 Timeline Focus")
                .font(.headline)
                .foregroundColor(.purple)
            
            Text("Connect this workflow to your timeline goals")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // Placeholder for timeline integration
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "target")
                        .foregroundColor(.purple)
                    Text("Link to Goal")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.purple.opacity(0.1))
                .cornerRadius(12)
                
                HStack {
                    Image(systemName: "calendar.badge.plus")
                        .foregroundColor(.purple)
                    Text("Set Milestone")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color.purple.opacity(0.1))
                .cornerRadius(12)
            }
        }
    }
}

// MARK: - Supporting Views

// Note: Using existing PersonSuggestionCard from OnboardingStepViews.swift

struct WorkflowPersonCard: View {
    let index: Int
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Circle()
                    .fill(Color.blue.opacity(0.2))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Text("P\(index + 1)")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    )

                Text("Person \(index + 1)")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Additional Step Content Views

struct WorkflowStepContent: View {
    let step: WorkflowStep
    let workflowManager: CrossSectionWorkflowManager

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Workflow Step")
                .font(.headline)
                .fontWeight(.semibold)

            Text(step.description)
                .font(.body)
                .foregroundColor(.secondary)

            // Generic workflow content
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .frame(height: 100)
                .overlay(
                    Text("Workflow content for \(step.title)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                )
        }
        .padding()
    }
}

struct GeneralStepContent: View {
    let step: WorkflowStep
    let workflowManager: CrossSectionWorkflowManager

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("General Step")
                .font(.headline)
                .fontWeight(.semibold)

            Text(step.description)
                .font(.body)
                .foregroundColor(.secondary)

            // Generic general content
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .frame(height: 100)
                .overlay(
                    Text("General content for \(step.title)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                )
        }
        .padding()
    }
}
