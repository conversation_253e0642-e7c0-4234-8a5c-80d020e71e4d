//
//  EnhancedWeekContentViews.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Enhanced Week Content Area
/// Enhanced week content that shows people interactions, team achievements, and social context

struct EnhancedWeekContentArea: View {
    let selectedWeekNumber: Int?
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var teamManager: TeamManager
    @Binding var showingEnhancedWeekDetail: Bool
    
    var body: some View {
        Group {
            if let weekNumber = selectedWeekNumber {
                EnhancedWeekContentView(
                    weekNumber: weekNumber,
                    timelineManager: timelineManager,
                    peopleManager: peopleManager,
                    teamManager: teamManager,
                    showingEnhancedWeekDetail: $showingEnhancedWeekDetail
                )
            } else {
                EnhancedTimelineEmptyStateView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(KeepsDesignSystem.Colors.background)
    }
}

// MARK: - Enhanced Week Content View
struct EnhancedWeekContentView: View {
    let weekNumber: Int
    @ObservedObject var timelineManager: EvolutionTimelineManager
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var teamManager: TeamManager
    @Binding var showingEnhancedWeekDetail: Bool
    
    @State private var peopleInteractions: [PeopleInteractionMarker] = []
    @State private var teamAchievements: [TeamAchievementMilestone] = []
    @State private var socialEvents: [SocialContextEvent] = []
    @State private var collaborativeEntries: [CollaborativeTimelineEntry] = []
    
    private var weekEntry: WeekEntry {
        timelineManager.getWeekEntry(for: weekNumber)
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.lg) {
                // Enhanced week header
                enhancedWeekHeaderView
                
                // People interactions section
                if !peopleInteractions.isEmpty {
                    peopleInteractionsSection
                }
                
                // Team achievements section
                if !teamAchievements.isEmpty {
                    teamAchievementsSection
                }
                
                // Social context section
                if !socialEvents.isEmpty {
                    socialContextSection
                }
                
                // Original week content
                originalWeekContentSection
                
                // Collaborative features
                if !collaborativeEntries.isEmpty {
                    collaborativeSection
                }
                
                // Action buttons
                actionButtonsSection
                
                Spacer(minLength: 100)
            }
            .padding(KeepsDesignSystem.Spacing.lg)
        }
        .onAppear {
            loadWeekData()
        }
    }
    
    // MARK: - Enhanced Week Header
    private var enhancedWeekHeaderView: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            HStack {
                VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.xs) {
                    Text("Week \(weekNumber)")
                        .font(KeepsDesignSystem.Typography.displayMedium)
                        .fontWeight(.bold)
                        .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                    
                    Text(weekEntry.startDate, format: .dateTime.weekday(.wide).month(.wide).day().year())
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: KeepsDesignSystem.Spacing.xs) {
                    if weekEntry.isCurrentWeek {
                        PolishedBadge(
                            text: "Current",
                            color: KeepsDesignSystem.Colors.primary,
                            size: .medium
                        )
                    }
                    
                    // Connection indicators
                    HStack(spacing: KeepsDesignSystem.Spacing.xs) {
                        if !peopleInteractions.isEmpty {
                            ConnectionIndicator(
                                count: peopleInteractions.count,
                                icon: "person.2.fill",
                                color: KeepsDesignSystem.Colors.primary
                            )
                        }
                        
                        if !teamAchievements.isEmpty {
                            ConnectionIndicator(
                                count: teamAchievements.count,
                                icon: "trophy.fill",
                                color: KeepsDesignSystem.Colors.success
                            )
                        }
                        
                        if !socialEvents.isEmpty {
                            ConnectionIndicator(
                                count: socialEvents.count,
                                icon: "heart.fill",
                                color: KeepsDesignSystem.Colors.accent
                            )
                        }
                    }
                }
            }
            
            // Week progress and emotional context
            HStack(spacing: KeepsDesignSystem.Spacing.lg) {
                StatItem(
                    title: "Progress",
                    value: "\(Int((Double(weekNumber) / Double(TimelineConfiguration.totalLifeWeeks)) * 100))%",
                    color: KeepsDesignSystem.Colors.info
                )
                
                StatItem(
                    title: "Connections",
                    value: "\(peopleInteractions.count + socialEvents.count)",
                    color: KeepsDesignSystem.Colors.primary
                )
                
                StatItem(
                    title: "Achievements",
                    value: "\(teamAchievements.count)",
                    color: KeepsDesignSystem.Colors.success
                )
                
                Spacer()
            }
        }
        .polishedCard()
    }
    
    // MARK: - People Interactions Section
    private var peopleInteractionsSection: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            PolishedSectionHeader(
                title: "People Interactions",
                subtitle: "\(peopleInteractions.count) interactions this week"
            )
            
            LazyVStack(spacing: KeepsDesignSystem.Spacing.sm) {
                ForEach(peopleInteractions, id: \.id) { interaction in
                    PeopleInteractionCard(interaction: interaction)
                }
            }
        }
    }
    
    // MARK: - Team Achievements Section
    private var teamAchievementsSection: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            PolishedSectionHeader(
                title: "Team Achievements",
                subtitle: "\(teamAchievements.count) achievements this week"
            )
            
            LazyVStack(spacing: KeepsDesignSystem.Spacing.sm) {
                ForEach(teamAchievements, id: \.id) { achievement in
                    TeamAchievementCard(achievement: achievement)
                }
            }
        }
    }
    
    // MARK: - Social Context Section
    private var socialContextSection: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            PolishedSectionHeader(
                title: "Social Context",
                subtitle: "\(socialEvents.count) social events this week"
            )
            
            LazyVStack(spacing: KeepsDesignSystem.Spacing.sm) {
                ForEach(socialEvents, id: \.id) { event in
                    SocialContextCard(event: event)
                }
            }
        }
    }
    
    // MARK: - Original Week Content Section
    private var originalWeekContentSection: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            PolishedSectionHeader(
                title: "Week Reflection",
                subtitle: "Your personal insights and accomplishments"
            )
            
            if weekEntry.hasContent {
                VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
                    if !weekEntry.title.isEmpty {
                        ContentCard(title: "Week Title", icon: "text.quote") {
                            Text(weekEntry.title)
                                .font(KeepsDesignSystem.Typography.body)
                                .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                        }
                    }
                    
                    if !weekEntry.insight.isEmpty {
                        ContentCard(title: "Key Insight", icon: "lightbulb") {
                            Text(weekEntry.insight)
                                .font(KeepsDesignSystem.Typography.body)
                                .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                        }
                    }
                    
                    if !weekEntry.accomplishments.isEmpty {
                        ContentCard(title: "Accomplishments", icon: "checkmark.seal") {
                            VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.sm) {
                                ForEach(Array(weekEntry.accomplishments.enumerated()), id: \.offset) { _, accomplishment in
                                    HStack(spacing: KeepsDesignSystem.Spacing.sm) {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(KeepsDesignSystem.Colors.success)
                                            .font(KeepsDesignSystem.Typography.caption)
                                        
                                        Text(accomplishment)
                                            .font(KeepsDesignSystem.Typography.body)
                                            .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                                        
                                        Spacer()
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                EmptyWeekPromptView(showingEnhancedWeekDetail: $showingEnhancedWeekDetail)
            }
        }
    }
    
    // MARK: - Collaborative Section
    private var collaborativeSection: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.md) {
            PolishedSectionHeader(
                title: "Collaborative Timeline",
                subtitle: "Shared with team members"
            )
            
            LazyVStack(spacing: KeepsDesignSystem.Spacing.sm) {
                ForEach(collaborativeEntries, id: \.id) { entry in
                    CollaborativeEntryCard(entry: entry)
                }
            }
        }
    }
    
    // MARK: - Action Buttons Section
    private var actionButtonsSection: some View {
        HStack(spacing: KeepsDesignSystem.Spacing.md) {
            PolishedButton(
                action: { showingEnhancedWeekDetail = true },
                style: .secondary,
                accessibilityLabel: "Edit Week"
            ) {
                HStack(spacing: KeepsDesignSystem.Spacing.xs) {
                    Image(systemName: "pencil")
                    Text("Edit Week")
                }
            }
            
            PolishedButton(
                action: { /* Add interaction */ },
                style: .tertiary,
                accessibilityLabel: "Add Interaction"
            ) {
                HStack(spacing: KeepsDesignSystem.Spacing.xs) {
                    Image(systemName: "person.badge.plus")
                    Text("Add Interaction")
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Data Loading
    private func loadWeekData() {
        // Generate sample data for the week
        peopleInteractions = generateSampleInteractions()
        teamAchievements = generateSampleAchievements()
        socialEvents = generateSampleSocialEvents()
        collaborativeEntries = generateSampleCollaborativeEntries()
    }
    
    private func generateSampleInteractions() -> [PeopleInteractionMarker] {
        guard weekNumber <= timelineManager.currentWeekNumber else { return [] }
        
        return [
            PeopleInteractionMarker(
                personId: UUID(),
                personName: "Sarah Chen",
                interactionType: .collaboration,
                date: weekEntry.startDate,
                weekNumber: weekNumber,
                description: "Worked on project proposal together",
                emotionalContext: .inspiring,
                duration: 7200,
                location: "Office",
                isSignificant: true
            )
        ]
    }
    
    private func generateSampleAchievements() -> [TeamAchievementMilestone] {
        guard weekNumber <= timelineManager.currentWeekNumber else { return [] }
        
        return [
            TeamAchievementMilestone(
                teamId: UUID(),
                teamName: "Development Team",
                achievementTitle: "Sprint Goal Completed",
                description: "Successfully completed all sprint objectives",
                date: weekEntry.endDate,
                weekNumber: weekNumber,
                achievementType: .goalAchievement,
                participants: [UUID(), UUID()],
                impact: .team,
                celebrationStyle: .sparkles,
                isSharedMilestone: true
            )
        ]
    }
    
    private func generateSampleSocialEvents() -> [SocialContextEvent] {
        guard weekNumber <= timelineManager.currentWeekNumber else { return [] }
        
        return [
            SocialContextEvent(
                title: "Team Lunch",
                description: "Informal team bonding session",
                date: Calendar.current.date(byAdding: .day, value: 3, to: weekEntry.startDate) ?? weekEntry.startDate,
                weekNumber: weekNumber,
                involvedPeople: [UUID(), UUID(), UUID()],
                relationshipContext: .colleagues,
                eventType: .bonding,
                emotionalImpact: .positive,
                isPrivate: false,
                tags: ["team", "social", "bonding"]
            )
        ]
    }
    
    private func generateSampleCollaborativeEntries() -> [CollaborativeTimelineEntry] {
        return []
    }
}

// MARK: - Connection Indicator
struct ConnectionIndicator: View {
    let count: Int
    let icon: String
    let color: Color
    
    var body: some View {
        HStack(spacing: KeepsDesignSystem.Spacing.xs) {
            Image(systemName: icon)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(color)
            
            Text("\(count)")
                .font(KeepsDesignSystem.Typography.caption2)
                .foregroundColor(color)
        }
        .padding(.horizontal, KeepsDesignSystem.Spacing.xs)
        .padding(.vertical, 2)
        .background(
            Capsule()
                .fill(color.opacity(0.1))
        )
    }
}

// MARK: - Enhanced Empty State
struct EnhancedTimelineEmptyStateView: View {
    var body: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.lg) {
            Image(systemName: "timeline.selection")
                .font(.system(size: 64))
                .foregroundColor(KeepsDesignSystem.Colors.primary.opacity(0.6))
            
            VStack(spacing: KeepsDesignSystem.Spacing.sm) {
                Text("Enhanced Timeline")
                    .font(KeepsDesignSystem.Typography.displaySmall)
                    .fontWeight(.semibold)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)
                
                Text("Select a week to see people interactions, team achievements, and social context")
                    .font(KeepsDesignSystem.Typography.body)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(KeepsDesignSystem.Spacing.xl)
    }
}

// MARK: - Empty Week Prompt
struct EmptyWeekPromptView: View {
    @Binding var showingEnhancedWeekDetail: Bool

    var body: some View {
        VStack(spacing: KeepsDesignSystem.Spacing.lg) {
            Image(systemName: "plus.circle")
                .font(.system(size: 48))
                .foregroundColor(KeepsDesignSystem.Colors.primary.opacity(0.6))

            VStack(spacing: KeepsDesignSystem.Spacing.sm) {
                Text("Capture This Week")
                    .font(KeepsDesignSystem.Typography.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)

                Text("Add your thoughts, insights, and accomplishments to make this week memorable.")
                    .font(KeepsDesignSystem.Typography.body)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                    .multilineTextAlignment(.center)
                    .lineSpacing(2)
            }

            PolishedButton(
                action: { showingEnhancedWeekDetail = true },
                style: .primary,
                accessibilityLabel: "Start Writing"
            ) {
                HStack(spacing: KeepsDesignSystem.Spacing.xs) {
                    Image(systemName: "pencil")
                    Text("Start Writing")
                }
            }
        }
        .polishedCard()
    }
}

// MARK: - People Interaction Card
struct PeopleInteractionCard: View {
    let interaction: PeopleInteractionMarker

    var body: some View {
        HStack(spacing: KeepsDesignSystem.Spacing.md) {
            // Interaction type indicator
            Circle()
                .fill(interaction.interactionType.color.opacity(0.1))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: interaction.interactionType.icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(interaction.interactionType.color)
                )

            // Interaction details
            VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.xs) {
                HStack {
                    Text(interaction.personName)
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(KeepsDesignSystem.Colors.primaryText)

                    Spacer()

                    Text(interaction.emotionalContext.emoji)
                        .font(KeepsDesignSystem.Typography.caption)
                }

                Text(interaction.description)
                    .font(KeepsDesignSystem.Typography.caption)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                    .lineLimit(2)

                HStack {
                    PolishedBadge(
                        text: interaction.interactionType.rawValue,
                        color: interaction.interactionType.color,
                        size: .small
                    )

                    if let duration = interaction.duration {
                        Text("\(Int(duration / 60))min")
                            .font(KeepsDesignSystem.Typography.caption2)
                            .foregroundColor(KeepsDesignSystem.Colors.tertiaryText)
                    }

                    if let location = interaction.location {
                        Text("• \(location)")
                            .font(KeepsDesignSystem.Typography.caption2)
                            .foregroundColor(KeepsDesignSystem.Colors.tertiaryText)
                    }

                    Spacer()
                }
            }
        }
        .polishedCard()
    }
}

// MARK: - Team Achievement Card
struct TeamAchievementCard: View {
    let achievement: TeamAchievementMilestone

    var body: some View {
        HStack(spacing: KeepsDesignSystem.Spacing.md) {
            // Achievement type indicator
            Diamond()
                .fill(achievement.achievementType.color)
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: achievement.achievementType.icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                )

            // Achievement details
            VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.xs) {
                HStack {
                    Text(achievement.achievementTitle)
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(KeepsDesignSystem.Colors.primaryText)

                    Spacer()

                    PolishedBadge(
                        text: achievement.impact.rawValue,
                        color: achievement.achievementType.color,
                        size: .small
                    )
                }

                Text(achievement.description)
                    .font(KeepsDesignSystem.Typography.caption)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                    .lineLimit(2)

                HStack {
                    Text(achievement.teamName)
                        .font(KeepsDesignSystem.Typography.caption2)
                        .foregroundColor(achievement.achievementType.color)

                    Text("• \(achievement.participants.count) participants")
                        .font(KeepsDesignSystem.Typography.caption2)
                        .foregroundColor(KeepsDesignSystem.Colors.tertiaryText)

                    Spacer()
                }
            }
        }
        .polishedCard()
    }
}

// MARK: - Social Context Card
struct SocialContextCard: View {
    let event: SocialContextEvent

    var body: some View {
        HStack(spacing: KeepsDesignSystem.Spacing.md) {
            // Event type indicator
            Circle()
                .fill(event.relationshipContext.color.opacity(0.1))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: event.relationshipContext.icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(event.relationshipContext.color)
                )

            // Event details
            VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.xs) {
                HStack {
                    Text(event.title)
                        .font(KeepsDesignSystem.Typography.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(KeepsDesignSystem.Colors.primaryText)

                    Spacer()

                    Circle()
                        .fill(event.emotionalImpact.color)
                        .frame(width: 8, height: 8)
                }

                Text(event.description)
                    .font(KeepsDesignSystem.Typography.caption)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)
                    .lineLimit(2)

                HStack {
                    PolishedBadge(
                        text: event.relationshipContext.rawValue,
                        color: event.relationshipContext.color,
                        size: .small
                    )

                    Text("\(event.involvedPeople.count) people")
                        .font(KeepsDesignSystem.Typography.caption2)
                        .foregroundColor(KeepsDesignSystem.Colors.tertiaryText)

                    Spacer()
                }
            }
        }
        .polishedCard()
    }
}

// MARK: - Collaborative Entry Card
struct CollaborativeEntryCard: View {
    let entry: CollaborativeTimelineEntry

    var body: some View {
        VStack(alignment: .leading, spacing: KeepsDesignSystem.Spacing.sm) {
            HStack {
                Text("Shared Timeline Entry")
                    .font(KeepsDesignSystem.Typography.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)

                Spacer()

                PolishedBadge(
                    text: entry.permissions.rawValue,
                    color: KeepsDesignSystem.Colors.info,
                    size: .small
                )
            }

            if !entry.collaborativeContent.sharedTitle.isEmpty {
                Text(entry.collaborativeContent.sharedTitle)
                    .font(KeepsDesignSystem.Typography.body)
                    .foregroundColor(KeepsDesignSystem.Colors.primaryText)
            }

            HStack {
                Text("\(entry.contributors.count) contributors")
                    .font(KeepsDesignSystem.Typography.caption2)
                    .foregroundColor(KeepsDesignSystem.Colors.secondaryText)

                Spacer()

                Text("Updated \(entry.lastUpdated.timeAgoDisplay)")
                    .font(KeepsDesignSystem.Typography.caption2)
                    .foregroundColor(KeepsDesignSystem.Colors.tertiaryText)
            }
        }
        .polishedCard()
    }
}
