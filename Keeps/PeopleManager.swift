//
//  PeopleManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import Foundation
import Combine
import CoreData

/// Sorting options for contacts
enum SortOption: String, CaseIterable {
    case name = "Name"
    case lastSeen = "Last Contact"
    case relationship = "Relationship"
    case frequency = "Frequency"
    case company = "Company"
    case favorites = "Favorites First"

    var icon: String {
        switch self {
        case .name: return "textformat.abc"
        case .lastSeen: return "clock"
        case .relationship: return "person.2"
        case .frequency: return "chart.bar"
        case .company: return "building.2"
        case .favorites: return "star"
        }
    }
}

/// Central manager for all people/contacts functionality and state management
/// Provides real-time updates, data persistence, and contact operations with performance optimizations
/// Enhanced with Core Data integration and relationship intelligence
class PeopleManager: ObservableObject {
    @Published var people: [Person] = []
    @Published var selectedPerson: Person?
    @Published var searchText: String = ""
    @Published var selectedRelationshipType: Person.RelationshipType? = nil
    @Published var showOnlineOnly: Bool = false
    @Published var showFavoritesOnly: Bool = false
    @Published var isLoading: Bool = false
    @Published var currentError: KeepsError?
    @Published var sortOption: SortOption = .name
    @Published var sortAscending: Bool = true

    // Core Data Integration
    private let context: NSManagedObjectContext
    private let personEntityManager: PersonEntityManager

    // Performance optimization properties
    @Published var loadingStates: [String: LoadingState] = [:]
    private let cacheManager = CacheManager.shared
    private let errorManager = ErrorManager.shared
    private var cancellables = Set<AnyCancellable>()
    private let debounceInterval: TimeInterval = 0.3
    private let userDefaults = UserDefaults.standard
    private let peopleKey = "SavedPeople"
    
    init(context: NSManagedObjectContext = PersistenceController.shared.container.viewContext) {
        self.context = context
        self.personEntityManager = PersonEntityManager(context: context)
        loadPeople()
        setupRealTimeUpdates()
        loadSampleDataIfNeeded()
        setupPerformanceOptimizations()
        migrateToCoreDateIfNeeded()
    }

    // MARK: - Performance Optimizations

    private func setupPerformanceOptimizations() {
        // Debounced search to prevent excessive filtering
        $searchText
            .debounce(for: .seconds(debounceInterval), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)

        // Cache filtered results to avoid recomputation
        Publishers.CombineLatest4($people, $searchText, $selectedRelationshipType, $showOnlineOnly)
            .debounce(for: .seconds(0.1), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.cacheFilteredResults()
            }
            .store(in: &cancellables)
    }

    private func cacheFilteredResults() {
        let cacheKey = "filtered_\(searchText)_\(selectedRelationshipType?.rawValue ?? "")_\(showOnlineOnly)_\(showFavoritesOnly)"

        // Check cache first
        if let cached: [Person] = cacheManager.retrieve([Person].self, forKey: cacheKey) {
            // Use cached results if available and recent
            return
        }

        // Compute and cache new results
        let filtered = computeFilteredPeople()
        cacheManager.store(filtered, forKey: cacheKey)
    }

    private func computeFilteredPeople() -> [Person] {
        var filtered = people

        // Apply search filter with performance optimization
        if !searchText.isEmpty {
            filtered = filtered.filter { person in
                let basicMatch = person.name.localizedCaseInsensitiveContains(searchText) ||
                               person.role.localizedCaseInsensitiveContains(searchText) ||
                               person.company.localizedCaseInsensitiveContains(searchText) ||
                               person.email.localizedCaseInsensitiveContains(searchText) ||
                               person.phone.localizedCaseInsensitiveContains(searchText) ||
                               person.location.localizedCaseInsensitiveContains(searchText) ||
                               person.notes.localizedCaseInsensitiveContains(searchText)

                let tagMatch = person.tags.contains { tag in
                    tag.localizedCaseInsensitiveContains(searchText)
                }

                let smartMatch = matchesSmartKeywords(person: person, searchText: searchText)

                return basicMatch || tagMatch || smartMatch
            }
        }

        // Apply relationship filter
        if let relationshipType = selectedRelationshipType {
            filtered = filtered.filter { $0.relationshipType == relationshipType }
        }

        // Apply online filter
        if showOnlineOnly {
            filtered = filtered.filter { $0.isOnline }
        }

        // Apply favorites filter
        if showFavoritesOnly {
            filtered = filtered.filter { $0.isFavorite }
        }

        // Apply smart sorting
        return applySorting(to: filtered)
    }
    
    // MARK: - Data Management

    /// Load people from persistent storage (Core Data first, then UserDefaults fallback)
    private func loadPeople() {
        // Try loading from Core Data first
        loadPeopleFromCoreData()

        // If no Core Data people, try UserDefaults (for migration)
        if people.isEmpty {
            if let data = userDefaults.data(forKey: peopleKey),
               let decodedPeople = try? JSONDecoder().decode([Person].self, from: data) {
                self.people = decodedPeople
            }
        }
    }

    /// Load people from Core Data
    private func loadPeopleFromCoreData() {
        let request: NSFetchRequest<PersonEntity> = PersonEntity.fetchRequest()

        do {
            let entities = try context.fetch(request)
            self.people = entities.map { personEntityManager.createPerson(from: $0) }
        } catch {
            print("Error loading people from Core Data: \(error)")
        }
    }
    
    /// Save people to persistent storage (Core Data)
    func savePeople() {
        savePeopleToCoreData()

        // Keep UserDefaults as backup for now
        if let encoded = try? JSONEncoder().encode(people) {
            userDefaults.set(encoded, forKey: peopleKey)
        }
    }

    /// Save people to Core Data
    private func savePeopleToCoreData() {
        // Clear existing entities
        let deleteRequest = NSBatchDeleteRequest(fetchRequest: PersonEntity.fetchRequest())
        try? context.execute(deleteRequest)

        // Create new entities
        for person in people {
            _ = personEntityManager.createPersonEntity(from: person)
        }

        // Save context
        do {
            try context.save()
        } catch {
            print("Error saving people to Core Data: \(error)")
        }
    }

    /// Migrate from UserDefaults to Core Data if needed
    private func migrateToCoreDateIfNeeded() {
        // Check if we have UserDefaults data but no Core Data
        let request: NSFetchRequest<PersonEntity> = PersonEntity.fetchRequest()
        let coreDataCount = (try? context.count(for: request)) ?? 0

        if coreDataCount == 0 && !people.isEmpty {
            print("Migrating people from UserDefaults to Core Data...")
            savePeopleToCoreData()
        }
    }
    
    /// Setup real-time updates and auto-save
    private func setupRealTimeUpdates() {
        // Auto-save when people array changes
        $people
            .debounce(for: .seconds(1), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                self?.savePeople()
            }
            .store(in: &cancellables)
        
        // Simulate real-time status updates
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.updateRandomStatuses()
            }
            .store(in: &cancellables)
    }
    
    /// Load sample data if no people exist
    private func loadSampleDataIfNeeded() {
        guard people.isEmpty else { return }
        
        let samplePeople = [
            Person(
                name: "Sarah Johnson",
                role: "Product Manager",
                company: "TechCorp",
                email: "<EMAIL>",
                phone: "+****************",
                avatarImageName: "person.circle.fill",
                isOnline: true,
                availability: .available,
                relationshipType: .colleague,
                interactionFrequency: .daily,
                location: "San Francisco, CA",
                isFavorite: true
            ),
            Person(
                name: "Mike Chen",
                role: "iOS Developer",
                company: "StartupXYZ",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: true,
                availability: .busy,
                relationshipType: .colleague,
                interactionFrequency: .weekly,
                location: "Austin, TX"
            ),
            Person(
                name: "Emma Wilson",
                role: "Designer",
                company: "Creative Studio",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: false,
                availability: .away,
                relationshipType: .friend,
                interactionFrequency: .monthly,
                location: "New York, NY",
                isFavorite: true
            ),
            Person(
                name: "David Rodriguez",
                role: "Marketing Director",
                company: "BigCorp Inc",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: true,
                availability: .available,
                relationshipType: .client,
                interactionFrequency: .weekly,
                location: "Los Angeles, CA"
            ),
            Person(
                name: "Lisa Park",
                role: "Data Scientist",
                company: "AI Solutions",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: false,
                availability: .offline,
                relationshipType: .colleague,
                interactionFrequency: .rarely,
                location: "Seattle, WA"
            ),
            Person(
                name: "Alex Thompson",
                role: "Freelance Writer",
                company: "Self-Employed",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: true,
                availability: .doNotDisturb,
                relationshipType: .friend,
                interactionFrequency: .monthly,
                location: "Portland, OR"
            ),
            Person(
                name: "Jennifer Lee",
                role: "HR Manager",
                company: "TechCorp",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: true,
                availability: .available,
                relationshipType: .colleague,
                interactionFrequency: .daily,
                location: "San Francisco, CA"
            ),
            Person(
                name: "Robert Kim",
                role: "Sales Manager",
                company: "SalesForce Pro",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: false,
                availability: .away,
                relationshipType: .vendor,
                interactionFrequency: .weekly,
                location: "Chicago, IL"
            )
        ]
        
        people = samplePeople
        savePeople()
    }
    
    /// Update random person statuses for demo purposes
    private func updateRandomStatuses() {
        guard !people.isEmpty else { return }
        
        let randomPerson = people.randomElement()!
        let randomStatus = Person.AvailabilityStatus.allCases.randomElement()!
        let randomOnlineStatus = Bool.random()
        
        if let index = people.firstIndex(where: { $0.id == randomPerson.id }) {
            people[index].availability = randomStatus
            people[index].isOnline = randomOnlineStatus
            people[index].lastSeen = Date()
        }
    }
    
    // MARK: - Computed Properties
    
    /// Enhanced filtered people with smart search, advanced filtering, and performance optimization
    var filteredPeople: [Person] {
        // Use cached results when possible for better performance
        let cacheKey = "filtered_\(searchText)_\(selectedRelationshipType?.rawValue ?? "")_\(showOnlineOnly)_\(showFavoritesOnly)_\(sortOption.rawValue)_\(sortAscending)"

        if let cached: [Person] = cacheManager.retrieve([Person].self, forKey: cacheKey) {
            return cached
        }

        // Fallback to computation if cache miss
        let result = computeFilteredPeople()
        cacheManager.store(result, forKey: cacheKey)
        return result
    }
    
    /// Get people analytics
    func getPeopleAnalytics() -> PeopleAnalytics {
        let totalPeople = people.count
        let onlinePeople = people.filter { $0.isOnline }.count
        let favoritesPeople = people.filter { $0.isFavorite }.count
        let relationshipBreakdown = Dictionary(grouping: people, by: { $0.relationshipType })
            .mapValues { $0.count }
        
        return PeopleAnalytics(
            totalPeople: totalPeople,
            onlinePeople: onlinePeople,
            favoritesPeople: favoritesPeople,
            relationshipBreakdown: relationshipBreakdown
        )
    }
    
    // MARK: - Actions
    
    /// Add a new person
    func addPerson(_ person: Person) {
        people.append(person)
        savePeople()
    }

    /// Remove a person
    func removePerson(_ person: Person) {
        people.removeAll { $0.id == person.id }
        savePeople()
    }

    /// Toggle favorite status
    func toggleFavorite(for person: Person) {
        if let index = people.firstIndex(where: { $0.id == person.id }) {
            people[index].isFavorite.toggle()
            savePeople()
        }
    }

    // MARK: - Relationship Intelligence

    /// Add interaction for a person
    func addInteraction(for person: Person, type: InteractionType, content: String, date: Date = Date()) {
        personEntityManager.addInteraction(for: person.id, type: type, content: content, date: date)

        // Update person's last interaction date
        if let index = people.firstIndex(where: { $0.id == person.id }) {
            people[index].lastSeen = date
            savePeople()
        }
    }

    /// Get relationship strength for a person
    func getRelationshipStrength(for person: Person) -> Double {
        return personEntityManager.calculateRelationshipStrength(for: person)
    }

    /// Get interaction history for a person
    func getInteractionHistory(for person: Person) -> [Interaction] {
        return personEntityManager.interactionHistory[person.id] ?? []
    }

    /// Add relationship goal for a person
    func addRelationshipGoal(for person: Person, title: String, description: String, targetDate: Date? = nil) {
        personEntityManager.addRelationshipGoal(for: person.id, title: title, description: description, targetDate: targetDate)
    }

    /// Get relationship goals for a person
    func getRelationshipGoals(for person: Person) -> [RelationshipGoal] {
        return personEntityManager.relationshipGoals[person.id] ?? []
    }

    /// Update person's relationship value tracking
    func updateRelationshipValue(for person: Person, value: String) {
        if let index = people.firstIndex(where: { $0.id == person.id }) {
            // Store relationship value in notes for now (can be enhanced later)
            let valueNote = "Relationship Value: \(value)"
            if !people[index].notes.contains("Relationship Value:") {
                people[index].notes += "\n\(valueNote)"
            } else {
                // Replace existing value
                let lines = people[index].notes.components(separatedBy: "\n")
                let updatedLines = lines.map { line in
                    line.hasPrefix("Relationship Value:") ? valueNote : line
                }
                people[index].notes = updatedLines.joined(separator: "\n")
            }
            savePeople()
        }
    }
    
    /// Clear all filters
    func clearFilters() {
        searchText = ""
        selectedRelationshipType = nil
        showOnlineOnly = false
        showFavoritesOnly = false
        sortOption = .name
        sortAscending = true
    }

    // MARK: - Smart Sorting

    /// Apply intelligent sorting to filtered results
    private func applySorting(to people: [Person]) -> [Person] {
        let sorted = people.sorted { person1, person2 in
            switch sortOption {
            case .name:
                return sortAscending ?
                    person1.name.localizedCaseInsensitiveCompare(person2.name) == .orderedAscending :
                    person1.name.localizedCaseInsensitiveCompare(person2.name) == .orderedDescending

            case .lastSeen:
                return sortAscending ?
                    person1.lastSeen < person2.lastSeen :
                    person1.lastSeen > person2.lastSeen

            case .relationship:
                let type1 = person1.relationshipType.rawValue
                let type2 = person2.relationshipType.rawValue
                return sortAscending ?
                    type1.localizedCaseInsensitiveCompare(type2) == .orderedAscending :
                    type1.localizedCaseInsensitiveCompare(type2) == .orderedDescending

            case .frequency:
                return sortAscending ?
                    person1.interactionFrequency.priority < person2.interactionFrequency.priority :
                    person1.interactionFrequency.priority > person2.interactionFrequency.priority

            case .company:
                return sortAscending ?
                    person1.company.localizedCaseInsensitiveCompare(person2.company) == .orderedAscending :
                    person1.company.localizedCaseInsensitiveCompare(person2.company) == .orderedDescending

            case .favorites:
                // Always show favorites first, then sort by name
                if person1.isFavorite != person2.isFavorite {
                    return person1.isFavorite
                }
                return person1.name.localizedCaseInsensitiveCompare(person2.name) == .orderedAscending
            }
        }

        return sorted
    }

    /// Toggle sort direction
    func toggleSortDirection() {
        sortAscending.toggle()
    }

    /// Set sort option
    func setSortOption(_ option: SortOption) {
        if sortOption == option {
            toggleSortDirection()
        } else {
            sortOption = option
            sortAscending = true
        }
    }

    // MARK: - Smart Search & Suggestions

    /// Smart keyword matching for enhanced search
    private func matchesSmartKeywords(person: Person, searchText: String) -> Bool {
        let lowercaseSearch = searchText.lowercased()

        // Relationship type keywords
        let relationshipKeywords: [String: [String]] = [
            "family": ["family", "relative", "mom", "dad", "brother", "sister", "parent"],
            "friend": ["friend", "buddy", "pal", "mate"],
            "colleague": ["colleague", "coworker", "work", "office", "team"],
            "client": ["client", "customer", "business"],
            "mentor": ["mentor", "teacher", "coach", "advisor"],
            "other": ["contact", "person", "acquaintance"]
        ]

        for (type, keywords) in relationshipKeywords {
            if keywords.contains(where: { lowercaseSearch.contains($0) }) &&
               person.relationshipType.rawValue.lowercased().contains(type) {
                return true
            }
        }

        // Interaction frequency keywords
        let frequencyKeywords: [String: [String]] = [
            "daily": ["daily", "everyday", "frequent"],
            "weekly": ["weekly", "regular", "often"],
            "monthly": ["monthly", "occasional"],
            "rarely": ["rarely", "seldom", "infrequent"],
            "never": ["never", "inactive", "dormant"]
        ]

        for (frequency, keywords) in frequencyKeywords {
            if keywords.contains(where: { lowercaseSearch.contains($0) }) &&
               person.interactionFrequency.rawValue.lowercased().contains(frequency) {
                return true
            }
        }

        // Status keywords
        if (lowercaseSearch.contains("online") || lowercaseSearch.contains("active")) && person.isOnline {
            return true
        }

        if (lowercaseSearch.contains("offline") || lowercaseSearch.contains("inactive")) && !person.isOnline {
            return true
        }

        if (lowercaseSearch.contains("favorite") || lowercaseSearch.contains("starred")) && person.isFavorite {
            return true
        }

        return false
    }

    /// Get smart search suggestions based on current input
    var searchSuggestions: [String] {
        guard !searchText.isEmpty else {
            return recentSearches + popularSearches
        }

        var suggestions: [String] = []
        let lowercaseSearch = searchText.lowercased()

        // Name suggestions
        let nameMatches = people.compactMap { person in
            person.name.lowercased().hasPrefix(lowercaseSearch) ? person.name : nil
        }.prefix(3)
        suggestions.append(contentsOf: nameMatches)

        // Company suggestions
        let companyMatches = Set(people.compactMap { person in
            person.company.lowercased().hasPrefix(lowercaseSearch) && !person.company.isEmpty ? person.company : nil
        }).prefix(2)
        suggestions.append(contentsOf: companyMatches)

        // Smart keyword suggestions
        let smartSuggestions = getSmartSuggestions(for: lowercaseSearch)
        suggestions.append(contentsOf: smartSuggestions)

        return Array(Set(suggestions)).prefix(8).map { $0 }
    }

    /// Get smart suggestions based on search patterns
    private func getSmartSuggestions(for searchText: String) -> [String] {
        var suggestions: [String] = []

        // Relationship suggestions
        if searchText.hasPrefix("f") {
            suggestions.append(contentsOf: ["family", "friends"])
        }
        if searchText.hasPrefix("c") {
            suggestions.append(contentsOf: ["colleagues", "clients"])
        }
        if searchText.hasPrefix("w") {
            suggestions.append("work contacts")
        }

        // Status suggestions
        if searchText.hasPrefix("o") {
            suggestions.append(contentsOf: ["online", "offline"])
        }
        if searchText.hasPrefix("a") {
            suggestions.append("active contacts")
        }

        // Frequency suggestions
        if searchText.contains("freq") || searchText.contains("contact") {
            suggestions.append(contentsOf: ["frequent contacts", "recent contacts"])
        }

        return suggestions
    }

    /// Recent search history (would be persisted in real app)
    private var recentSearches: [String] {
        ["colleagues", "family", "online", "favorites"]
    }

    /// Popular search terms
    private var popularSearches: [String] {
        ["work contacts", "friends", "recent contacts", "favorites"]
    }

    /// Add search to history
    func addToSearchHistory(_ search: String) {
        // In a real app, this would persist the search history
        // For now, we'll just track it in memory
    }

    /// Force reload sample data (for debugging)
    func forceLoadSampleData() {
        let samplePeople = [
            Person(
                name: "Sarah Johnson",
                role: "Product Manager",
                company: "TechCorp",
                email: "<EMAIL>",
                phone: "+****************",
                avatarImageName: "person.circle.fill",
                isOnline: true,
                availability: .available,
                relationshipType: .colleague,
                interactionFrequency: .daily,
                location: "San Francisco, CA",
                isFavorite: true
            ),
            Person(
                name: "Mike Chen",
                role: "iOS Developer",
                company: "StartupXYZ",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: true,
                availability: .busy,
                relationshipType: .colleague,
                interactionFrequency: .weekly,
                location: "Austin, TX"
            ),
            Person(
                name: "Emma Wilson",
                role: "Designer",
                company: "Creative Studio",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: false,
                availability: .away,
                relationshipType: .friend,
                interactionFrequency: .monthly,
                location: "New York, NY",
                isFavorite: true
            ),
            Person(
                name: "David Rodriguez",
                role: "Marketing Director",
                company: "BigCorp Inc",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: true,
                availability: .available,
                relationshipType: .client,
                interactionFrequency: .weekly,
                location: "Los Angeles, CA"
            ),
            Person(
                name: "Lisa Park",
                role: "Data Scientist",
                company: "AI Solutions",
                email: "<EMAIL>",
                avatarImageName: "person.circle.fill",
                isOnline: false,
                availability: .offline,
                relationshipType: .colleague,
                interactionFrequency: .rarely,
                location: "Seattle, WA"
            )
        ]

        people = samplePeople
        savePeople()
    }
}

/// Analytics data structure for people overview
struct PeopleAnalytics {
    let totalPeople: Int
    let onlinePeople: Int
    let favoritesPeople: Int
    let relationshipBreakdown: [Person.RelationshipType: Int]
}
