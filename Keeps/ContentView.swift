//
//  ContentView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import CoreData

/// Main content view with tab navigation between Teams, People, and Timeline
/// Features smooth tab transitions, consistent design language, and cross-section navigation
struct ContentView: View {
    @StateObject private var smartSuggestionsEngine = SmartSuggestionsEngine()
    @StateObject private var automationManager = AutomationManager()
    @StateObject private var analyticsProcessor = AnalyticsProcessor()
    @StateObject private var navigationCoordinator = NavigationCoordinator()

    @State private var showingSmartSuggestions = false
    @State private var showingInsights = false

    var body: some View {
        ZStack {
            // Main tab view
            TabView(selection: $navigationCoordinator.selectedTab) {
                // Teams Tab
                TeamsListView()
                    .tabItem {
                        Image(systemName: navigationCoordinator.selectedTab == 0 ? "person.3.fill" : "person.3")
                        Text("Teams")
                    }
                    .tag(0)

                // People Tab
                PeopleListView(navigationCoordinator: navigationCoordinator)
                    .tabItem {
                        Image(systemName: navigationCoordinator.selectedTab == 1 ? "person.crop.circle.fill" : "person.crop.circle")
                        Text("People")
                    }
                    .tag(1)

                // Evolution Timeline Tab
                EvolutionTimelineView()
                    .tabItem {
                        Image(systemName: navigationCoordinator.selectedTab == 2 ? "timeline.selection" : "timeline.selection")
                        Text("Timeline")
                    }
                    .tag(2)

                // Insights Tab
                InsightsTabView()
                    .tabItem {
                        Image(systemName: navigationCoordinator.selectedTab == 3 ? "chart.bar.fill" : "chart.bar")
                        Text("Insights")
                    }
                    .tag(3)
            }
            .accentColor(.blue)

            // Smart Suggestions Floating Button
            VStack {
                Spacer()
                HStack {
                    Spacer()
                    Button(action: {
                        showingSmartSuggestions = true
                    }) {
                        Image(systemName: "brain.head.profile")
                            .font(.title2)
                            .foregroundColor(.white)
                            .frame(width: 56, height: 56)
                            .background(Color.blue)
                            .clipShape(Circle())
                            .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                    .padding(EdgeInsets(top: 0, leading: 0, bottom: 100, trailing: 20))
                }
            }
        }
        .sheet(isPresented: $showingSmartSuggestions) {
            NavigationView {
                SmartSuggestionsPanel(suggestionsEngine: smartSuggestionsEngine)
                    .navigationTitle("Smart Features")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Button("Done") {
                                showingSmartSuggestions = false
                            }
                        }
                    }
            }
        }
        .onAppear {
            setupSmartFeatures()
        }
    }

    private func setupSmartFeatures() {
        // Initialize smart suggestions and automation
        // Note: These methods will be implemented in the respective classes
        print("Smart features initialized")
    }
}
// MARK: - Insights Tab View

/// Insights tab containing Analytics Dashboard and Performance Dashboard
struct InsightsTabView: View {
    @State private var selectedInsightTab = 0

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab selector
                Picker("Insights", selection: $selectedInsightTab) {
                    Text("Analytics").tag(0)
                    Text("Performance").tag(1)
                    Text("Workflows").tag(2)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()

                // Content based on selected tab
                TabView(selection: $selectedInsightTab) {
                    InsightsDashboard()
                        .tag(0)

                    PerformanceDashboard()
                        .tag(1)

                    WorkflowLauncherView(workflowManager: CrossSectionWorkflowManager())
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Insights")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}





// MARK: - Views are imported from their respective files

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
