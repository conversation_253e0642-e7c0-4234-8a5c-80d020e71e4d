//
//  ContextualNavigationComponents.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Contextual Navigation Bar

/// Navigation bar that shows context when navigating between sections
struct ContextualNavigationBar: View {
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    
    var body: some View {
        if let context = navigationCoordinator.currentContext {
            VStack(spacing: 0) {
                HStack {
                    // Source section indicator
                    HStack(spacing: 8) {
                        Image(systemName: context.sourceSection.icon)
                            .foregroundColor(context.sourceSection.color)
                        Text(context.sourceSection.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Arrow
                    Image(systemName: "arrow.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    // Target section with context
                    HStack(spacing: 8) {
                        Image(systemName: context.targetSection.icon)
                            .foregroundColor(context.targetSection.color)
                        Text(context.displayText)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    
                    Space<PERSON>()
                    
                    // Clear context button
                    Button(action: {
                        withAnimation(.spring(response: 0.3)) {
                            navigationCoordinator.clearNavigationContext()
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(.ultraThinMaterial)
                
                Divider()
            }
            .transition(.move(edge: .top).combined(with: .opacity))
        }
    }
}

// MARK: - Breadcrumb Navigation

/// Breadcrumb navigation for complex navigation flows
struct BreadcrumbNavigation: View {
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    
    var body: some View {
        if navigationCoordinator.showingBreadcrumbs && !navigationCoordinator.breadcrumbs.isEmpty {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(Array(navigationCoordinator.breadcrumbs.enumerated()), id: \.element.id) { index, breadcrumb in
                        HStack(spacing: 4) {
                            if index > 0 {
                                Image(systemName: "chevron.right")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                            
                            Button(action: {
                                navigationCoordinator.navigateToBreadcrumb(breadcrumb)
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: breadcrumb.destination.icon)
                                        .font(.caption2)
                                    Text(breadcrumb.title)
                                        .font(.caption)
                                }
                                .foregroundColor(index == navigationCoordinator.breadcrumbs.count - 1 ? .primary : .secondary)
                            }
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
            .padding(.vertical, 8)
            .background(.ultraThinMaterial)
            .transition(.move(edge: .top).combined(with: .opacity))
        }
    }
}

// MARK: - Cross-Section Action Button

/// Button for performing cross-section actions
struct CrossSectionActionButton: View {
    let action: NavigationAction
    let navigationCoordinator: NavigationCoordinator
    let compact: Bool
    
    init(action: NavigationAction, navigationCoordinator: NavigationCoordinator, compact: Bool = false) {
        self.action = action
        self.navigationCoordinator = navigationCoordinator
        self.compact = compact
    }
    
    var body: some View {
        Button(action: {
            performAction()
        }) {
            if compact {
                compactButtonContent
            } else {
                fullButtonContent
            }
        }
        .buttonStyle(CrossSectionButtonStyle(color: action.color))
    }
    
    private var compactButtonContent: some View {
        HStack(spacing: 6) {
            Image(systemName: action.icon)
                .font(.caption)
            Text(action.displayName)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
    }
    
    private var fullButtonContent: some View {
        HStack(spacing: 8) {
            Image(systemName: action.icon)
                .font(.subheadline)
            Text(action.displayName)
                .font(.subheadline)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
    }
    
    private func performAction() {
        switch action {
        case .viewInTimeline(let id):
            // Navigate to timeline with person/team context
            if let weekNumber = getCurrentWeekForEntity(id) {
                navigationCoordinator.navigateToWeek(weekNumber, context: createTimelineContext(for: id))
            }
            
        case .viewTeamMembers(let teamId):
            navigationCoordinator.navigateToRelated(.teamMembers(teamId))
            
        case .viewRelatedPeople(let teamId):
            navigationCoordinator.navigateToRelated(.teamMembers(teamId))
            
        case .viewPersonTeams(let personId):
            navigationCoordinator.navigateToRelated(.personTeams(personId))
            
        case .viewWeekPeople(let weekNumber):
            navigationCoordinator.navigateToRelated(.timelinePeople(weekNumber))
            
        case .viewWeekTeams(let weekNumber):
            navigationCoordinator.navigateToRelated(.timelineTeams(weekNumber))
            
        case .createConnection(let personId, let teamId):
            // Handle connection creation
            createConnection(personId: personId, teamId: teamId)
            
        case .addToTimeline(let entityId, let weekNumber):
            // Handle adding entity to timeline
            addToTimeline(entityId: entityId, weekNumber: weekNumber)
        }
    }
    
    // Helper methods (would be implemented with actual data managers)
    private func getCurrentWeekForEntity(_ entityId: UUID) -> Int? {
        // This would query the timeline manager for the most recent week with this entity
        return nil // Placeholder
    }
    
    private func createTimelineContext(for entityId: UUID) -> NavigationContext? {
        // This would create appropriate context based on entity type
        return nil // Placeholder
    }
    
    private func createConnection(personId: UUID, teamId: UUID) {
        // This would use the appropriate managers to create the connection
    }
    
    private func addToTimeline(entityId: UUID, weekNumber: Int) {
        // This would add the entity to the specified timeline week
    }
}

// MARK: - Related Content Card

/// Card showing related content from other sections
struct RelatedContentCard: View {
    let relatedContent: RelatedContent
    let navigationCoordinator: NavigationCoordinator
    let count: Int
    
    var body: some View {
        Button(action: {
            navigationCoordinator.navigateToRelated(relatedContent)
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Image(systemName: relatedContent.icon)
                            .foregroundColor(relatedContent.targetSection.color)
                            .font(.title3)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(relatedContent.displayName)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)
                            
                            Text("\(count) items")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            .padding(16)
            .background(.ultraThinMaterial)
            .clipShape(RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Quick Navigation Menu

/// Quick navigation menu for accessing related content
struct QuickNavigationMenu: View {
    let sourceSection: AppSection
    let entityId: UUID
    let navigationCoordinator: NavigationCoordinator
    @State private var showingMenu = false
    
    var body: some View {
        Menu {
            ForEach(availableActions, id: \.id) { action in
                Button(action: {
                    performQuickAction(action)
                }) {
                    Label(action.displayName, systemImage: action.icon)
                }
            }
        } label: {
            Image(systemName: "ellipsis.circle")
                .foregroundColor(.secondary)
                .font(.title3)
        }
    }
    
    private var availableActions: [NavigationAction] {
        switch sourceSection {
        case .people:
            return [
                .viewPersonTeams(entityId),
                .viewInTimeline(entityId)
            ]
        case .teams:
            return [
                .viewTeamMembers(entityId),
                .viewInTimeline(entityId)
            ]
        case .timeline:
            // For timeline, entityId would be converted to week number
            return []
        case .workflow:
            return [
                .viewInTimeline(entityId)
            ]
        case .general:
            return []
        }
    }
    
    private func performQuickAction(_ action: NavigationAction) {
        // Use the same logic as CrossSectionActionButton
        switch action {
        case .viewPersonTeams(let personId):
            navigationCoordinator.navigateToRelated(.personTeams(personId))
        case .viewInTimeline(let id):
            if let weekNumber = getCurrentWeekForEntity(id) {
                navigationCoordinator.navigateToWeek(weekNumber)
            }
        case .viewTeamMembers(let teamId):
            navigationCoordinator.navigateToRelated(.teamMembers(teamId))
        default:
            break
        }
    }
    
    private func getCurrentWeekForEntity(_ entityId: UUID) -> Int? {
        // Placeholder implementation
        return nil
    }
}

// MARK: - Cross-Section Button Style

struct CrossSectionButtonStyle: ButtonStyle {
    let color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(color.opacity(configuration.isPressed ? 0.3 : 0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(color.opacity(0.3), lineWidth: 1)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Navigation Context Banner

/// Banner showing current navigation context
struct NavigationContextBanner: View {
    let context: NavigationContext
    let onDismiss: () -> Void
    
    var body: some View {
        HStack {
            HStack(spacing: 8) {
                Image(systemName: "link")
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Viewing \(context.targetSection.displayName)")
                        .font(.caption)
                        .fontWeight(.medium)
                    
                    if let subtitle = context.subtitle {
                        Text(subtitle)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(.blue.opacity(0.1))
        .overlay(
            Rectangle()
                .fill(.blue.opacity(0.3))
                .frame(height: 1),
            alignment: .bottom
        )
    }
}
