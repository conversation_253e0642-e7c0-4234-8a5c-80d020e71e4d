//
//  NotificationsView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI

/// View displaying team notifications with interactive functionality
struct NotificationsView: View {
    @ObservedObject var teamManager: TeamManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            Group {
                if teamManager.notifications.isEmpty {
                    EmptyNotificationsView()
                } else {
                    List {
                        ForEach(teamManager.notifications) { notification in
                            NotificationRowView(
                                notification: notification,
                                teamManager: teamManager
                            )
                        }
                        .onDelete(perform: deleteNotifications)
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    if !teamManager.notifications.isEmpty {
                        <PERSON><PERSON>("Clear All") {
                            teamManager.clearAllNotifications()
                        }
                        .foregroundColor(.red)
                    }
                }
            }
        }
    }
    
    private func deleteNotifications(at offsets: IndexSet) {
        teamManager.notifications.remove(atOffsets: offsets)
    }
}

/// Individual notification row
struct NotificationRowView: View {
    let notification: TeamNotification
    let teamManager: TeamManager
    
    var body: some View {
        HStack(spacing: 12) {
            // Notification icon
            Image(systemName: notification.type.icon)
                .font(.title3)
                .foregroundColor(notification.type.color)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(notification.message)
                    .font(.subheadline)
                    .fontWeight(notification.isRead ? .regular : .semibold)
                    .foregroundColor(.primary)
                
                Text(formatTimestamp(notification.timestamp))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if !notification.isRead {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 8, height: 8)
            }
        }
        .padding(.vertical, 8)
        .background(notification.isRead ? Color.clear : Color.blue.opacity(0.05))
        .onTapGesture {
            if !notification.isRead {
                teamManager.markNotificationAsRead(notification)
            }
        }
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

/// Empty state view for notifications
struct EmptyNotificationsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "bell.slash")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("No Notifications")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("You're all caught up! Team notifications will appear here.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
}

#Preview {
    NotificationsView(teamManager: TeamManager())
}
