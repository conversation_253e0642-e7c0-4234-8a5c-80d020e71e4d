//
//  EnhancedNotesInterface.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI

// MARK: - Note Entry Model

/// Individual Note Entry Model
struct NoteEntry: Identifiable, Codable {
    let id = UUID()
    var title: String
    var content: String
    var category: NoteCategory
    var createdDate: Date
    var lastModified: Date

    init(title: String = "", content: String = "", category: NoteCategory = .general) {
        self.title = title
        self.content = content
        self.category = category
        self.createdDate = Date()
        self.lastModified = Date()
    }

    mutating func updateContent(_ newContent: String) {
        self.content = newContent
        self.lastModified = Date()
    }

    mutating func updateTitle(_ newTitle: String) {
        self.title = newTitle
        self.lastModified = Date()
    }

    var preview: String {
        let cleanContent = content.replacingOccurrences(of: "**", with: "")
            .replacingOccurrences(of: "*", with: "")
            .replacingOccurrences(of: "#", with: "")
            .replacingO<PERSON><PERSON>ren<PERSON>(of: "•", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        return String(cleanContent.prefix(100))
    }

    var isEmpty: Bool {
        title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
}

// MARK: - Template Categories

enum TemplateCategory: String, CaseIterable {
    case business = "Business"
    case personal = "Personal"
    case meetings = "Meetings"
    case projects = "Projects"
    case followUp = "Follow-up"

    var icon: String {
        switch self {
        case .business: return "briefcase.fill"
        case .personal: return "heart.fill"
        case .meetings: return "calendar"
        case .projects: return "folder.fill"
        case .followUp: return "arrow.clockwise"
        }
    }

    var color: Color {
        switch self {
        case .business: return .blue
        case .personal: return .pink
        case .meetings: return .orange
        case .projects: return .purple
        case .followUp: return .green
        }
    }

    var templates: [NoteTemplate] {
        switch self {
        case .business:
            return [
                NoteTemplate(
                    title: "Client Meeting",
                    description: "Professional client meeting notes",
                    content: "📋 **Client Meeting Notes**\n\n**Client:** [Client Name]\n**Date:** [Date]\n**Attendees:** [Names]\n\n**Meeting Objectives:**\n• [Objective 1]\n• [Objective 2]\n\n**Key Discussion Points:**\n• [Point 1]\n• [Point 2]\n\n**Decisions Made:**\n• [Decision 1]\n• [Decision 2]\n\n**Action Items:**\n• [ ] [Action 1] - Due: [Date]\n• [ ] [Action 2] - Due: [Date]\n\n**Next Steps:**\n• [Next step 1]\n• [Next step 2]\n\n**Follow-up Required:** [Date]"
                ),
                NoteTemplate(
                    title: "Business Contact",
                    description: "Professional contact information",
                    content: "💼 **Business Contact**\n\n**Company:** [Company Name]\n**Position:** [Job Title]\n**Department:** [Department]\n\n**Contact Preferences:**\n• Best time to reach: [Time]\n• Preferred method: [Email/Phone/Slack]\n• Response time: [Timeframe]\n\n**Professional Interests:**\n• [Interest 1]\n• [Interest 2]\n\n**Current Projects:**\n• [Project 1]\n• [Project 2]\n\n**Notes:**\n[Additional notes about this contact]"
                ),
                NoteTemplate(
                    title: "Networking Event",
                    description: "Notes from networking events",
                    content: "🤝 **Networking Event**\n\n**Event:** [Event Name]\n**Date:** [Date]\n**Location:** [Venue]\n\n**How We Met:**\n[Context of meeting]\n\n**Their Background:**\n• Company: [Company]\n• Role: [Position]\n• Experience: [Years/Background]\n\n**Common Interests:**\n• [Interest 1]\n• [Interest 2]\n\n**Potential Collaboration:**\n• [Opportunity 1]\n• [Opportunity 2]\n\n**Follow-up Plan:**\n• [ ] Connect on LinkedIn\n• [ ] Send follow-up email\n• [ ] Schedule coffee meeting\n\n**Next Contact:** [Date]"
                )
            ]
        case .personal:
            return [
                NoteTemplate(
                    title: "Personal Friend",
                    description: "Notes about personal relationships",
                    content: "❤️ **Personal Notes**\n\n**How We Met:**\n[Story of how you met]\n\n**Personal Details:**\n• Birthday: [Date]\n• Family: [Family info]\n• Pets: [Pet names/types]\n• Hobbies: [Interests]\n\n**Favorite Things:**\n• Food: [Preferences]\n• Movies/Shows: [Favorites]\n• Music: [Genres/Artists]\n• Activities: [What they enjoy]\n\n**Important Dates:**\n• Anniversary: [Date]\n• Special events: [Events]\n\n**Recent Life Updates:**\n• [Update 1]\n• [Update 2]\n\n**Things to Remember:**\n• [Important detail 1]\n• [Important detail 2]\n\n**Gift Ideas:**\n• [Idea 1]\n• [Idea 2]"
                ),
                NoteTemplate(
                    title: "Family Member",
                    description: "Family relationship notes",
                    content: "👨‍👩‍👧‍👦 **Family Notes**\n\n**Relationship:** [How you're related]\n\n**Personal Info:**\n• Birthday: [Date]\n• Anniversary: [Date if applicable]\n• Address: [Current address]\n\n**Family Details:**\n• Spouse: [Name]\n• Children: [Names and ages]\n• Pets: [Pet details]\n\n**Health & Wellness:**\n• Health updates: [Any important health info]\n• Dietary restrictions: [Allergies/preferences]\n\n**Interests & Hobbies:**\n• [Hobby 1]\n• [Hobby 2]\n\n**Recent Updates:**\n• [Life update 1]\n• [Life update 2]\n\n**Upcoming Events:**\n• [Event 1]\n• [Event 2]\n\n**Gift Ideas:**\n• [Idea 1]\n• [Idea 2]"
                )
            ]
        case .meetings:
            return [
                NoteTemplate(
                    title: "Team Meeting",
                    description: "Regular team meeting notes",
                    content: "👥 **Team Meeting**\n\n**Date:** [Date]\n**Time:** [Time]\n**Attendees:** [Names]\n**Meeting Type:** [Weekly/Monthly/Ad-hoc]\n\n**Agenda Items:**\n1. [Item 1]\n2. [Item 2]\n3. [Item 3]\n\n**Discussion Summary:**\n• [Key point 1]\n• [Key point 2]\n• [Key point 3]\n\n**Decisions Made:**\n• [Decision 1]\n• [Decision 2]\n\n**Action Items:**\n• [ ] [Task 1] - Assigned to: [Name] - Due: [Date]\n• [ ] [Task 2] - Assigned to: [Name] - Due: [Date]\n\n**Blockers/Issues:**\n• [Issue 1]\n• [Issue 2]\n\n**Next Meeting:** [Date and time]"
                ),
                NoteTemplate(
                    title: "One-on-One",
                    description: "Individual meeting notes",
                    content: "👤 **One-on-One Meeting**\n\n**With:** [Person's name]\n**Date:** [Date]\n**Duration:** [Time]\n\n**Topics Discussed:**\n• [Topic 1]\n• [Topic 2]\n• [Topic 3]\n\n**Their Updates:**\n• [Update 1]\n• [Update 2]\n\n**Feedback Given:**\n• [Feedback 1]\n• [Feedback 2]\n\n**Feedback Received:**\n• [Feedback 1]\n• [Feedback 2]\n\n**Goals & Objectives:**\n• [Goal 1]\n• [Goal 2]\n\n**Support Needed:**\n• [Support 1]\n• [Support 2]\n\n**Action Items:**\n• [ ] [Action 1]\n• [ ] [Action 2]\n\n**Next Check-in:** [Date]"
                )
            ]
        case .projects:
            return [
                NoteTemplate(
                    title: "Project Kickoff",
                    description: "New project initiation notes",
                    content: "🚀 **Project Kickoff**\n\n**Project Name:** [Project Name]\n**Start Date:** [Date]\n**Expected Completion:** [Date]\n**Project Manager:** [Name]\n\n**Project Scope:**\n• [Scope item 1]\n• [Scope item 2]\n• [Scope item 3]\n\n**Key Stakeholders:**\n• [Stakeholder 1] - [Role]\n• [Stakeholder 2] - [Role]\n\n**Success Criteria:**\n• [Criteria 1]\n• [Criteria 2]\n\n**Major Milestones:**\n• [Milestone 1] - [Date]\n• [Milestone 2] - [Date]\n\n**Risks & Mitigation:**\n• Risk: [Risk 1] | Mitigation: [Plan]\n• Risk: [Risk 2] | Mitigation: [Plan]\n\n**Resources Required:**\n• [Resource 1]\n• [Resource 2]\n\n**Next Steps:**\n• [ ] [Step 1]\n• [ ] [Step 2]"
                ),
                NoteTemplate(
                    title: "Project Update",
                    description: "Regular project status update",
                    content: "📊 **Project Status Update**\n\n**Project:** [Project Name]\n**Update Date:** [Date]\n**Reporting Period:** [Period]\n\n**Overall Status:** 🟢 On Track / 🟡 At Risk / 🔴 Behind\n\n**Progress Summary:**\n• Completed: [What was completed]\n• In Progress: [Current work]\n• Upcoming: [Next tasks]\n\n**Key Achievements:**\n• [Achievement 1]\n• [Achievement 2]\n\n**Challenges & Blockers:**\n• [Challenge 1] - [Impact/Resolution]\n• [Challenge 2] - [Impact/Resolution]\n\n**Metrics:**\n• Budget: [Used/Remaining]\n• Timeline: [On schedule/Delayed by X days]\n• Quality: [Quality metrics]\n\n**Decisions Needed:**\n• [Decision 1]\n• [Decision 2]\n\n**Next Period Focus:**\n• [Focus area 1]\n• [Focus area 2]\n\n**Next Update:** [Date]"
                )
            ]
        case .followUp:
            return [
                NoteTemplate(
                    title: "Follow-up Call",
                    description: "Post-meeting follow-up notes",
                    content: "📞 **Follow-up Notes**\n\n**Original Meeting:** [Meeting/Event]\n**Follow-up Date:** [Date]\n**Method:** [Call/Email/In-person]\n\n**Purpose of Follow-up:**\n[Why you're following up]\n\n**Key Points Discussed:**\n• [Point 1]\n• [Point 2]\n• [Point 3]\n\n**Updates Since Last Contact:**\n• [Update 1]\n• [Update 2]\n\n**New Information:**\n• [New info 1]\n• [New info 2]\n\n**Action Items from This Contact:**\n• [ ] [Action 1] - Due: [Date]\n• [ ] [Action 2] - Due: [Date]\n\n**Their Commitments:**\n• [Commitment 1]\n• [Commitment 2]\n\n**Next Steps:**\n• [Step 1]\n• [Step 2]\n\n**Next Contact Scheduled:** [Date and method]"
                ),
                NoteTemplate(
                    title: "Check-in",
                    description: "Regular relationship check-in",
                    content: "✅ **Regular Check-in**\n\n**Date:** [Date]\n**Type:** [Scheduled/Spontaneous]\n**Duration:** [Time]\n\n**Their Current Status:**\n• Work: [Work situation]\n• Personal: [Personal updates]\n• Health: [Health status]\n\n**What's Going Well:**\n• [Positive 1]\n• [Positive 2]\n\n**Current Challenges:**\n• [Challenge 1]\n• [Challenge 2]\n\n**How I Can Help:**\n• [Support 1]\n• [Support 2]\n\n**Opportunities Discussed:**\n• [Opportunity 1]\n• [Opportunity 2]\n\n**Personal Notes:**\n• [Personal detail 1]\n• [Personal detail 2]\n\n**Follow-up Actions:**\n• [ ] [Action 1]\n• [ ] [Action 2]\n\n**Next Check-in:** [Date]"
                )
            ]
        }
    }
}

struct NoteTemplate {
    let title: String
    let description: String
    let content: String
}

// MARK: - Enhanced Notes Interface

/// Beautiful notes interface with individual note cards and full-screen editing
struct EnhancedNotesInterface: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager

    // MARK: - State Variables
    @State private var notes: [NoteEntry] = []
    @State private var showingTemplates = false
    @State private var searchText = ""
    @State private var selectedCategory: NoteCategory = .general
    @State private var showingFullScreenEditor = false
    @State private var editingNote: NoteEntry?
    @State private var isCreatingNewNote = false

    // MARK: - Computed Properties

    private var formattedLastModified: String {
        guard let lastNote = notes.max(by: { $0.lastModified < $1.lastModified }) else {
            return "never"
        }

        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: lastNote.lastModified, relativeTo: Date())
    }

    var filteredNotes: [NoteEntry] {
        if searchText.isEmpty {
            return notes.sorted { $0.lastModified > $1.lastModified }
        } else {
            return notes.filter { note in
                note.title.localizedCaseInsensitiveContains(searchText) ||
                note.content.localizedCaseInsensitiveContains(searchText)
            }.sorted { $0.lastModified > $1.lastModified }
        }
    }

    var body: some View {
        ZStack {
            // Subtle gradient background with blur
            LinearGradient(
                colors: [
                    Color(.systemGroupedBackground),
                    person.relationshipType.color.opacity(0.03),
                    person.relationshipType.color.opacity(0.06)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            VStack(spacing: 0) {
                // Header with blur background
                notesHeader
                    .background(
                        .regularMaterial,
                        in: RoundedRectangle(cornerRadius: 0)
                    )
                    .overlay(
                        Rectangle()
                            .fill(person.relationshipType.color.opacity(0.12))
                            .frame(height: 1),
                        alignment: .bottom
                    )

                // Content
                notesContent
            }
        }
        .onAppear {
            loadNotesFromPerson()
        }
        .sheet(isPresented: $showingFullScreenEditor) {
            FullScreenNoteEditor(
                note: $editingNote,
                isCreatingNew: isCreatingNewNote,
                onSave: { savedNote in
                    saveNote(savedNote)
                },
                onDelete: { noteToDelete in
                    deleteNote(noteToDelete)
                }
            )
        }
        .sheet(isPresented: $showingTemplates) {
            SimpleTemplateSelector(
                onTemplateSelected: { templateContent in
                    createNoteFromTemplate(templateContent)
                    showingTemplates = false
                }
            )
        }
    }
    
    // MARK: - Header

    private var notesHeader: some View {
        VStack(spacing: 20) {
            headerTitleSection
            searchAndFilterSection
        }
        .padding(.horizontal, 16)
        .padding(.top, 16)
    }

    private var headerTitleSection: some View {
        HStack {
            VStack(alignment: .leading, spacing: 6) {
                Text("Notes & Interactions")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                HStack(spacing: 16) {
                    HStack(spacing: 4) {
                        Image(systemName: "note.text")
                            .font(.caption)
                            .foregroundColor(person.relationshipType.color)
                        Text("\(notes.count) notes")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    if !notes.isEmpty {
                        HStack(spacing: 4) {
                            Image(systemName: "clock")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("Updated \(formattedLastModified)")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }

            Spacer()

            // Floating action button
            Button(action: {
                createNewNote()
            }) {
                Image(systemName: "plus")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(LinearGradient(
                                colors: [person.relationshipType.color, person.relationshipType.color.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .shadow(color: person.relationshipType.color.opacity(0.4), radius: 8, x: 0, y: 4)
                    )
            }
        }
    }

    private var searchAndFilterSection: some View {
        VStack(spacing: 12) {
            searchBarSection

            if !notes.isEmpty {
                categoryFilterSection
            }
        }
    }

    private var searchBarSection: some View {
        HStack(spacing: 8) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)

            TextField("Search notes, content, or categories...", text: $searchText)
                .font(.body)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                }
            }

            // Templates quick access
            Button(action: {
                showingTemplates = true
            }) {
                Image(systemName: "doc.text.below.ecg")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(person.relationshipType.color)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(searchText.isEmpty ? Color(.systemGray5) : person.relationshipType.color.opacity(0.3), lineWidth: 1)
                )
        )
    }

    private var categoryFilterSection: some View {
        let categoriesWithCounts = NoteCategory.allCases.compactMap { category in
            let count = notes.filter { $0.category == category }.count
            return count > 0 ? (category, count) : nil
        }

        return ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(categoriesWithCounts.indices, id: \.self) { index in
                    let (category, count) = categoriesWithCounts[index]
                    CategoryFilterChip(
                        category: category,
                        count: count,
                        isSelected: selectedCategory == category,
                        action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                selectedCategory = selectedCategory == category ? .general : category
                            }
                        }
                    )
                }
            }
            .padding(.horizontal, 16)
        }
    }
    
    // MARK: - Notes Content

    private var notesContent: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if filteredNotes.isEmpty {
                    EnhancedEmptyNotesView(onStartEditing: {
                        createNewNote()
                    })
                    .padding(.horizontal, 16)
                } else {
                    ForEach(filteredNotes) { note in
                        NoteCard(
                            note: note,
                            onTap: {
                                editNote(note)
                            },
                            onDelete: {
                                deleteNote(note)
                            }
                        )
                        .padding(.horizontal, 16)
                    }
                }
            }
            .padding(.vertical, 16)
        }
    }

    // MARK: - Helper Methods

    private func loadNotesFromPerson() {
        // Convert old notes format to new format if needed
        if !person.notes.isEmpty && notes.isEmpty {
            let legacyNote = NoteEntry(
                title: "Legacy Notes",
                content: person.notes,
                category: .general
            )
            notes = [legacyNote]
            saveNotesToPerson()
        }

        // Load notes from person's notesData if available
        if let notesData = person.notesData,
           let decodedNotes = try? JSONDecoder().decode([NoteEntry].self, from: notesData) {
            notes = decodedNotes
        }
    }

    private func saveNotesToPerson() {
        if let encoded = try? JSONEncoder().encode(notes) {
            person.notesData = encoded
            // Also update the old notes field for backward compatibility
            person.notes = notes.map { "\($0.title): \($0.content)" }.joined(separator: "\n\n")
            peopleManager.savePeople()
        }
    }

    private func createNewNote() {
        let newNote = NoteEntry()
        editingNote = newNote
        isCreatingNewNote = true
        showingFullScreenEditor = true
    }

    private func createNoteFromTemplate(_ templateContent: String) {
        let lines = templateContent.components(separatedBy: .newlines)
        let title = lines.first?.replacingOccurrences(of: "**", with: "")
            .replacingOccurrences(of: "#", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines) ?? "New Note"

        let newNote = NoteEntry(
            title: title,
            content: templateContent,
            category: .general
        )
        editingNote = newNote
        isCreatingNewNote = true
        showingFullScreenEditor = true
    }

    private func editNote(_ note: NoteEntry) {
        editingNote = note
        isCreatingNewNote = false
        showingFullScreenEditor = true
    }

    private func saveNote(_ note: NoteEntry) {
        if let index = notes.firstIndex(where: { $0.id == note.id }) {
            notes[index] = note
        } else {
            notes.append(note)
        }
        saveNotesToPerson()
    }

    private func deleteNote(_ note: NoteEntry) {
        notes.removeAll { $0.id == note.id }
        saveNotesToPerson()
    }
}

// MARK: - Expandable Text Editor

struct ExpandableTextEditor: View {
    @Binding var text: String
    let category: NoteCategory
    @Binding var height: CGFloat
    
    @State private var isTyping = false
    
    var body: some View {
        VStack(spacing: 12) {
            // Rich text toolbar
            RichTextToolbar(text: $text)
            
            // Expandable text editor
            ZStack(alignment: .topLeading) {
                // Background with category color accent
                RoundedRectangle(cornerRadius: 16)
                    .fill(.regularMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isTyping ? category.color : Color(.systemGray4),
                                lineWidth: isTyping ? 2 : 1
                            )
                            .animation(.easeInOut(duration: 0.2), value: isTyping)
                    )
                
                // Text editor
                TextEditor(text: $text)
                    .font(.body)
                    .padding(16)
                    .background(Color.clear)
                    .frame(minHeight: 120)
                    .onTapGesture {
                        isTyping = true
                    }
                    .onChange(of: text) { _, newValue in
                        // Auto-expand based on content
                        let lineHeight: CGFloat = 20
                        let lines = max(6, newValue.components(separatedBy: .newlines).count)
                        height = max(120, CGFloat(lines) * lineHeight + 32)
                    }
                
                // Placeholder
                if text.isEmpty {
                    Text("Start writing your notes here...\n\nUse templates for quick formatting or write freely.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .padding(20)
                        .allowsHitTesting(false)
                }
            }
            .frame(height: height)
            .animation(.easeInOut(duration: 0.3), value: height)
            
            // Character count and category indicator
            HStack {
                HStack(spacing: 6) {
                    Circle()
                        .fill(category.color)
                        .frame(width: 8, height: 8)
                    
                    Text(category.rawValue.capitalized)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(category.color)
                }
                
                Spacer()
                
                Text("\(text.count) characters")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 4)
        }
        .onTapGesture {
            // Dismiss keyboard when tapping outside
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
            isTyping = false
        }
    }
}

// MARK: - Search Bar

struct SearchBar: View {
    @Binding var searchText: String
    @State private var isSearching = false
    
    var body: some View {
        HStack(spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(isSearching ? .blue : .secondary)
                    .animation(.easeInOut(duration: 0.2), value: isSearching)
                
                TextField("Search in notes...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onTapGesture {
                        isSearching = true
                    }
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                        isSearching = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.regularMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSearching ? .blue : Color(.systemGray4), lineWidth: 1)
                    )
            )
        }
        .onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
            isSearching = false
        }
    }
}

// MARK: - Notes Display Card

struct NotesDisplayCard: View {
    let notes: String
    let searchText: String
    let category: NoteCategory
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with metadata
            HStack {
                HStack(spacing: 6) {
                    Circle()
                        .fill(category.color)
                        .frame(width: 8, height: 8)
                    
                    Text(category.rawValue.capitalized)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(category.color)
                }
                
                Spacer()
                
                Text("Last updated: \(Date().formatted(date: .abbreviated, time: .shortened))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Divider()
            
            // Notes content
            ScrollView {
                Text(highlightedText)
                    .font(.body)
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.vertical, 4)
            }
            .frame(maxHeight: 300)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private var highlightedText: String {
        if searchText.isEmpty {
            return notes
        }
        // Simple highlighting - in production you'd use AttributedString
        return notes.replacingOccurrences(
            of: searchText,
            with: "🔍\(searchText)🔍",
            options: .caseInsensitive
        )
    }
}

// MARK: - Beautiful Template Gallery

struct BeautifulTemplateGallery: View {
    @Binding var selectedCategory: TemplateCategory
    let onTemplateSelected: (String) -> Void

    @State private var animatedCategories: [Bool] = Array(repeating: false, count: TemplateCategory.allCases.count)
    @State private var animatedTemplates: [Bool] = []

    var body: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Choose a Template")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    Text("Select a category and template to get started quickly")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            // Category selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(Array(TemplateCategory.allCases.enumerated()), id: \.offset) { index, category in
                        CategoryButton(
                            category: category,
                            isSelected: selectedCategory == category,
                            isAnimated: animatedCategories.indices.contains(index) ? animatedCategories[index] : false
                        ) {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                selectedCategory = category
                                setupTemplateAnimations()
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
            }

            // Templates grid
            ScrollView {
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: 16),
                    GridItem(.flexible(), spacing: 16)
                ], spacing: 16) {
                    ForEach(Array(selectedCategory.templates.enumerated()), id: \.offset) { index, template in
                        TemplateCard(
                            template: template,
                            category: selectedCategory,
                            isAnimated: animatedTemplates.indices.contains(index) ? animatedTemplates[index] : false
                        ) {
                            onTemplateSelected(template.content)
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
            .frame(maxHeight: 400)
        }
        .padding(.vertical, 20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        )
        .onAppear {
            setupCategoryAnimations()
            setupTemplateAnimations()
        }
        .onChange(of: selectedCategory) { _, _ in
            setupTemplateAnimations()
        }
    }

    private func setupCategoryAnimations() {
        animatedCategories = Array(repeating: false, count: TemplateCategory.allCases.count)

        for i in 0..<TemplateCategory.allCases.count {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.1) {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animatedCategories[i] = true
                }
            }
        }
    }

    private func setupTemplateAnimations() {
        animatedTemplates = Array(repeating: false, count: selectedCategory.templates.count)

        for i in 0..<selectedCategory.templates.count {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.1) {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    animatedTemplates[i] = true
                }
            }
        }
    }
}

// MARK: - Category Button

struct CategoryButton: View {
    let category: TemplateCategory
    let isSelected: Bool
    let isAnimated: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: category.icon)
                    .font(.system(size: 16, weight: .medium))

                Text(category.rawValue)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : category.color)
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? category.color : category.color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(category.color.opacity(isSelected ? 0 : 0.3), lineWidth: 1)
                    )
            )
        }
        .scaleEffect(isAnimated ? 1.0 : 0.8)
        .opacity(isAnimated ? 1.0 : 0.0)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isSelected)
    }
}

// MARK: - Template Card

struct TemplateCard: View {
    let template: NoteTemplate
    let category: TemplateCategory
    let isAnimated: Bool
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                // Header with icon
                HStack {
                    Image(systemName: category.icon)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(category.color)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(category.color.opacity(0.1))
                        )

                    Spacer()

                    Image(systemName: "arrow.up.right")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }

                // Content
                VStack(alignment: .leading, spacing: 6) {
                    Text(template.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    Text(template.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }

                Spacer()

                // Preview snippet
                Text(String(template.content.prefix(60)) + "...")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .frame(height: 160)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.regularMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(category.color.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : (isAnimated ? 1.0 : 0.8))
        .opacity(isAnimated ? 1.0 : 0.0)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

// MARK: - Enhanced Empty Notes View

struct EnhancedEmptyNotesView: View {
    let onStartEditing: () -> Void

    @State private var pulseAnimation = false

    var body: some View {
        VStack(spacing: 20) {
            // Animated icon
            Image(systemName: "note.text")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.secondary)
                .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: pulseAnimation)
                .onAppear { pulseAnimation = true }

            VStack(spacing: 8) {
                Text("No notes yet")
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("Start capturing important details about this person")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            // Action buttons
            VStack(spacing: 12) {
                Button(action: onStartEditing) {
                    HStack(spacing: 8) {
                        Image(systemName: "pencil")
                            .font(.system(size: 14, weight: .medium))
                        Text("Start Writing")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(LinearGradient(
                                colors: [.blue, .blue.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                    )
                }

                Text("or use a template to get started quickly")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(32)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
        )
    }
}

// MARK: - Missing Components

/// Note category selector
struct NoteCategorySelector: View {
    @Binding var selectedCategory: NoteCategory

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(NoteCategory.allCases, id: \.self) { category in
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            selectedCategory = category
                        }
                    }) {
                        HStack(spacing: 6) {
                            Circle()
                                .fill(category.color)
                                .frame(width: 8, height: 8)

                            Text(category.rawValue.capitalized)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(selectedCategory == category ? .white : category.color)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(selectedCategory == category ? category.color : category.color.opacity(0.1))
                        )
                    }
                }
            }
            .padding(.horizontal, 20)
        }
    }
}

/// Rich text toolbar
struct RichTextToolbar: View {
    @Binding var text: String

    var body: some View {
        HStack(spacing: 16) {
            // Quick formatting buttons
            HStack(spacing: 8) {
                ToolbarButton(icon: "bold", action: {
                    // Add bold formatting
                })

                ToolbarButton(icon: "italic", action: {
                    // Add italic formatting
                })

                ToolbarButton(icon: "list.bullet", action: {
                    // Add bullet point
                    text += "\n• "
                })

                ToolbarButton(icon: "number", action: {
                    // Add numbered list
                    text += "\n1. "
                })
            }

            Spacer()

            // Word count
            Text("\(text.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }.count) words")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 4)
    }
}

/// Toolbar button
struct ToolbarButton: View {
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 28, height: 28)
                .background(
                    Circle()
                        .fill(Color(.systemGray6))
                )
        }
    }
}

/// Note category enum
enum NoteCategory: String, CaseIterable, Codable {
    case general = "general"
    case business = "business"
    case personal = "personal"
    case meeting = "meeting"
    case project = "project"

    var color: Color {
        switch self {
        case .general: return .blue
        case .business: return .green
        case .personal: return .pink
        case .meeting: return .orange
        case .project: return .purple
        }
    }
}

// MARK: - Simple Template Selector

struct SimpleTemplateSelector: View {
    let onTemplateSelected: (String) -> Void
    @Environment(\.dismiss) private var dismiss

    private let templates = [
        ("Meeting Notes", "# Meeting Notes\n\n**Date:** \(Date().formatted(date: .abbreviated, time: .omitted))\n**Attendees:** \n\n## Agenda\n- \n\n## Discussion\n\n\n## Action Items\n- [ ] \n\n## Next Steps\n"),
        ("Quick Note", "# Quick Note\n\n**Date:** \(Date().formatted(date: .abbreviated, time: .omitted))\n\n"),
        ("Follow-up", "# Follow-up\n\n**Date:** \(Date().formatted(date: .abbreviated, time: .omitted))\n**Regarding:** \n\n## Summary\n\n\n## Next Actions\n- [ ] \n"),
        ("Personal", "# Personal Note\n\n**Date:** \(Date().formatted(date: .abbreviated, time: .omitted))\n\n")
    ]

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Choose a Template")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top)

                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 16) {
                    ForEach(templates.indices, id: \.self) { index in
                        let (title, content) = templates[index]
                        Button(action: {
                            onTemplateSelected(content)
                        }) {
                            VStack(spacing: 8) {
                                Image(systemName: "doc.text")
                                    .font(.title2)
                                    .foregroundColor(.blue)

                                Text(title)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .multilineTextAlignment(.center)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(.regularMaterial)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)

                Spacer()
            }
            .navigationTitle("Templates")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Category Filter Chip

struct CategoryFilterChip: View {
    let category: NoteCategory
    let count: Int
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Circle()
                    .fill(category.color)
                    .frame(width: 8, height: 8)

                Text(category.rawValue.capitalized)
                    .font(.caption)
                    .fontWeight(.medium)

                Text("(\(count))")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : category.color)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? category.color : category.color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(category.color.opacity(0.3), lineWidth: isSelected ? 0 : 1)
                    )
            )
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - Note Card

struct NoteCard: View {
    let note: NoteEntry
    let onTap: () -> Void
    let onDelete: () -> Void

    @State private var showingDeleteAlert = false

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    HStack(spacing: 8) {
                        Circle()
                            .fill(note.category.color)
                            .frame(width: 8, height: 8)

                        Text(note.category.rawValue.capitalized)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(note.category.color)
                    }

                    Spacer()

                    Menu {
                        Button(action: onTap) {
                            Label("Edit", systemImage: "pencil")
                        }

                        Button(role: .destructive, action: {
                            showingDeleteAlert = true
                        }) {
                            Label("Delete", systemImage: "trash")
                        }
                    } label: {
                        Image(systemName: "ellipsis")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.secondary)
                            .padding(8)
                            .background(Circle().fill(Color(.systemGray6)))
                    }
                }

                // Title
                if !note.title.isEmpty {
                    Text(note.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }

                // Preview
                if !note.preview.isEmpty {
                    Text(note.preview)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(3)
                        .multilineTextAlignment(.leading)
                }

                // Footer
                HStack {
                    Text(note.lastModified.formatted(date: .abbreviated, time: .shortened))
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(note.category.color.opacity(0.15), lineWidth: 1.5)
                    )
                    .shadow(color: note.category.color.opacity(0.1), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .alert("Delete Note", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                onDelete()
            }
        } message: {
            Text("Are you sure you want to delete this note? This action cannot be undone.")
        }
    }
}

// MARK: - Full Screen Note Editor

struct FullScreenNoteEditor: View {
    @Binding var note: NoteEntry?
    let isCreatingNew: Bool
    let onSave: (NoteEntry) -> Void
    let onDelete: (NoteEntry) -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var editingNote: NoteEntry = NoteEntry()
    @State private var showingDeleteAlert = false
    @State private var showingTemplates = false
    @State private var isAutoSaving = false
    @State private var showingFormatting = false
    @State private var titleFocused = false
    @State private var contentFocused = false
    @State private var hasUnsavedChanges = false
    @State private var isContentExpanded = false
    @State private var contentFieldHeight: CGFloat = 120

    // Auto-save timer
    @State private var autoSaveTimer: Timer?

    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: [
                        Color(.systemGroupedBackground),
                        editingNote.category.color.opacity(0.05)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                ScrollView {
                    VStack(spacing: 24) {
                        // Header section with category and quick actions
                        headerSection

                        // Title section
                        titleSection

                        // Content editor section
                        contentSection

                        // Quick formatting tools
                        if contentFocused {
                            formattingToolbar
                        }

                        // Bottom spacing for keyboard
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 16)
                }
            }
            .navigationTitle(isCreatingNew ? "New Note" : "Edit Note")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        if hasUnsavedChanges {
                            // Show save prompt
                            saveNote()
                        } else {
                            dismiss()
                        }
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: hasUnsavedChanges ? "checkmark" : "xmark")
                                .font(.system(size: 16, weight: .medium))
                            Text(hasUnsavedChanges ? "Save" : "Cancel")
                                .font(.body)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(hasUnsavedChanges ? editingNote.category.color : .secondary)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 12) {
                        // Auto-save indicator
                        if isAutoSaving {
                            HStack(spacing: 4) {
                                ProgressView()
                                    .scaleEffect(0.7)
                                Text("Saving...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        // Templates button
                        Button(action: {
                            showingTemplates = true
                        }) {
                            Image(systemName: "doc.text.below.ecg")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(editingNote.category.color)
                        }

                        // Delete button (for existing notes)
                        if !isCreatingNew {
                            Button(action: {
                                showingDeleteAlert = true
                            }) {
                                Image(systemName: "trash")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.red)
                            }
                        }
                    }
                }
            }
        }
        .onAppear {
            setupNote()
        }
        .onChange(of: editingNote.title) { _, _ in
            handleContentChange()
        }
        .onChange(of: editingNote.content) { _, _ in
            handleContentChange()
        }
        .sheet(isPresented: $showingTemplates) {
            SimpleTemplateSelector { templateContent in
                editingNote.content = templateContent
                let lines = templateContent.components(separatedBy: .newlines)
                if let firstLine = lines.first {
                    editingNote.title = firstLine.replacingOccurrences(of: "#", with: "")
                        .replacingOccurrences(of: "*", with: "")
                        .trimmingCharacters(in: .whitespacesAndNewlines)
                }
                showingTemplates = false
                hasUnsavedChanges = true
            }
        }
        .alert("Delete Note", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                if let noteToDelete = note {
                    onDelete(noteToDelete)
                }
                dismiss()
            }
        } message: {
            Text("Are you sure you want to delete this note? This action cannot be undone.")
        }
    }

    // MARK: - Header Section

    private var headerSection: some View {
        VStack(spacing: 16) {
            // Category selector with enhanced design
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Category")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

                    Spacer()

                    if isCreatingNew {
                        HStack(spacing: 4) {
                            Image(systemName: "sparkles")
                                .font(.caption)
                                .foregroundColor(editingNote.category.color)
                            Text("New Note")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(editingNote.category.color)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(editingNote.category.color.opacity(0.1))
                        )
                    }
                }

                // Enhanced category selector
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(NoteCategory.allCases, id: \.self) { category in
                            CategoryChip(
                                category: category,
                                isSelected: editingNote.category == category,
                                action: {
                                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                        editingNote.category = category
                                        hasUnsavedChanges = true
                                    }
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(editingNote.category.color.opacity(0.2), lineWidth: 1)
                    )
            )
        }
    }

    // MARK: - Title Section

    private var titleSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Title")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Spacer()

                if titleFocused {
                    Text("\(editingNote.title.count)/100")
                        .font(.caption)
                        .foregroundColor(editingNote.title.count > 80 ? .orange : .secondary)
                }
            }

            TextField("Enter note title...", text: $editingNote.title)
                .font(.title2)
                .fontWeight(.bold)
                .textFieldStyle(PlainTextFieldStyle())
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(.regularMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(
                                    titleFocused ? editingNote.category.color : Color(.systemGray5),
                                    lineWidth: titleFocused ? 2 : 1
                                )
                                .animation(.easeInOut(duration: 0.2), value: titleFocused)
                        )
                )
                .onTapGesture {
                    titleFocused = true
                }
                .onChange(of: editingNote.title) { _, _ in
                    hasUnsavedChanges = true
                }
        }
    }

    // MARK: - Content Section

    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Content")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Spacer()

                if !isContentExpanded {
                    HStack(spacing: 8) {
                        Text("\(editingNote.content.count) characters")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        HStack(spacing: 4) {
                            Image(systemName: "arrow.up.left.and.arrow.down.right")
                                .font(.caption)
                            Text("Tap to expand")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(editingNote.category.color.opacity(0.7))
                    }
                }
            }

            if !isContentExpanded {
                // Compact content preview
                compactContentEditor
            }
        }
        .fullScreenCover(isPresented: $isContentExpanded) {
            ExpandedContentEditor(
                content: $editingNote.content,
                category: editingNote.category,
                onDismiss: {
                    isContentExpanded = false
                    hasUnsavedChanges = true
                }
            )
        }
    }

    private var compactContentEditor: some View {
        ZStack(alignment: .topLeading) {
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(editingNote.category.color.opacity(0.3), lineWidth: 1)
                )

            // Invisible overlay for touch detection
            Rectangle()
                .fill(Color.clear)
                .contentShape(Rectangle())
                .onTapGesture {
                    expandContentEditor()
                }

            // Content preview (non-interactive)
            VStack(alignment: .leading, spacing: 12) {
                if editingNote.content.isEmpty {
                    HStack(spacing: 12) {
                        Image(systemName: "square.and.pencil")
                            .font(.title2)
                            .foregroundColor(editingNote.category.color.opacity(0.6))

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Tap anywhere to start writing...")
                                .font(.body)
                                .foregroundColor(.secondary)

                            Text("Opens full-screen editor")
                                .font(.caption)
                                .foregroundColor(.secondary.opacity(0.7))
                        }

                        Spacer()
                    }
                } else {
                    // Show content preview
                    VStack(alignment: .leading, spacing: 8) {
                        Text(editingNote.content)
                            .font(.body)
                            .foregroundColor(.primary)
                            .lineLimit(6)
                            .multilineTextAlignment(.leading)

                        if editingNote.content.components(separatedBy: .newlines).count > 6 {
                            HStack {
                                Text("...")
                                    .font(.body)
                                    .foregroundColor(.secondary)

                                Spacer()

                                HStack(spacing: 4) {
                                    Image(systemName: "arrow.up.left.and.arrow.down.right")
                                        .font(.caption)
                                    Text("Tap to expand")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                }
                                .foregroundColor(editingNote.category.color)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(editingNote.category.color.opacity(0.1))
                                )
                            }
                        }
                    }
                }

                Spacer()
            }
            .padding(16)
            .allowsHitTesting(false)
        }
        .frame(height: contentFieldHeight)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: contentFieldHeight)
    }

    // MARK: - Formatting Toolbar

    private var formattingToolbar: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Quick Formatting")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Spacer()

                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        showingFormatting.toggle()
                    }
                }) {
                    Image(systemName: showingFormatting ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(editingNote.category.color)
                }
            }

            if showingFormatting {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        FormatButton(icon: "bold", title: "Bold", action: {
                            insertFormatting("**", "**")
                        })

                        FormatButton(icon: "italic", title: "Italic", action: {
                            insertFormatting("*", "*")
                        })

                        FormatButton(icon: "list.bullet", title: "List", action: {
                            insertFormatting("- ", "")
                        })

                        FormatButton(icon: "checkmark.square", title: "Checkbox", action: {
                            insertFormatting("- [ ] ", "")
                        })

                        FormatButton(icon: "number", title: "Numbered", action: {
                            insertFormatting("1. ", "")
                        })

                        FormatButton(icon: "quote.bubble", title: "Quote", action: {
                            insertFormatting("> ", "")
                        })
                    }
                    .padding(.horizontal, 4)
                }
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(editingNote.category.color.opacity(0.2), lineWidth: 1)
                )
        )
    }

    // MARK: - Helper Functions

    private func setupNote() {
        if let note = note {
            editingNote = note
        }

        // Start auto-save timer
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
            if hasUnsavedChanges && !editingNote.isEmpty {
                autoSave()
            }
        }
    }

    private func handleContentChange() {
        hasUnsavedChanges = true

        // Reset auto-save timer
        autoSaveTimer?.invalidate()
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            if hasUnsavedChanges && !editingNote.isEmpty {
                autoSave()
            }
        }
    }

    private func autoSave() {
        guard !editingNote.isEmpty else { return }

        withAnimation(.easeInOut(duration: 0.3)) {
            isAutoSaving = true
        }

        editingNote.lastModified = Date()
        onSave(editingNote)

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            withAnimation(.easeInOut(duration: 0.3)) {
                isAutoSaving = false
                hasUnsavedChanges = false
            }
        }
    }

    private func saveNote() {
        editingNote.lastModified = Date()
        onSave(editingNote)
        dismiss()
    }

    private func insertFormatting(_ prefix: String, _ suffix: String) {
        let currentText = editingNote.content
        let newText = currentText + prefix + suffix
        editingNote.content = newText
        hasUnsavedChanges = true
    }

    private func expandContentEditor() {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            isContentExpanded = true
        }
    }

    private func updateContentHeight() {
        let baseHeight: CGFloat = 120
        let lineHeight: CGFloat = 20
        let lines = editingNote.content.isEmpty ? 0 : max(3, editingNote.content.components(separatedBy: .newlines).count)
        let contentBasedHeight = CGFloat(lines) * lineHeight + 40
        let newHeight = editingNote.content.isEmpty ? baseHeight : min(300, max(baseHeight, contentBasedHeight))

        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            contentFieldHeight = newHeight
        }
    }
}

// MARK: - Expanded Content Editor

struct ExpandedContentEditor: View {
    @Binding var content: String
    let category: NoteCategory
    let onDismiss: () -> Void

    @State private var isTyping = false
    @State private var showingFormatting = true
    @State private var keyboardHeight: CGFloat = 0
    @FocusState private var isTextEditorFocused: Bool

    var body: some View {
        NavigationView {
            ZStack {
                // Beautiful gradient background
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        category.color.opacity(0.03),
                        category.color.opacity(0.08)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Writing stats header
                    writingStatsHeader

                    // Main text editor
                    ScrollView {
                        VStack(spacing: 0) {
                            TextEditor(text: $content)
                                .font(.body)
                                .lineSpacing(4)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 16)
                                .background(Color.clear)
                                .focused($isTextEditorFocused)
                                .onTapGesture {
                                    isTyping = true
                                }
                                .frame(minHeight: UIScreen.main.bounds.height - 200)
                        }
                    }
                    .background(Color.clear)

                    // Formatting toolbar
                    if showingFormatting && isTextEditorFocused {
                        formattingToolbar
                            .transition(.move(edge: .bottom).combined(with: .opacity))
                    }
                }
            }
            .navigationTitle("Writing")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        onDismiss()
                    }
                    .fontWeight(.semibold)
                    .foregroundColor(category.color)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            showingFormatting.toggle()
                        }
                    }) {
                        Image(systemName: showingFormatting ? "textformat.alt" : "textformat")
                            .foregroundColor(category.color)
                    }
                }
            }
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isTextEditorFocused = true
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)) { notification in
            if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue {
                keyboardHeight = keyboardFrame.cgRectValue.height
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)) { _ in
            keyboardHeight = 0
        }
    }

    private var writingStatsHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 12) {
                    HStack(spacing: 4) {
                        Image(systemName: "textformat.abc")
                            .font(.caption)
                            .foregroundColor(category.color)
                        Text("\(content.count) characters")
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    HStack(spacing: 4) {
                        Image(systemName: "doc.text")
                            .font(.caption)
                            .foregroundColor(category.color)
                        Text("\(wordCount) words")
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    HStack(spacing: 4) {
                        Image(systemName: "list.number")
                            .font(.caption)
                            .foregroundColor(category.color)
                        Text("\(lineCount) lines")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
                .foregroundColor(.secondary)
            }

            Spacer()

            // Category indicator
            HStack(spacing: 6) {
                Circle()
                    .fill(category.color)
                    .frame(width: 8, height: 8)

                Text(category.rawValue.capitalized)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(category.color)
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 4)
            .background(
                Capsule()
                    .fill(category.color.opacity(0.1))
            )
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(.ultraThinMaterial)
    }

    private var formattingToolbar: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                FormatButton(icon: "bold", title: "Bold") {
                    insertFormatting("**", "**")
                }

                FormatButton(icon: "italic", title: "Italic") {
                    insertFormatting("*", "*")
                }

                FormatButton(icon: "list.bullet", title: "List") {
                    insertFormatting("\n- ", "")
                }

                FormatButton(icon: "checkmark.square", title: "Todo") {
                    insertFormatting("\n- [ ] ", "")
                }

                FormatButton(icon: "number", title: "Numbered") {
                    insertFormatting("\n1. ", "")
                }

                FormatButton(icon: "quote.bubble", title: "Quote") {
                    insertFormatting("\n> ", "")
                }

                FormatButton(icon: "textformat.size", title: "Header") {
                    insertFormatting("\n# ", "")
                }

                FormatButton(icon: "minus", title: "Divider") {
                    insertFormatting("\n---\n", "")
                }
            }
            .padding(.horizontal, 20)
        }
        .padding(.vertical, 12)
        .background(.regularMaterial)
    }

    private var wordCount: Int {
        content.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }.count
    }

    private var lineCount: Int {
        content.components(separatedBy: .newlines).count
    }

    private func insertFormatting(_ prefix: String, _ suffix: String) {
        content += prefix + suffix
    }
}

// MARK: - Supporting Components

struct CategoryChip: View {
    let category: NoteCategory
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Circle()
                    .fill(category.color)
                    .frame(width: 10, height: 10)

                Text(category.rawValue.capitalized)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .white : category.color)
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? category.color : category.color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(category.color.opacity(0.3), lineWidth: isSelected ? 0 : 1)
                    )
            )
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

struct FormatButton: View {
    let icon: String
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))

                Text(title)
                    .font(.caption2)
                    .fontWeight(.medium)
            }
            .foregroundColor(.primary)
            .frame(width: 60, height: 50)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.regularMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray5), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
