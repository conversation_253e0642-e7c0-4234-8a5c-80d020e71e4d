//
//  TeamEntityManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import Foundation
import CoreData
import SwiftUI

/// Manages the bridge between Team class and TeamEntity Core Data model
/// Provides project management capabilities and team-people-timeline integration
class TeamEntityManager: ObservableObject {
    
    // MARK: - Properties
    
    private let context: NSManagedObjectContext
    
    @Published var projectMilestones: [UUID: [ProjectMilestone]] = [:]
    @Published var teamResources: [UUID: [TeamResource]] = [:]
    @Published var teamGoals: [UUID: [TeamGoal]] = [:]
    @Published var memberRoles: [UUID: [MemberRole]] = [:]
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // MARK: - Core Data Bridge Methods
    
    /// Convert Team to TeamEntity
    func createTeamEntity(from team: Team) -> TeamEntity {
        let entity = TeamEntity(context: context)
        updateTeamEntity(entity, from: team)
        return entity
    }
    
    /// Update TeamEntity from Team
    func updateTeamEntity(_ entity: TeamEntity, from team: Team) {
        // Basic Information
        entity.id = team.id
        entity.name = team.name
        entity.teamDescription = team.description
        entity.category = team.category.rawValue
        entity.location = team.location.isEmpty ? nil : team.location
        
        // Project Management
        entity.projectStatus = team.projectStatus.rawValue
        entity.isActive = team.isActive
        entity.lastActivity = team.lastActivity
        entity.createdDate = entity.createdDate ?? Date()
        
        // Goals & Planning
        entity.progress = calculateTeamProgress(for: team)
        
        // Store complex data as JSON
        if !team.members.isEmpty {
            entity.goals = encodeToJSON(extractTeamGoals(from: team))
            entity.objectives = encodeToJSON(extractTeamObjectives(from: team))
            entity.resources = encodeToJSON(extractTeamResources(from: team))
            entity.timeline = encodeToJSON(extractTeamTimeline(from: team))
        }
    }
    
    /// Convert TeamEntity to Team
    func createTeam(from entity: TeamEntity) -> Team {
        let team = Team(
            name: entity.name ?? "Unknown Team",
            description: entity.teamDescription ?? "",
            category: TeamCategory(rawValue: entity.category ?? "all") ?? .all,
            location: entity.location ?? "",
            members: [], // Will be populated separately
            isActive: entity.isActive,
            projectStatus: Team.ProjectStatus(rawValue: entity.projectStatus ?? "planning") ?? .planning
        )
        
        // Set additional properties
        team.lastActivity = entity.lastActivity ?? Date()
        
        // Decode complex data from JSON
        if let goalsJSON = entity.goals {
            teamGoals[team.id] = decodeFromJSON([TeamGoal].self, from: goalsJSON) ?? []
        }
        if let resourcesJSON = entity.resources {
            teamResources[team.id] = decodeFromJSON([TeamResource].self, from: resourcesJSON) ?? []
        }
        
        return team
    }
    
    // MARK: - Project Management Methods
    
    /// Calculate team progress based on completed milestones and goals
    func calculateTeamProgress(for team: Team) -> Double {
        let milestones = projectMilestones[team.id] ?? []
        let goals = teamGoals[team.id] ?? []
        
        guard !milestones.isEmpty || !goals.isEmpty else { return 0.0 }
        
        let completedMilestones = milestones.filter { $0.isCompleted }.count
        let completedGoals = goals.filter { $0.isCompleted }.count
        
        let totalItems = milestones.count + goals.count
        let completedItems = completedMilestones + completedGoals
        
        return totalItems > 0 ? Double(completedItems) / Double(totalItems) : 0.0
    }
    
    /// Add project milestone
    func addProjectMilestone(for teamId: UUID, title: String, description: String, targetDate: Date, priority: MilestonePriority = .medium) {
        let milestone = ProjectMilestone(
            id: UUID(),
            teamId: teamId,
            title: title,
            description: description,
            targetDate: targetDate,
            priority: priority,
            isCompleted: false,
            createdDate: Date()
        )
        
        if projectMilestones[teamId] == nil {
            projectMilestones[teamId] = []
        }
        projectMilestones[teamId]?.append(milestone)
    }
    
    /// Complete project milestone
    func completeProjectMilestone(milestoneId: UUID) {
        for (teamId, milestones) in projectMilestones {
            if let index = milestones.firstIndex(where: { $0.id == milestoneId }) {
                projectMilestones[teamId]?[index].isCompleted = true
                projectMilestones[teamId]?[index].completedDate = Date()
                break
            }
        }
    }
    
    /// Add team goal
    func addTeamGoal(for teamId: UUID, title: String, description: String, targetDate: Date? = nil, category: GoalCategory = .general) {
        let goal = TeamGoal(
            id: UUID(),
            teamId: teamId,
            title: title,
            description: description,
            category: category,
            targetDate: targetDate,
            isCompleted: false,
            createdDate: Date()
        )
        
        if teamGoals[teamId] == nil {
            teamGoals[teamId] = []
        }
        teamGoals[teamId]?.append(goal)
    }
    
    /// Add team resource
    func addTeamResource(for teamId: UUID, name: String, type: ResourceType, description: String? = nil, url: String? = nil) {
        let resource = TeamResource(
            id: UUID(),
            teamId: teamId,
            name: name,
            type: type,
            description: description,
            url: url,
            isAvailable: true,
            createdDate: Date()
        )
        
        if teamResources[teamId] == nil {
            teamResources[teamId] = []
        }
        teamResources[teamId]?.append(resource)
    }
    
    /// Assign role to team member
    func assignMemberRole(for teamId: UUID, memberId: UUID, role: String, responsibilities: [String] = [], permissions: [String] = []) {
        let memberRole = MemberRole(
            id: UUID(),
            teamId: teamId,
            memberId: memberId,
            role: role,
            responsibilities: responsibilities,
            permissions: permissions,
            assignedDate: Date()
        )
        
        if memberRoles[teamId] == nil {
            memberRoles[teamId] = []
        }
        
        // Remove existing role for this member
        memberRoles[teamId]?.removeAll { $0.memberId == memberId }
        
        // Add new role
        memberRoles[teamId]?.append(memberRole)
    }
    
    // MARK: - Timeline Integration Methods
    
    /// Link team to timeline entry
    func linkTeamToTimelineEntry(teamId: UUID, timelineEntryId: UUID) {
        // Update timeline entry to include this team
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", timelineEntryId as CVarArg)
        
        do {
            let entities = try context.fetch(request)
            if let entity = entities.first {
                var linkedTeamIDs = decodeFromJSON([UUID].self, from: entity.linkedTeamIDs ?? "[]") ?? []
                if !linkedTeamIDs.contains(teamId) {
                    linkedTeamIDs.append(teamId)
                    entity.linkedTeamIDs = encodeToJSON(linkedTeamIDs)
                    try context.save()
                }
            }
        } catch {
            print("Error linking team to timeline entry: \(error)")
        }
    }
    
    /// Get timeline entries linked to team
    func getLinkedTimelineEntries(for teamId: UUID) -> [TimelineEntryEntity] {
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        
        do {
            let allEntries = try context.fetch(request)
            return allEntries.filter { entry in
                guard let linkedTeamIDsJSON = entry.linkedTeamIDs else { return false }
                let linkedTeamIDs = decodeFromJSON([UUID].self, from: linkedTeamIDsJSON) ?? []
                return linkedTeamIDs.contains(teamId)
            }
        } catch {
            print("Error fetching linked timeline entries: \(error)")
            return []
        }
    }
    
    // MARK: - Helper Methods
    
    private func extractTeamGoals(from team: Team) -> [String] {
        // Extract goals from team notifications or other sources
        return team.notifications.compactMap { notification in
            if notification.message.contains("goal") || notification.message.contains("objective") {
                return notification.message
            }
            return nil
        }
    }
    
    private func extractTeamObjectives(from team: Team) -> [String] {
        // Extract objectives based on project status and events
        var objectives: [String] = []
        
        switch team.projectStatus {
        case .planning:
            objectives.append("Complete project planning phase")
        case .inProgress:
            objectives.append("Execute project milestones")
        case .review:
            objectives.append("Review and finalize deliverables")
        case .completed:
            objectives.append("Document lessons learned")
        case .onHold:
            objectives.append("Resolve blocking issues")
        }
        
        return objectives
    }
    
    private func extractTeamResources(from team: Team) -> [String] {
        // Extract resources from team members and events
        var resources: [String] = []
        
        resources.append("Team Members: \(team.members.count)")
        resources.append("Location: \(team.location)")
        
        if !team.upcomingEvents.isEmpty {
            resources.append("Scheduled Events: \(team.upcomingEvents.count)")
        }
        
        return resources
    }
    
    private func extractTeamTimeline(from team: Team) -> [String] {
        // Extract timeline information from events
        return team.upcomingEvents.map { event in
            "\(event.title) - \(event.date.formatted(date: .abbreviated, time: .shortened))"
        }
    }
    
    private func encodeToJSON<T: Codable>(_ object: T) -> String? {
        guard let data = try? JSONEncoder().encode(object) else { return nil }
        return String(data: data, encoding: .utf8)
    }
    
    private func decodeFromJSON<T: Codable>(_ type: T.Type, from json: String) -> T? {
        guard let data = json.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }
}

// MARK: - Supporting Models

/// Represents a project milestone
struct ProjectMilestone: Identifiable, Codable {
    let id: UUID
    let teamId: UUID
    let title: String
    let description: String
    let targetDate: Date
    let priority: MilestonePriority
    var isCompleted: Bool
    let createdDate: Date
    var completedDate: Date?
    var assignedMemberIds: [UUID] = []
    var dependencies: [UUID] = [] // Other milestone IDs
}

enum MilestonePriority: String, CaseIterable, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case critical = "Critical"

    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .yellow
        case .high: return .orange
        case .critical: return .red
        }
    }
}

/// Represents a team goal
struct TeamGoal: Identifiable, Codable {
    let id: UUID
    let teamId: UUID
    let title: String
    let description: String
    let category: GoalCategory
    let targetDate: Date?
    var isCompleted: Bool
    let createdDate: Date
    var completedDate: Date?
    var progress: Double = 0.0
}

enum GoalCategory: String, CaseIterable, Codable {
    case general = "General"
    case performance = "Performance"
    case learning = "Learning"
    case collaboration = "Collaboration"
    case innovation = "Innovation"

    var icon: String {
        switch self {
        case .general: return "target"
        case .performance: return "chart.line.uptrend.xyaxis"
        case .learning: return "book.fill"
        case .collaboration: return "person.2.fill"
        case .innovation: return "lightbulb.fill"
        }
    }
}

/// Represents a team resource
struct TeamResource: Identifiable, Codable {
    let id: UUID
    let teamId: UUID
    let name: String
    let type: ResourceType
    let description: String?
    let url: String?
    var isAvailable: Bool
    let createdDate: Date
    var lastUsed: Date?
}

enum ResourceType: String, CaseIterable, Codable {
    case document = "Document"
    case tool = "Tool"
    case equipment = "Equipment"
    case software = "Software"
    case reference = "Reference"
    case template = "Template"

    var icon: String {
        switch self {
        case .document: return "doc.fill"
        case .tool: return "wrench.fill"
        case .equipment: return "desktopcomputer"
        case .software: return "app.fill"
        case .reference: return "book.closed.fill"
        case .template: return "rectangle.on.rectangle"
        }
    }
}

/// Represents a team member's role and responsibilities
struct MemberRole: Identifiable, Codable {
    let id: UUID
    let teamId: UUID
    let memberId: UUID
    let role: String
    let responsibilities: [String]
    let permissions: [String]
    let assignedDate: Date
    var isActive: Bool = true
}
