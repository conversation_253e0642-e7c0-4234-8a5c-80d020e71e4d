//
//  MemoryTimelineView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Beautiful memory timeline showing shared experiences and relationship evolution
/// Creates emotional connection through visual storytelling
struct MemoryTimelineView: View {
    let person: Person
    @State private var memories: [Memory] = []
    @State private var selectedMemory: Memory?
    @State private var showingMemoryDetail = false
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVStack(spacing: 20) {
                // Timeline header
                TimelineHeaderView(person: person)
                    .padding(.horizontal)
                
                // Memory cards
                ForEach(Array(memories.enumerated()), id: \.element.id) { index, memory in
                    MemoryCardView(
                        memory: memory,
                        index: index,
                        isEven: index % 2 == 0
                    )
                    .onTapGesture {
                        selectedMemory = memory
                        showingMemoryDetail = true
                    }
                    .modifier(MemoryAppearanceAnimation(index: index))
                }
            }
            .padding(.vertical)
        }
        .background(
            LinearGradient(
                colors: [
                    Color(.systemBackground),
                    person.relationshipType.color.opacity(0.05)
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .onAppear {
            loadMemories()
        }
        .sheet(isPresented: $showingMemoryDetail) {
            if let memory = selectedMemory {
                MemoryDetailView(memory: memory, person: person)
            }
        }
    }
    
    private func loadMemories() {
        // Generate sample memories based on relationship
        memories = generateSampleMemories(for: person)
    }
}

/// Timeline header with relationship insights
struct TimelineHeaderView: View {
    let person: Person
    
    var body: some View {
        VStack(spacing: 16) {
            // Person avatar with emotional glow
            AsyncImage(url: URL(string: person.avatarImageName)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(person.relationshipType.color)
            }
            .frame(width: 80, height: 80)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(
                        LinearGradient(
                            colors: [
                                person.relationshipType.color,
                                person.relationshipType.color.opacity(0.3)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 3
                    )
            )
            .shadow(color: person.relationshipType.color.opacity(0.3), radius: 10)
            
            // Relationship info
            VStack(spacing: 8) {
                Text(person.name)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(person.interactionFrequency.emotionalDescription)
                    .font(.subheadline)
                    .foregroundColor(person.relationshipType.color)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(person.relationshipType.color.opacity(0.1))
                    )
            }
            
            // Timeline stats
            HStack(spacing: 30) {
                StatView(
                    title: "Memories",
                    value: "\(memories.count)",
                    color: person.relationshipType.color
                )
                
                StatView(
                    title: "Connection",
                    value: "\(Int(person.interactionFrequency.emotionalIntensity * 100))%",
                    color: person.relationshipType.color
                )
                
                StatView(
                    title: "Years",
                    value: "2+",
                    color: person.relationshipType.color
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 10)
        )
    }
    
    @State private var memories: [Memory] = []
}

/// Individual memory card with beautiful design
struct MemoryCardView: View {
    let memory: Memory
    let index: Int
    let isEven: Bool
    
    var body: some View {
        HStack(spacing: 0) {
            if !isEven {
                Spacer()
            }
            
            // Memory content
            VStack(alignment: .leading, spacing: 12) {
                // Memory type icon
                HStack {
                    Image(systemName: memory.type.icon)
                        .font(.title3)
                        .foregroundColor(memory.type.color)
                    
                    Spacer()
                    
                    Text(memory.date, style: .date)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // Memory title
                Text(memory.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                // Memory description
                Text(memory.description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                
                // Emotional indicator
                HStack {
                    ForEach(0..<memory.emotionalIntensity, id: \.self) { _ in
                        Image(systemName: "heart.fill")
                            .font(.caption)
                            .foregroundColor(memory.type.color)
                    }
                    
                    Spacer()
                    
                    Text(memory.type.rawValue)
                        .font(.caption)
                        .foregroundColor(memory.type.color)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(memory.type.color.opacity(0.1))
                        )
                }
            }
            .padding()
            .frame(maxWidth: 280)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(color: memory.type.color.opacity(0.2), radius: 8)
            )
            .overlay(
                // Timeline connector
                Rectangle()
                    .fill(memory.type.color)
                    .frame(width: 3, height: 40)
                    .offset(x: isEven ? -20 : 20)
            )
            
            if isEven {
                Spacer()
            }
        }
        .padding(.horizontal)
    }
}

/// Memory detail view
struct MemoryDetailView: View {
    let memory: Memory
    let person: Person
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Memory header
                    VStack(spacing: 16) {
                        Image(systemName: memory.type.icon)
                            .font(.system(size: 60))
                            .foregroundColor(memory.type.color)
                        
                        Text(memory.title)
                            .font(.title)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)
                        
                        Text(memory.date, style: .date)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    
                    // Memory content
                    VStack(alignment: .leading, spacing: 16) {
                        Text(memory.description)
                            .font(.body)
                        
                        // Emotional impact
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Emotional Impact")
                                .font(.headline)
                            
                            HStack {
                                ForEach(0..<5, id: \.self) { index in
                                    Image(systemName: index < memory.emotionalIntensity ? "heart.fill" : "heart")
                                        .foregroundColor(memory.type.color)
                                }
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                    )
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Memory")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

/// Memory data model
struct Memory: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let date: Date
    let type: MemoryType
    let emotionalIntensity: Int // 1-5 scale
    
    enum MemoryType: String, CaseIterable, Codable {
        case meeting = "First Meeting"
        case achievement = "Achievement"
        case celebration = "Celebration"
        case milestone = "Milestone"
        case adventure = "Adventure"
        case support = "Support"
        case collaboration = "Collaboration"
        
        var icon: String {
            switch self {
            case .meeting: return "person.2"
            case .achievement: return "trophy"
            case .celebration: return "party.popper"
            case .milestone: return "flag"
            case .adventure: return "map"
            case .support: return "heart.circle"
            case .collaboration: return "hands.sparkles"
            }
        }
        
        var color: Color {
            switch self {
            case .meeting: return .blue
            case .achievement: return .yellow
            case .celebration: return .purple
            case .milestone: return .green
            case .adventure: return .orange
            case .support: return .red
            case .collaboration: return .cyan
            }
        }
    }
}

/// Animation for memory appearance
struct MemoryAppearanceAnimation: ViewModifier {
    let index: Int
    @State private var hasAppeared = false
    
    func body(content: Content) -> some View {
        content
            .opacity(hasAppeared ? 1 : 0)
            .offset(x: hasAppeared ? 0 : (index % 2 == 0 ? -100 : 100))
            .onAppear {
                let delay = Double(index) * 0.1
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    withAnimation(
                        .spring(response: 0.8, dampingFraction: 0.8)
                    ) {
                        hasAppeared = true
                    }
                }
            }
    }
}

/// Stat view for timeline header
struct StatView: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

/// Generate sample memories for demonstration
func generateSampleMemories(for person: Person) -> [Memory] {
    let sampleMemories = [
        Memory(
            title: "First Meeting",
            description: "Met \(person.name) at a coffee shop. Instant connection over shared interests in technology and design.",
            date: Calendar.current.date(byAdding: .month, value: -8, to: Date()) ?? Date(),
            type: .meeting,
            emotionalIntensity: 4
        ),
        Memory(
            title: "Project Success",
            description: "Successfully completed our first collaboration project. Great teamwork and amazing results!",
            date: Calendar.current.date(byAdding: .month, value: -5, to: Date()) ?? Date(),
            type: .achievement,
            emotionalIntensity: 5
        ),
        Memory(
            title: "Birthday Celebration",
            description: "Celebrated \(person.name)'s birthday with a surprise party. Such a wonderful evening with great friends.",
            date: Calendar.current.date(byAdding: .month, value: -3, to: Date()) ?? Date(),
            type: .celebration,
            emotionalIntensity: 5
        )
    ]
    
    return sampleMemories
}
