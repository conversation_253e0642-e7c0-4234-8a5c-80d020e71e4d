//
//  UnifiedSearchInterface.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import SwiftUIX

/// Main unified search interface with advanced features
struct UnifiedSearchInterface: View {
    @ObservedObject var searchManager: SearchManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @State private var showingSearchSheet = false
    @State private var searchText = ""
    @FocusState private var isSearchFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // Search header with section filters
            SearchHeaderView(
                searchManager: searchManager,
                searchText: $searchText,
                isSearchFocused: $isSearchFocused
            )
            .padding(.horizontal)
            .padding(.top, 8)
            
            // Search content area
            if searchManager.isSearching {
                SearchLoadingView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if !searchManager.searchResults.isEmpty {
                SearchResultsView(
                    searchManager: searchManager,
                    navigationCoordinator: navigationCoordinator
                )
            } else if !searchText.isEmpty {
                SearchEmptyStateView(query: searchText)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                SearchHomeView(searchManager: searchManager)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .background(Color(.systemGroupedBackground))
        .onChange(of: searchText) { newValue in
            searchManager.updateQuery(newValue)
        }
        .sheet(isPresented: $searchManager.showingFilters) {
            SearchFiltersSheet(searchManager: searchManager)
        }
        .sheet(isPresented: $searchManager.showingHistory) {
            SearchHistorySheet(searchManager: searchManager)
        }
    }
}

/// Search header with input field and section filters
struct SearchHeaderView: View {
    @ObservedObject var searchManager: SearchManager
    @Binding var searchText: String
    var isSearchFocused: FocusState<Bool>.Binding
    
    var body: some View {
        VStack(spacing: 12) {
            // Search input with suggestions
            HStack(spacing: 12) {
                // Search field
                HStack(spacing: 8) {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                        .font(.system(size: 16))
                    
                    TextField("Search people, teams, timeline...", text: $searchText)
                        .focused(isSearchFocused)
                        .textFieldStyle(PlainTextFieldStyle())
                        .onSubmit {
                            searchManager.search()
                        }
                    
                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                            searchManager.clearSearch()
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                                .font(.system(size: 16))
                        }
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
                .background(Color(.systemBackground))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(isSearchFocused.wrappedValue ? Color.blue : Color.clear, lineWidth: 2)
                )
                
                // Filter button
                Button(action: {
                    searchManager.showingFilters = true
                }) {
                    ZStack {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.system(size: 20))
                            .foregroundColor(.blue)
                        
                        if searchManager.currentQuery.hasActiveFilters {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 8, height: 8)
                                .offset(x: 8, y: -8)
                        }
                    }
                }
                
                // History button
                Button(action: {
                    searchManager.showingHistory = true
                }) {
                    Image(systemName: "clock")
                        .font(.system(size: 20))
                        .foregroundColor(.blue)
                }
            }
            
            // Section filter tabs
            SearchSectionTabs(searchManager: searchManager)
            
            // Search suggestions (when focused and has suggestions)
            if isSearchFocused.wrappedValue && !searchManager.suggestions.isEmpty {
                SearchSuggestionsView(
                    suggestions: searchManager.suggestions,
                    onSuggestionTapped: { suggestion in
                        searchText = suggestion.text
                        searchManager.updateSection(suggestion.section)
                        searchManager.search()
                        isSearchFocused.wrappedValue = false
                    }
                )
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isSearchFocused.wrappedValue)
        .animation(.easeInOut(duration: 0.2), value: searchManager.suggestions.isEmpty)
    }
}

/// Section filter tabs
struct SearchSectionTabs: View {
    @ObservedObject var searchManager: SearchManager
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(SearchSection.allCases) { section in
                    SearchSectionTab(
                        section: section,
                        isSelected: searchManager.currentQuery.section == section,
                        action: {
                            searchManager.updateSection(section)
                        }
                    )
                }
            }
            .padding(.horizontal, 4)
        }
    }
}

/// Individual section tab
struct SearchSectionTab: View {
    let section: SearchSection
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: section.icon)
                    .font(.system(size: 14, weight: .medium))
                
                Text(section.rawValue)
                    .font(.system(size: 14, weight: .medium))
            }
            .foregroundColor(isSelected ? .white : section.color)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? section.color : Color.clear)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(section.color, lineWidth: isSelected ? 0 : 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// Search suggestions dropdown
struct SearchSuggestionsView: View {
    let suggestions: [SearchSuggestion]
    let onSuggestionTapped: (SearchSuggestion) -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            ForEach(suggestions) { suggestion in
                Button(action: {
                    onSuggestionTapped(suggestion)
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: suggestion.section.icon)
                            .font(.system(size: 14))
                            .foregroundColor(suggestion.section.color)
                            .frame(width: 20)
                        
                        Text(suggestion.text)
                            .font(.system(size: 15))
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        if suggestion.type == .recent {
                            Image(systemName: "clock")
                                .font(.system(size: 12))
                                .foregroundColor(.secondary)
                        } else if suggestion.type == .popular {
                            Image(systemName: "flame")
                                .font(.system(size: 12))
                                .foregroundColor(.orange)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                }
                .buttonStyle(PlainButtonStyle())
                
                if suggestion.id != suggestions.last?.id {
                    Divider()
                        .padding(.leading, 44)
                }
            }
        }
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

/// Search loading state
struct SearchLoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("Searching...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

/// Empty search state
struct SearchEmptyStateView: View {
    let query: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("No results found")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("Try adjusting your search or filters")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(40)
    }
}

/// Search home view (when no search is active)
struct SearchHomeView: View {
    @ObservedObject var searchManager: SearchManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Recent searches
                if !searchManager.searchHistory.isEmpty {
                    SearchHomeSection(
                        title: "Recent Searches",
                        icon: "clock",
                        color: .blue
                    ) {
                        ForEach(searchManager.searchHistory.prefix(5)) { entry in
                            SearchHistoryRow(
                                entry: entry,
                                onTap: {
                                    searchManager.searchFromHistory(entry)
                                }
                            )
                        }
                    }
                }
                
                // Quick actions
                SearchHomeSection(
                    title: "Quick Actions",
                    icon: "bolt",
                    color: .orange
                ) {
                    QuickSearchActionsView(searchManager: searchManager)
                }
                
                // Search tips
                SearchHomeSection(
                    title: "Search Tips",
                    icon: "lightbulb",
                    color: .yellow
                ) {
                    SearchTipsView()
                }
            }
            .padding()
        }
    }
}

/// Search home section container
struct SearchHomeSection<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    @ViewBuilder let content: Content
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(color)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            content
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

// MARK: - Global Search Bar Component

/// Compact global search bar for integration into main views
struct GlobalSearchBar: View {
    @State private var showingSearch = false
    @State private var searchText = ""

    var body: some View {
        Button(action: {
            showingSearch = true
        }) {
            HStack(spacing: 8) {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16))

                Text("Search people, teams, timeline...")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16))

                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(Color(.systemGray6))
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingSearch) {
            SearchSheetView()
        }
    }
}

/// Full search sheet view
struct SearchSheetView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var peopleManager = PeopleManager()
    @StateObject private var teamManager = TeamManager()
    @StateObject private var timelineManager = EvolutionTimelineManager()
    @StateObject private var navigationCoordinator = NavigationCoordinator()
    @StateObject private var searchManager: SearchManager

    init() {
        let peopleManager = PeopleManager()
        let teamManager = TeamManager()
        let timelineManager = EvolutionTimelineManager()
        let navigationCoordinator = NavigationCoordinator()

        self._peopleManager = StateObject(wrappedValue: peopleManager)
        self._teamManager = StateObject(wrappedValue: teamManager)
        self._timelineManager = StateObject(wrappedValue: timelineManager)
        self._navigationCoordinator = StateObject(wrappedValue: navigationCoordinator)
        self._searchManager = StateObject(wrappedValue: SearchManager(
            peopleManager: peopleManager,
            teamManager: teamManager,
            timelineManager: timelineManager,
            navigationCoordinator: navigationCoordinator
        ))
    }

    var body: some View {
        NavigationView {
            UnifiedSearchInterface(
                searchManager: searchManager,
                navigationCoordinator: navigationCoordinator
            )
            .navigationTitle("Search")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
