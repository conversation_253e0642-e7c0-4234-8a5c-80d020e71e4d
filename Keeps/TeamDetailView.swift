//
//  TeamDetailView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Detailed view of a team with full functionality and real-time updates
/// Now includes member management, event creation, and team settings
struct TeamDetailView: View {
    @ObservedObject var team: Team
    let teamManager: TeamManager
    @Environment(\.dismiss) private var dismiss
    @State private var showingAddMember = false
    @State private var showingAddEvent = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background
                Color(.systemGroupedBackground)
                    .ignoresSafeArea()
                
                CocoaScrollView {
                    VStack(spacing: 24) {
                        // Team header with members
                        TeamHeaderView(team: team)
                            .transition(.move(edge: .top).combined(with: .opacity))

                        // Team overview section
                        TeamOverviewView(team: team)
                            .transition(.move(edge: .leading).combined(with: .opacity))

                        // Team notifications
                        if !team.notifications.isEmpty {
                            TeamNotificationsView(team: team)
                        }

                        // Upcoming events section
                        UpcomingEventsView(team: team, teamManager: teamManager, showingAddEvent: $showingAddEvent)
                            .transition(.move(edge: .bottom).combined(with: .opacity))

                        // Bottom padding for floating button
                        Spacer(minLength: 80)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .animation(.spring(response: 0.8, dampingFraction: 0.8), value: team.id)
                }
                .scrollIndicators(.hidden)
                
                // Floating action buttons
                VStack {
                    Spacer()
                    HStack {
                        Spacer()

                        HStack(spacing: 12) {
                            // Add member button
                            Button(action: {
                                showingAddMember = true
                            }) {
                                Image(systemName: "person.badge.plus")
                                    .font(.system(size: 18, weight: .medium))
                                    .foregroundColor(.white)
                                    .frame(width: 44, height: 44)
                                    .background(Circle().fill(Color.green))
                            }

                            // Add event button
                            Button(action: {
                                showingAddEvent = true
                            }) {
                                Image(systemName: "calendar.badge.plus")
                                    .font(.system(size: 18, weight: .medium))
                                    .foregroundColor(.white)
                                    .frame(width: 44, height: 44)
                                    .background(Circle().fill(Color.blue))
                            }
                        }
                        .padding(.trailing, 20)
                        .padding(.bottom, 34)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .principal) {
                    VStack {
                        Text(team.name)
                            .font(.headline)
                        Text(team.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
}

/// Team header showing member photos and basic info
struct TeamHeaderView: View {
    let team: Team
    
    var body: some View {
        VStack(spacing: 16) {
            // Member photos with names and roles
            HStack(spacing: 20) {
                ForEach(team.members.prefix(3)) { member in
                    VStack(spacing: 8) {
                        MemberAvatarView(member: member)
                            .scaleEffect(1.2) // Larger avatars in detail view
                        
                        VStack(spacing: 2) {
                            Text(member.name)
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.primary)
                                .multilineTextAlignment(.center)
                            
                            Text(member.role)
                                .font(.system(size: 12, weight: .regular))
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
    }
}

/// Team overview section with statistics and heatmap
struct TeamOverviewView: View {
    let team: Team
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section header
            HStack {
                Text("Team Overview")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("\(team.memberCount)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
            }
            
            VStack(spacing: 12) {
                // Timezone range
                OverviewRowView(
                    icon: "globe",
                    title: "Team timezone range",
                    subtitle: "Spanning",
                    value: team.timezoneRange
                )
                
                // Next sync time
                OverviewRowView(
                    icon: "clock",
                    title: "Next sync time",
                    subtitle: "Next standup",
                    value: team.nextSyncTime
                )
                
                // Heatmap
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "chart.bar.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.secondary)
                            .frame(width: 20)
                        
                        Text("Heatmap")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                        
                        Spacer()
                    }
                    
                    HeatmapView(data: team.heatmapData)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
            )
        }
    }
}

/// Individual row in the overview section
struct OverviewRowView: View {
    let icon: String
    let title: String
    let subtitle: String
    let value: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(value)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)
        }
    }
}

/// Heatmap visualization component
struct HeatmapView: View {
    let data: [Double]
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(Array(data.enumerated()), id: \.offset) { index, value in
                RoundedRectangle(cornerRadius: 2)
                    .fill(Color.blue.opacity(value))
                    .frame(width: 12, height: 20)
            }
        }
        .padding(.leading, 24) // Align with text
    }
}



/// Individual event row
struct EventRowView: View {
    let event: TeamEvent
    
    var body: some View {
        HStack(spacing: 12) {
            // Event type indicator
            RoundedRectangle(cornerRadius: 2)
                .fill(event.type.color)
                .frame(width: 4, height: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(event.title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                if let platform = event.platform {
                    Text(platform)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(event.time)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                
                HStack(spacing: 4) {
                    Image(systemName: "person.fill")
                        .font(.system(size: 10))
                        .foregroundColor(.secondary)
                    Text("\(event.attendeeCount)")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemBackground))
        )
    }
}

/// Floating add button
struct FloatingAddButton: View {
    var body: some View {
        Button(action: {
            // Handle add action
        }) {
            Image(systemName: "plus")
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 56, height: 56)
                .background(
                    Circle()
                        .fill(Color.black)
                )
                .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
        }
    }
}

/// Team notifications section
struct TeamNotificationsView: View {
    @ObservedObject var team: Team

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Recent Activity")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)

                Spacer()

                Text("\(team.notifications.count)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
            }

            VStack(spacing: 8) {
                ForEach(team.notifications.prefix(3)) { notification in
                    HStack {
                        Image(systemName: notification.type.icon)
                            .font(.caption)
                            .foregroundColor(notification.type.color)
                            .frame(width: 16)

                        Text(notification.message)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)

                        Spacer()

                        Text(formatTimestamp(notification.timestamp))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(12)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }

    private func formatTimestamp(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

/// Enhanced upcoming events view with management capabilities
struct UpcomingEventsView: View {
    @ObservedObject var team: Team
    let teamManager: TeamManager
    @Binding var showingAddEvent: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Upcoming")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)

                Spacer()

                Text("\(team.upcomingEvents.count)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)

                Button(action: {
                    showingAddEvent = true
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title3)
                        .foregroundColor(.blue)
                }
            }

            if team.upcomingEvents.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "calendar")
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Text("No upcoming events")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Button("Add Event") {
                        showingAddEvent = true
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                .frame(maxWidth: .infinity)
                .padding(20)
                .background(Color(.systemGray6))
                .cornerRadius(12)
            } else {
                VStack(spacing: 12) {
                    ForEach(team.upcomingEvents) { event in
                        EventRowView(event: event)
                    }
                }
            }
        }
    }
}

#Preview {
    let teamManager = TeamManager()
    let teams = SampleData.createSampleTeams()

    return TeamDetailView(team: teams[0], teamManager: teamManager)
}
