//
//  OnboardingStepViews.swift
//  Keeps
//
//  Individual step views for the onboarding flow
//

import SwiftUI

// MARK: - Personal Info Step

/// Personal information collection step
struct PersonalInfoStepView: View {
    @ObservedObject var onboardingManager: OnboardingManager
    @State private var animateForm = false
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // Title
            VStack(spacing: 16) {
                Text("About You")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("Let's personalize your Keeps experience")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 40)
            
            // Form
            VStack(spacing: 24) {
                // Name field
                VStack(alignment: .leading, spacing: 8) {
                    Text("Your Name")
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    TextField("Enter your name", text: $onboardingManager.userName)
                        .textFieldStyle(OnboardingTextFieldStyle())
                }
                
                // Birth date field
                VStack(alignment: .leading, spacing: 8) {
                    Text("Birth Date")
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    DatePicker("", selection: $onboardingManager.userBirthDate, displayedComponents: .date)
                        .datePickerStyle(.compact)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(12)
                }
                
                // Life goals selection
                VStack(alignment: .leading, spacing: 16) {
                    Text("What matters most to you?")
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        ForEach(LifeGoal.allCases, id: \.self) { goal in
                            GoalSelectionCard(
                                goal: goal,
                                isSelected: onboardingManager.selectedGoals.contains(goal)
                            ) {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    if onboardingManager.selectedGoals.contains(goal) {
                                        onboardingManager.selectedGoals.remove(goal)
                                    } else {
                                        onboardingManager.selectedGoals.insert(goal)
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, 40)
            .opacity(animateForm ? 1 : 0)
            .offset(y: animateForm ? 0 : 30)
            .animation(.easeOut(duration: 0.8).delay(0.3), value: animateForm)
            
            Spacer()
        }
        .onAppear {
            animateForm = true
        }
    }
}

// MARK: - Relationships Step

/// Relationships setup step
struct RelationshipsStepView: View {
    @ObservedObject var onboardingManager: OnboardingManager
    @State private var newPersonName = ""
    @State private var newPersonRelationship = "Friend"
    @State private var animateContent = false
    
    private let relationshipTypes = ["Family", "Friend", "Colleague", "Mentor", "Partner"]
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // Title
            VStack(spacing: 16) {
                Text("Your People")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("Add the people who matter most in your life")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 40)
            
            // Add person form
            VStack(spacing: 16) {
                HStack(spacing: 12) {
                    TextField("Person's name", text: $newPersonName)
                        .textFieldStyle(OnboardingTextFieldStyle())
                    
                    Picker("Relationship", selection: $newPersonRelationship) {
                        ForEach(relationshipTypes, id: \.self) { type in
                            Text(type).tag(type)
                        }
                    }
                    .pickerStyle(.menu)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(12)
                    
                    Button(action: addPerson) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                    .disabled(newPersonName.isEmpty)
                }
                .padding(.horizontal, 40)
            }
            
            // People list
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(onboardingManager.initialPeople) { person in
                        PersonRow(person: person) {
                            removePerson(person)
                        }
                    }
                }
                .padding(.horizontal, 40)
            }
            .frame(maxHeight: 200)
            
            // Suggestion
            if onboardingManager.initialPeople.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "person.crop.circle.badge.plus")
                        .font(.system(size: 50))
                        .foregroundColor(.blue.opacity(0.6))
                    
                    Text("Start by adding 3-5 important people")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.horizontal, 40)
            }
            
            Spacer()
        }
        .opacity(animateContent ? 1 : 0)
        .offset(y: animateContent ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: animateContent)
        .onAppear {
            animateContent = true
        }
    }
    
    private func addPerson() {
        guard !newPersonName.isEmpty else { return }
        
        let person = OnboardingPerson(
            name: newPersonName,
            relationship: newPersonRelationship,
            importance: 3
        )
        
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            onboardingManager.initialPeople.append(person)
        }
        
        newPersonName = ""
    }
    
    private func removePerson(_ person: OnboardingPerson) {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            onboardingManager.initialPeople.removeAll { $0.id == person.id }
        }
    }
}

// MARK: - Teams Step

/// Teams setup step
struct TeamsStepView: View {
    @ObservedObject var onboardingManager: OnboardingManager
    @State private var animateContent = false
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // Title
            VStack(spacing: 16) {
                Text("Your First Team")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("Create a team or project to collaborate on")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 40)
            
            // Team form
            VStack(spacing: 24) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Team/Project Name")
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    TextField("e.g., Family Vacation Planning", text: $onboardingManager.firstTeamName)
                        .textFieldStyle(OnboardingTextFieldStyle())
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Goal or Purpose")
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    TextField("What do you want to achieve together?", text: $onboardingManager.firstTeamGoal, axis: .vertical)
                        .textFieldStyle(OnboardingTextFieldStyle())
                        .lineLimit(3...6)
                }
                
                // Team member suggestions
                if !onboardingManager.initialPeople.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Add Team Members")
                            .font(.headline)
                            .fontWeight(.medium)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                            ForEach(onboardingManager.initialPeople.prefix(4)) { person in
                                PersonSuggestionCard(person: person)
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, 40)
            .opacity(animateContent ? 1 : 0)
            .offset(y: animateContent ? 0 : 30)
            .animation(.easeOut(duration: 0.8).delay(0.3), value: animateContent)
            
            Spacer()
        }
        .onAppear {
            animateContent = true
        }
    }
}

// MARK: - Timeline Step

/// Timeline setup step
struct TimelineStepView: View {
    @ObservedObject var onboardingManager: OnboardingManager
    @State private var animateContent = false
    
    var body: some View {
        VStack(spacing: 40) {
            Spacer()
            
            // Title
            VStack(spacing: 16) {
                Text("Your Timeline")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Text("Track your journey and celebrate achievements")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 40)
            
            // Timeline preview
            TimelinePreview(birthDate: onboardingManager.userBirthDate)
                .frame(height: 200)
                .padding(.horizontal, 40)
            
            // Completion message
            VStack(spacing: 16) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
                    .scaleEffect(animateContent ? 1.0 : 0.8)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.5), value: animateContent)
                
                Text("You're all set!")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Your personalized Keeps experience is ready. Start building meaningful connections and achieving your goals.")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 40)
            .opacity(animateContent ? 1 : 0)
            .offset(y: animateContent ? 0 : 30)
            .animation(.easeOut(duration: 0.8).delay(0.3), value: animateContent)
            
            Spacer()
        }
        .onAppear {
            animateContent = true
        }
    }
}

// MARK: - Supporting Views

struct OnboardingTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
    }
}

struct GoalSelectionCard: View {
    let goal: LifeGoal
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: goal.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : goal.color)
                
                Text(goal.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
                    .multilineTextAlignment(.center)
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .background(isSelected ? goal.color : Color.gray.opacity(0.1))
            .cornerRadius(12)
            .scaleEffect(isSelected ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct PersonRow: View {
    let person: OnboardingPerson
    let onRemove: () -> Void
    
    var body: some View {
        HStack {
            Circle()
                .fill(Color.blue.opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(String(person.name.prefix(1)).uppercased())
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text(person.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(person.relationship)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(action: onRemove) {
                Image(systemName: "minus.circle.fill")
                    .foregroundColor(.red)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
}

struct PersonSuggestionCard: View {
    let person: OnboardingPerson
    @State private var isSelected = false
    
    var body: some View {
        Button(action: { isSelected.toggle() }) {
            HStack(spacing: 8) {
                Circle()
                    .fill(Color.blue.opacity(0.2))
                    .frame(width: 30, height: 30)
                    .overlay(
                        Text(String(person.name.prefix(1)).uppercased())
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    )
                
                Text(person.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? Color.blue : Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct TimelinePreview: View {
    let birthDate: Date
    @State private var animateWeeks = false
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Your Life Timeline")
                .font(.headline)
                .fontWeight(.medium)
            
            // Simplified timeline visualization
            HStack(spacing: 2) {
                ForEach(0..<50, id: \.self) { index in
                    Rectangle()
                        .fill(index < 25 ? Color.blue : Color.gray.opacity(0.3))
                        .frame(width: 4, height: 20)
                        .scaleEffect(animateWeeks ? 1.0 : 0.8)
                        .animation(.spring(response: 0.4, dampingFraction: 0.8).delay(Double(index) * 0.02), value: animateWeeks)
                }
            }
            
            Text("~4000 weeks of life")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .onAppear {
            animateWeeks = true
        }
    }
}
