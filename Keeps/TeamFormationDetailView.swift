//
//  TeamFormationDetailView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI
import SwiftUIX

/// Detailed view for team formation suggestion with role assignments
struct TeamFormationDetailView: View {
    let suggestion: TeamFormationSuggestion
    let integrationManager: ProjectPeopleIntegrationManager
    let teamManager: TeamManager
    let onCreateTeam: (Team) -> Void
    
    @State private var teamName: String = ""
    @State private var teamDescription: String = ""
    @State private var selectedCategory: TeamCategory = .all
    @State private var location: String = ""
    @State private var roleAssignments: [RoleAssignmentRecommendation] = []
    @State private var showingRoleAssignment = false
    @State private var isCreatingTeam = false
    
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Team Details Form
                    teamDetailsSection
                    
                    // Team Members
                    teamMembersSection
                    
                    // Role Assignments
                    if !roleAssignments.isEmpty {
                        roleAssignmentsSection
                    }
                    
                    // Create Team Button
                    createTeamButton
                }
                .padding()
            }
            .navigationTitle("Team Formation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Generate Roles") {
                        generateRoleAssignments()
                    }
                    .disabled(teamName.isEmpty)
                }
            }
        }
        .onAppear {
            teamName = suggestion.name
            teamDescription = suggestion.reasoning
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: suggestion.type.icon)
                    .font(.system(size: 40))
                    .foregroundColor(suggestion.type.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(suggestion.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Match Score: \(Int(suggestion.score * 100))%")
                        .font(.subheadline)
                        .foregroundColor(suggestion.type.color)
                        .fontWeight(.semibold)
                }
                
                Spacer()
            }
            
            Text(suggestion.reasoning)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(suggestion.type.color.opacity(0.1))
        .cornerRadius(16)
    }
    
    private var teamDetailsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Team Details")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                TextField("Team Name", text: $teamName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                TextField("Description", text: $teamDescription, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
                
                Picker("Category", selection: $selectedCategory) {
                    ForEach(TeamCategory.allCases, id: \.self) { category in
                        Text(category.displayName)
                            .tag(category)
                    }
                }
                .pickerStyle(MenuPickerStyle())
                
                TextField("Location", text: $location)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var teamMembersSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Team Members")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(suggestion.members.count) members")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(suggestion.members, id: \.id) { member in
                    TeamMemberCard(person: member)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var roleAssignmentsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recommended Role Assignments")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVStack(spacing: 12) {
                ForEach(roleAssignments) { assignment in
                    RoleAssignmentCard(assignment: assignment)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
    
    private var createTeamButton: some View {
        Button {
            createTeam()
        } label: {
            HStack {
                if isCreatingTeam {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "plus.circle.fill")
                }
                
                Text(isCreatingTeam ? "Creating Team..." : "Create Team")
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                LinearGradient(
                    colors: [suggestion.type.color, suggestion.type.color.opacity(0.8)],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .foregroundColor(.white)
            .cornerRadius(12)
        }
        .disabled(teamName.isEmpty || isCreatingTeam)
        .opacity(teamName.isEmpty ? 0.6 : 1.0)
    }
    
    // MARK: - Actions
    
    private func generateRoleAssignments() {
        // Create a temporary team for role assignment generation
        let tempTeam = Team(
            name: teamName,
            description: teamDescription,
            category: selectedCategory,
            location: location,
            members: suggestion.members.map { person in
                TeamMember(
                    name: person.name,
                    role: person.role,
                    avatarImageName: person.avatarImageName
                )
            },
            isActive: true,
            projectStatus: .planning
        )
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            roleAssignments = integrationManager.generateRoleAssignments(
                for: tempTeam,
                projectType: .software // Default, could be made configurable
            )
        }
    }
    
    private func createTeam() {
        isCreatingTeam = true
        
        // Create the team
        teamManager.createTeam(
            name: teamName,
            description: teamDescription,
            category: selectedCategory,
            location: location
        )
        
        // Get the created team
        if let createdTeam = teamManager.teams.last {
            // Add members to the team
            for member in suggestion.members {
                let teamMember = TeamMember(
                    name: member.name,
                    role: member.role,
                    avatarImageName: member.avatarImageName
                )
                createdTeam.addMember(teamMember)
            }
            
            // Apply role assignments if available
            for assignment in roleAssignments {
                teamManager.assignMemberRole(
                    to: createdTeam,
                    memberId: assignment.personId,
                    role: assignment.recommendedRole.rawValue,
                    responsibilities: assignment.recommendedRole.keywords,
                    permissions: []
                )
            }
            
            // Save the team
            teamManager.saveTeams()
            
            // Call completion handler
            onCreateTeam(createdTeam)
        }
        
        isCreatingTeam = false
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
        
        dismiss()
    }
}

// MARK: - Team Member Card

struct TeamMemberCard: View {
    let person: Person
    
    var body: some View {
        VStack(spacing: 8) {
            Circle()
                .fill(Color.blue.opacity(0.2))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(String(person.name.prefix(1)))
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                )
            
            VStack(spacing: 2) {
                Text(person.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text(person.role)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            HStack(spacing: 4) {
                Image(systemName: "star.fill")
                    .font(.caption2)
                    .foregroundColor(.yellow)
                
                Text("4.8") // Placeholder for relationship strength
                    .font(.caption2)
                    .fontWeight(.medium)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Role Assignment Card

struct RoleAssignmentCard: View {
    let assignment: RoleAssignmentRecommendation
    
    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(Color.green.opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(String(assignment.personName.prefix(1)))
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(assignment.personName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text("\(Int(assignment.suitabilityScore * 100))%")
                        .font(.caption)
                        .fontWeight(.bold)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.green.opacity(0.2))
                        .foregroundColor(.green)
                        .cornerRadius(4)
                }
                
                Text(assignment.recommendedRole.rawValue)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.blue)
                
                Text(assignment.reasoning)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    TeamFormationDetailView(
        suggestion: TeamFormationSuggestion(
            id: UUID(),
            name: "Sample Team",
            members: [],
            score: 0.85,
            reasoning: "Great collaboration potential",
            type: .balanced
        ),
        integrationManager: ProjectPeopleIntegrationManager(
            context: PersistenceController.shared.container.viewContext,
            peopleManager: PeopleManager(),
            teamManager: TeamManager()
        ),
        teamManager: TeamManager(),
        onCreateTeam: { _ in }
    )
}
