//
//  TeamCardView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Individual team card component with real functionality
/// Now includes real-time updates, status indicators, and interactive elements
struct TeamCardView: View {
    @ObservedObject var team: Team
    let teamManager: TeamManager
    @State private var isPressed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Team category and status with enhanced animations
            HStack {
                Text(team.category.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(team.category.color)
                    .modifier(ShimmerEffect())

                Spacer()

                // Project status indicator with pulse animation
                HStack(spacing: 4) {
                    Circle()
                        .fill(team.projectStatus.color)
                        .frame(width: 8, height: 8)
                        .modifier(PulseEffect(isActive: team.isActive))

                    Text(team.projectStatus.rawValue)
                        .font(.caption)
                        .foregroundColor(team.projectStatus.color)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(team.projectStatus.color.opacity(0.1))
                        .modifier(GlowEffect(color: team.projectStatus.color, isActive: team.isActive))
                )
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            
            // Main content
            VStack(alignment: .leading, spacing: 12) {
                // Team name and description
                VStack(alignment: .leading, spacing: 4) {
                    Text(team.name)
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.primary)
                    
                    Text(team.description)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.secondary)
                }
                
                // Member avatars
                MemberAvatarsView(members: team.featuredMembers)
                
                // Team stats with real-time data
                TeamStatsView(team: team)

                // Quick actions if team is active
                if team.isActive {
                    QuickActionsView(team: team, teamManager: teamManager)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .modifier(CardBackgroundEffect(isPressed: isPressed))
                .shadow(
                    color: .black.opacity(isPressed ? 0.15 : 0.08),
                    radius: isPressed ? 12 : 8,
                    x: 0,
                    y: isPressed ? 6 : 4
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    LinearGradient(
                        colors: [Color(.systemGray5), Color(.systemGray6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
                .opacity(isPressed ? 0.5 : 1.0)
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .rotation3DEffect(
            .degrees(isPressed ? 2 : 0),
            axis: (x: 1, y: 0, z: 0),
            perspective: 0.5
        )
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
        .onLongPressGesture(minimumDuration: 0.1, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.spring(response: 0.2, dampingFraction: 0.9)) {
                isPressed = pressing
            }

            // Enhanced haptic feedback
            if pressing {
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
        }, perform: {})
    }
}

/// Component displaying member avatars in an overlapping layout
struct MemberAvatarsView: View {
    let members: [TeamMember]
    
    var body: some View {
        HStack(spacing: -8) {
            ForEach(Array(members.enumerated()), id: \.element.id) { index, member in
                MemberAvatarView(member: member)
                    .zIndex(Double(members.count - index))
            }
            
            // Show additional members count if more than 3
            if members.count > 3 {
                Text("+\(members.count - 3)")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.leading, 8)
            }
        }
    }
}

/// Individual member avatar with online status indicator
struct MemberAvatarView: View {
    let member: TeamMember
    
    var body: some View {
        ZStack(alignment: .bottomTrailing) {
            // Avatar circle with system person icon
            Circle()
                .fill(LinearGradient(
                    gradient: Gradient(colors: [
                        Color.blue.opacity(0.8),
                        Color.purple.opacity(0.8)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 44, height: 44)
                .overlay(
                    Image(systemName: "person.fill")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                )
                .overlay(
                    Circle()
                        .stroke(Color(.systemBackground), lineWidth: 2)
                )
            
            // Online status indicator
            if member.isOnline {
                Circle()
                    .fill(Color.green)
                    .frame(width: 12, height: 12)
                    .overlay(
                        Circle()
                            .stroke(Color(.systemBackground), lineWidth: 2)
                    )
                    .offset(x: 2, y: 2)
            }
        }
    }
}

/// Enhanced team statistics with real-time data
struct TeamStatsView: View {
    @ObservedObject var team: Team

    var body: some View {
        HStack(spacing: 16) {
            // Member count
            HStack(spacing: 4) {
                Image(systemName: "person.2.fill")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                Text("\(team.memberCount)")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }

            // Location
            HStack(spacing: 4) {
                Image(systemName: "location.fill")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                Text(team.location)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }

            Spacer()

            // Online status with real-time updates
            HStack(spacing: 4) {
                Circle()
                    .fill(team.onlineCount > 0 ? Color.green : Color.gray)
                    .frame(width: 8, height: 8)
                Text("\(team.onlineCount) Online")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.secondary)
            }
        }
    }
}

/// Quick actions for active teams
struct QuickActionsView: View {
    @ObservedObject var team: Team
    let teamManager: TeamManager

    var body: some View {
        HStack(spacing: 12) {
            // Quick meeting button
            Button(action: {
                teamManager.startQuickMeeting(for: team)
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "video")
                        .font(.caption)
                    Text("Meet")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.blue)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(6)
            }

            // Send message button
            Button(action: {
                teamManager.sendTeamAnnouncement("Quick update", to: team)
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "message")
                        .font(.caption)
                    Text("Message")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(.green)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.green.opacity(0.1))
                .cornerRadius(6)
            }

            Spacer()

            // Last activity indicator
            Text("Active \(formatLastActivity(team.lastActivity))")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }

    private func formatLastActivity(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - SwiftUIX Enhanced Effects

/// Shimmer effect for text elements
struct ShimmerEffect: ViewModifier {
    @State private var isAnimating = false

    func body(content: Content) -> some View {
        content
            .overlay(
                LinearGradient(
                    colors: [Color.clear, Color.white.opacity(0.3), Color.clear],
                    startPoint: .leading,
                    endPoint: .trailing
                )
                .rotationEffect(.degrees(30))
                .offset(x: isAnimating ? 200 : -200)
                .animation(
                    .linear(duration: 2)
                    .repeatForever(autoreverses: false),
                    value: isAnimating
                )
            )
            .onAppear {
                isAnimating = true
            }
            .mask(content)
    }
}

/// Pulse effect for status indicators
struct PulseEffect: ViewModifier {
    let isActive: Bool
    @State private var isPulsing = false

    func body(content: Content) -> some View {
        content
            .scaleEffect(isPulsing && isActive ? 1.2 : 1.0)
            .opacity(isPulsing && isActive ? 0.7 : 1.0)
            .animation(
                isActive ? .easeInOut(duration: 1).repeatForever(autoreverses: true) : .default,
                value: isPulsing
            )
            .onAppear {
                if isActive {
                    isPulsing = true
                }
            }
            .onChange(of: isActive) { _, newValue in
                isPulsing = newValue
            }
    }
}

/// Glow effect for backgrounds
struct GlowEffect: ViewModifier {
    let color: Color
    let isActive: Bool
    @State private var isGlowing = false

    func body(content: Content) -> some View {
        content
            .shadow(
                color: isActive && isGlowing ? color.opacity(0.6) : Color.clear,
                radius: isActive && isGlowing ? 8 : 0
            )
            .animation(
                isActive ? .easeInOut(duration: 2).repeatForever(autoreverses: true) : .default,
                value: isGlowing
            )
            .onAppear {
                if isActive {
                    isGlowing = true
                }
            }
            .onChange(of: isActive) { _, newValue in
                isGlowing = newValue
            }
    }
}

/// Card background effect with subtle gradient animation
struct CardBackgroundEffect: ViewModifier {
    let isPressed: Bool
    @State private var gradientOffset: CGFloat = 0

    func body(content: Content) -> some View {
        content
            .background(
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        Color(.systemBackground).opacity(0.95),
                        Color(.systemBackground)
                    ],
                    startPoint: UnitPoint(x: gradientOffset, y: 0),
                    endPoint: UnitPoint(x: gradientOffset + 0.3, y: 1)
                )
                .animation(
                    .linear(duration: 3)
                    .repeatForever(autoreverses: false),
                    value: gradientOffset
                )
            )
            .onAppear {
                gradientOffset = -0.3
                withAnimation {
                    gradientOffset = 1.3
                }
            }
    }
}

#Preview {
    let teamManager = TeamManager()
    let teams = SampleData.createSampleTeams()

    return VStack(spacing: 20) {
        TeamCardView(team: teams[0], teamManager: teamManager)
        TeamCardView(team: teams[1], teamManager: teamManager)
    }
    .padding()
    .background(Color(.systemGroupedBackground))
}
