//
//  EvolutionTimelineManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI
import Foundation
import Combine
import CoreData

/// Manages the Evolution Timeline data and business logic
/// Handles week entries, milestones, and timeline navigation
class EvolutionTimelineManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var weekEntries: [WeekEntry] = []
    @Published var milestones: [TimelineMilestone] = []
    @Published var currentZoomLevel: TimelineZoomLevel = .week
    @Published var selectedWeek: WeekEntry?
    @Published var birthDate: Date = Calendar.current.date(byAdding: .year, value: -25, to: Date()) ?? Date()
    @Published var currentWeekNumber: Int = 1
    @Published var isLoading = false
    @Published var searchText = ""
    @Published var selectedEmotionalTag: EmotionalTag?
    @Published var needsBirthDateSetup = true
    
    // MARK: - Private Properties

    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    private let dataKey = "EvolutionTimelineData"
    private let milestonesKey = "TimelineMilestones"
    private let birthDateKey = "UserBirthDate"

    // Core Data Integration
    private let context: NSManagedObjectContext
    private let timelineEntityManager: TimelineEntityManager
    private var hasCompletedMigration = false
    
    // MARK: - Initialization

    init(context: NSManagedObjectContext = PersistenceController.shared.container.viewContext) {
        self.context = context
        self.timelineEntityManager = TimelineEntityManager(context: context)

        loadData()
        setupCurrentWeek()
        generateDefaultMilestones()
        migrateToCoreDateIfNeeded()
    }
    
    // MARK: - Data Management
    
    /// Load timeline data from UserDefaults
    private func loadData() {
        // Load birth date
        if let birthDateData = userDefaults.data(forKey: birthDateKey),
           let decodedBirthDate = try? JSONDecoder().decode(Date.self, from: birthDateData) {
            birthDate = decodedBirthDate
            needsBirthDateSetup = false
        }
        
        // Load week entries
        if let weekData = userDefaults.data(forKey: dataKey),
           let decodedWeeks = try? JSONDecoder().decode([WeekEntry].self, from: weekData) {
            weekEntries = decodedWeeks
        }
        
        // Load milestones
        if let milestonesData = userDefaults.data(forKey: milestonesKey),
           let decodedMilestones = try? JSONDecoder().decode([TimelineMilestone].self, from: milestonesData) {
            milestones = decodedMilestones
        }
    }
    
    /// Save timeline data to UserDefaults or Core Data
    func saveData() {
        if hasCompletedMigration {
            // Save to Core Data
            saveToCoreData()
        } else {
            // Save to UserDefaults (legacy)
            saveToUserDefaults()
        }
    }

    /// Save timeline data to UserDefaults (legacy method)
    private func saveToUserDefaults() {
        // Save birth date
        if let birthDateData = try? JSONEncoder().encode(birthDate) {
            userDefaults.set(birthDateData, forKey: birthDateKey)
        }

        // Save week entries
        if let weekData = try? JSONEncoder().encode(weekEntries) {
            userDefaults.set(weekData, forKey: dataKey)
        }

        // Save milestones
        if let milestonesData = try? JSONEncoder().encode(milestones) {
            userDefaults.set(milestonesData, forKey: milestonesKey)
        }
    }

    /// Save timeline data to Core Data
    private func saveToCoreData() {
        do {
            try context.save()
        } catch {
            print("❌ Failed to save timeline data to Core Data: \(error)")
        }
    }
    
    /// Setup current week tracking
    private func setupCurrentWeek() {
        currentWeekNumber = TimelineConfiguration.weekNumber(from: Date(), birthDate: birthDate)
        
        // Create current week entry if it doesn't exist
        if !weekEntries.contains(where: { $0.weekNumber == currentWeekNumber }) {
            let currentWeekStart = TimelineConfiguration.startDate(for: currentWeekNumber, birthDate: birthDate)
            let newWeek = WeekEntry(weekNumber: currentWeekNumber, startDate: currentWeekStart)
            weekEntries.append(newWeek)
            saveData()
        }
    }
    
    // MARK: - Week Management
    
    /// Get a week entry for the specified week number (read-only, safe for computed properties)
    func getWeekEntry(for weekNumber: Int) -> WeekEntry {
        if let existingWeek = weekEntries.first(where: { $0.weekNumber == weekNumber }) {
            return existingWeek
        }

        // Return a temporary week entry without modifying the published array
        // This prevents state updates during view rendering
        return WeekEntry(weekNumber: weekNumber, birthDate: birthDate)
    }

    /// Create and store a week entry for the specified week number
    func createWeekEntry(for weekNumber: Int) -> WeekEntry {
        if let existingWeek = weekEntries.first(where: { $0.weekNumber == weekNumber }) {
            return existingWeek
        }

        // Use the new birth-date-based initializer for accurate date calculations
        let newWeek = WeekEntry(weekNumber: weekNumber, birthDate: birthDate)
        weekEntries.append(newWeek)
        weekEntries.sort { $0.weekNumber < $1.weekNumber }
        saveData()
        return newWeek
    }
    
    /// Update a week entry
    func updateWeekEntry(_ week: WeekEntry) {
        week.lastModified = Date()

        // Auto-update completion status based on content
        week.updateCompletionStatus()

        if let index = weekEntries.firstIndex(where: { $0.id == week.id }) {
            weekEntries[index] = week
        }
        saveData()
    }
    
    /// Delete a week entry
    func deleteWeekEntry(_ week: WeekEntry) {
        weekEntries.removeAll { $0.id == week.id }
        saveData()
    }
    
    /// Get current week entry
    var currentWeek: WeekEntry? {
        return weekEntries.first { $0.isCurrentWeek }
    }
    
    // MARK: - Filtering and Search
    
    /// Get filtered week entries based on search and emotional tag
    var filteredWeekEntries: [WeekEntry] {
        // Prevent infinite loops by checking if we're already in a filtering operation
        guard !isLoading else { return [] }

        var filtered = weekEntries

        // Filter by search text with safety checks
        if !searchText.isEmpty && searchText.count >= 2 { // Minimum 2 characters to search
            filtered = filtered.filter { week in
                // Use safe string operations to prevent crashes
                let titleMatch = week.title.localizedCaseInsensitiveContains(searchText)
                let insightMatch = week.insight.localizedCaseInsensitiveContains(searchText)
                let accomplishmentMatch = week.accomplishments.contains { accomplishment in
                    accomplishment.localizedCaseInsensitiveContains(searchText)
                }
                return titleMatch || insightMatch || accomplishmentMatch
            }
        }

        // Filter by emotional tag
        if let selectedTag = selectedEmotionalTag {
            filtered = filtered.filter { $0.emotionalTag == selectedTag }
        }

        // Limit results to prevent performance issues
        let maxResults = 100
        let sortedFiltered = filtered.sorted { $0.weekNumber < $1.weekNumber }
        return Array(sortedFiltered.prefix(maxResults))
    }
    
    // MARK: - Milestone Management
    
    /// Generate default milestones based on birth date
    private func generateDefaultMilestones() {
        guard milestones.isEmpty else { return }
        
        let calendar = Calendar.current
        
        // Birth milestone
        milestones.append(TimelineMilestone(
            title: "Birth",
            description: "The beginning of your journey",
            weekNumber: 1,
            category: .birth,
            isUserDefined: false
        ))
        
        // School milestones
        if let schoolStart = calendar.date(byAdding: .year, value: 6, to: birthDate) {
            let weekNumber = TimelineConfiguration.weekNumber(from: schoolStart, birthDate: birthDate)
            milestones.append(TimelineMilestone(
                title: "Started School",
                description: "Beginning of formal education",
                weekNumber: weekNumber,
                category: .education,
                isUserDefined: false
            ))
        }
        
        // High school graduation
        if let hsGraduation = calendar.date(byAdding: .year, value: 18, to: birthDate) {
            let weekNumber = TimelineConfiguration.weekNumber(from: hsGraduation, birthDate: birthDate)
            milestones.append(TimelineMilestone(
                title: "High School Graduation",
                description: "Completed secondary education",
                weekNumber: weekNumber,
                category: .education,
                isUserDefined: false
            ))
        }
        
        saveData()
    }
    
    /// Add a new milestone
    func addMilestone(_ milestone: TimelineMilestone) {
        milestones.append(milestone)
        milestones.sort { $0.weekNumber < $1.weekNumber }
        saveData()
    }
    
    /// Update a milestone
    func updateMilestone(_ milestone: TimelineMilestone) {
        if let index = milestones.firstIndex(where: { $0.id == milestone.id }) {
            milestones[index] = milestone
        }
        saveData()
    }
    
    /// Delete a milestone
    func deleteMilestone(_ milestone: TimelineMilestone) {
        milestones.removeAll { $0.id == milestone.id }
        saveData()
    }
    
    // MARK: - Analytics and Insights
    
    /// Get completion rate for past weeks
    var completionRate: Double {
        let pastWeeks = weekEntries.filter { $0.isPastWeek }
        guard !pastWeeks.isEmpty else { return 0 }
        let completedWeeks = pastWeeks.filter { $0.isCompleted }
        return Double(completedWeeks.count) / Double(pastWeeks.count)
    }
    
    /// Get most common emotional tag
    var dominantEmotionalTag: EmotionalTag? {
        let tags = weekEntries.map { $0.emotionalTag }
        let tagCounts = Dictionary(grouping: tags, by: { $0 })
        return tagCounts.max { $0.value.count < $1.value.count }?.key
    }
    
    /// Get streak of completed weeks
    var currentStreak: Int {
        let sortedWeeks = weekEntries.filter { $0.isPastWeek }.sorted { $0.weekNumber > $1.weekNumber }
        var streak = 0
        for week in sortedWeeks {
            if week.isCompleted {
                streak += 1
            } else {
                break
            }
        }
        return streak
    }
    
    // MARK: - Navigation Helpers
    
    /// Navigate to a specific week
    func navigateToWeek(_ weekNumber: Int) {
        selectedWeek = createWeekEntry(for: weekNumber)
    }
    
    /// Navigate to current week
    func navigateToCurrentWeek() {
        navigateToWeek(currentWeekNumber)
    }

    /// Get week number for a specific date
    func weekNumber(for date: Date) -> Int {
        return TimelineConfiguration.weekNumber(from: date, birthDate: birthDate)
    }

    /// Load timeline data (public method for view initialization)
    func loadTimelineData() {
        loadData()
        setupCurrentWeek()
    }

    /// Set user's birth date and recalculate timeline
    func setBirthDate(_ newBirthDate: Date) {
        birthDate = newBirthDate
        needsBirthDateSetup = false

        // Recalculate current week
        setupCurrentWeek()

        // Clear existing milestones and regenerate
        milestones.removeAll()
        generateDefaultMilestones()

        saveData()
    }
    
    /// Get weeks for a specific zoom level and range
    func getWeeksForZoomLevel(_ zoomLevel: TimelineZoomLevel, centerWeek: Int) -> [WeekEntry] {
        let range = getWeekRange(for: zoomLevel, centerWeek: centerWeek)
        return (range.lowerBound...range.upperBound).map { getWeekEntry(for: $0) }
    }

    // MARK: - Core Data Migration

    /// Migrate timeline data from UserDefaults to Core Data
    private func migrateToCoreDateIfNeeded() {
        let migrationKey = "TimelineCoreDateMigrationCompleted"

        guard !userDefaults.bool(forKey: migrationKey) else {
            hasCompletedMigration = true
            loadFromCoreData()
            return
        }

        print("🔄 Starting Timeline migration from UserDefaults to Core Data...")

        // Migrate existing week entries
        for weekEntry in weekEntries {
            _ = timelineEntityManager.createTimelineEntity(from: weekEntry)

            do {
                try context.save()
                print("✅ Migrated week \(weekEntry.weekNumber)")
            } catch {
                print("❌ Failed to migrate week \(weekEntry.weekNumber): \(error)")
            }
        }

        // Migrate user settings
        migrateUserSettings()

        // Mark migration as completed
        userDefaults.set(true, forKey: migrationKey)
        hasCompletedMigration = true

        print("🎉 Timeline migration completed successfully!")

        // Load from Core Data going forward
        loadFromCoreData()
    }

    /// Migrate user settings to Core Data
    private func migrateUserSettings() {
        let request: NSFetchRequest<UserSettingsEntity> = UserSettingsEntity.fetchRequest()

        do {
            let existingSettings = try context.fetch(request)
            let userSettings = existingSettings.first ?? UserSettingsEntity(context: context)

            userSettings.id = userSettings.id ?? UUID()
            userSettings.birthDate = birthDate
            userSettings.currentWeekNumber = Int32(currentWeekNumber)
            userSettings.needsBirthDateSetup = needsBirthDateSetup
            userSettings.createdDate = userSettings.createdDate ?? Date()
            userSettings.lastModified = Date()

            try context.save()
            print("✅ Migrated user settings")
        } catch {
            print("❌ Failed to migrate user settings: \(error)")
        }
    }

    /// Load timeline data from Core Data
    private func loadFromCoreData() {
        // Load user settings
        loadUserSettingsFromCoreData()

        // Load week entries
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \TimelineEntryEntity.weekNumber, ascending: true)]

        do {
            let entities = try context.fetch(request)
            weekEntries = entities.map { timelineEntityManager.createWeekEntry(from: $0) }
            print("✅ Loaded \(weekEntries.count) week entries from Core Data")
        } catch {
            print("❌ Failed to load week entries from Core Data: \(error)")
        }
    }

    /// Load user settings from Core Data
    private func loadUserSettingsFromCoreData() {
        let request: NSFetchRequest<UserSettingsEntity> = UserSettingsEntity.fetchRequest()

        do {
            let settings = try context.fetch(request)
            if let userSettings = settings.first {
                birthDate = userSettings.birthDate ?? birthDate
                currentWeekNumber = Int(userSettings.currentWeekNumber)
                needsBirthDateSetup = userSettings.needsBirthDateSetup
            }
        } catch {
            print("❌ Failed to load user settings from Core Data: \(error)")
        }
    }

    // MARK: - Enhanced Timeline Capabilities

    /// Add accomplishment to a week entry
    func addAccomplishment(to weekNumber: Int, title: String, description: String? = nil, category: AccomplishmentCategory, impact: ImpactLevel = .medium, linkedPeople: [UUID] = [], linkedTeams: [UUID] = []) {
        let weekEntry = createWeekEntry(for: weekNumber)
        timelineEntityManager.addAccomplishment(
            to: weekEntry.id,
            title: title,
            description: description,
            category: category,
            impact: impact,
            linkedPeople: linkedPeople,
            linkedTeams: linkedTeams
        )

        // Update the week entry in Core Data
        updateWeekEntryInCoreData(weekEntry)
    }

    /// Add reflection to a week entry
    func addReflection(to weekNumber: Int, content: String, type: ReflectionType, mood: EmotionalTag? = nil, learnings: [String] = [], gratitude: [String] = []) {
        let weekEntry = createWeekEntry(for: weekNumber)
        timelineEntityManager.addReflection(
            to: weekEntry.id,
            content: content,
            type: type,
            mood: mood,
            learnings: learnings,
            gratitude: gratitude
        )

        // Update the week entry in Core Data
        updateWeekEntryInCoreData(weekEntry)
    }

    /// Connect timeline entry to team milestone
    func connectToTeamMilestone(weekNumber: Int, teamId: UUID, milestoneId: UUID, connectionType: MilestoneConnectionType) {
        let weekEntry = createWeekEntry(for: weekNumber)
        timelineEntityManager.connectToTeamMilestone(
            weekEntryId: weekEntry.id,
            teamId: teamId,
            milestoneId: milestoneId,
            connectionType: connectionType
        )

        // Update the week entry in Core Data
        updateWeekEntryInCoreData(weekEntry)
    }

    /// Get accomplishments for a week
    func getAccomplishments(for weekNumber: Int) -> [TimelineAccomplishment] {
        let weekEntry = getWeekEntry(for: weekNumber)
        return timelineEntityManager.accomplishments[weekEntry.id] ?? []
    }

    /// Get reflections for a week
    func getReflections(for weekNumber: Int) -> [TimelineReflection] {
        let weekEntry = getWeekEntry(for: weekNumber)
        return timelineEntityManager.reflections[weekEntry.id] ?? []
    }

    /// Get streak data for a week
    func getStreakData(for weekNumber: Int) -> StreakData? {
        let weekEntry = getWeekEntry(for: weekNumber)
        return timelineEntityManager.streakData[weekEntry.id]
    }

    /// Get life phase for a week
    func getLifePhase(for weekNumber: Int) -> LifePhase? {
        return timelineEntityManager.getLifePhase(for: weekNumber, birthDate: birthDate)
    }

    /// Calculate accomplishment streak for a category
    func calculateAccomplishmentStreak(category: AccomplishmentCategory, endingWeek: Int) -> Int {
        return timelineEntityManager.calculateAccomplishmentStreak(category: category, endingWeek: endingWeek)
    }

    /// Get accomplishment distribution by category
    func getAccomplishmentDistribution() -> [AccomplishmentCategory: Int] {
        return timelineEntityManager.getAccomplishmentDistribution()
    }

    /// Get timeline entries for a specific person
    func getTimelineEntriesForPerson(_ personId: UUID) -> [WeekEntry] {
        let entities = timelineEntityManager.getTimelineEntriesForPerson(personId)
        return entities.map { timelineEntityManager.createWeekEntry(from: $0) }
    }

    /// Get timeline entries for a specific team
    func getTimelineEntriesForTeam(_ teamId: UUID) -> [WeekEntry] {
        let entities = timelineEntityManager.getTimelineEntriesForTeam(teamId)
        return entities.map { timelineEntityManager.createWeekEntry(from: $0) }
    }

    /// Update week entry in Core Data
    private func updateWeekEntryInCoreData(_ weekEntry: WeekEntry) {
        let request: NSFetchRequest<TimelineEntryEntity> = TimelineEntryEntity.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", weekEntry.id as CVarArg)

        do {
            let entities = try context.fetch(request)
            if let entity = entities.first {
                timelineEntityManager.updateTimelineEntity(entity, from: weekEntry)
                try context.save()
            }
        } catch {
            print("❌ Failed to update week entry in Core Data: \(error)")
        }
    }
    
    /// Get week range for zoom level
    private func getWeekRange(for zoomLevel: TimelineZoomLevel, centerWeek: Int) -> ClosedRange<Int> {
        let halfRange = zoomLevel.weeksPerUnit / 2
        let start = max(1, centerWeek - halfRange)
        let end = min(TimelineConfiguration.totalLifeWeeks, centerWeek + halfRange)
        return start...end
    }
}
