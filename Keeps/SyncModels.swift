//
//  SyncModels.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import Foundation
import SwiftUI
import Combine

// MARK: - Core Sync Models

/// Represents a synchronization operation
struct SyncOperation: Identifiable, Codable {
    let id = UUID()
    let entityType: EntityType
    let entityId: UUID
    let operationType: OperationType
    let data: Data
    let timestamp: Date
    let priority: SyncPriority
    var status: SyncStatus
    var retryCount: Int
    var lastAttempt: Date?
    var errorMessage: String?
    
    enum EntityType: String, CaseIterable, Codable {
        case person = "person"
        case team = "team"
        case weekEntry = "weekEntry"
        case accomplishment = "accomplishment"
        case reflection = "reflection"
        case milestone = "milestone"
        case relationship = "relationship"
    }
    
    enum OperationType: String, CaseIterable, Codable {
        case create = "create"
        case update = "update"
        case delete = "delete"
        case merge = "merge"
        case restore = "restore"
    }
    
    enum SyncPriority: Int, CaseIterable, Codable {
        case low = 1
        case medium = 2
        case high = 3
        case critical = 4
        
        var description: String {
            switch self {
            case .low: return "Low"
            case .medium: return "Medium"
            case .high: return "High"
            case .critical: return "Critical"
            }
        }
    }
    
    enum SyncStatus: String, CaseIterable, Codable {
        case pending = "pending"
        case inProgress = "inProgress"
        case completed = "completed"
        case failed = "failed"
        case conflicted = "conflicted"
        case cancelled = "cancelled"
        
        var color: Color {
            switch self {
            case .pending: return .orange
            case .inProgress: return .blue
            case .completed: return .green
            case .failed: return .red
            case .conflicted: return .purple
            case .cancelled: return .gray
            }
        }
        
        var icon: String {
            switch self {
            case .pending: return "clock"
            case .inProgress: return "arrow.clockwise"
            case .completed: return "checkmark.circle"
            case .failed: return "xmark.circle"
            case .conflicted: return "exclamationmark.triangle"
            case .cancelled: return "minus.circle"
            }
        }
    }
    
    init(entityType: EntityType, entityId: UUID, operationType: OperationType, data: Data, priority: SyncPriority = .medium) {
        self.entityType = entityType
        self.entityId = entityId
        self.operationType = operationType
        self.data = data
        self.timestamp = Date()
        self.priority = priority
        self.status = .pending
        self.retryCount = 0
    }
}

/// Represents a data conflict that needs resolution
struct DataConflict: Identifiable, Codable {
    let id = UUID()
    let entityType: SyncOperation.EntityType
    let entityId: UUID
    let localData: Data
    let remoteData: Data
    let localTimestamp: Date
    let remoteTimestamp: Date
    let conflictType: ConflictType
    let detectedAt: Date
    var resolutionStrategy: ResolutionStrategy?
    var isResolved: Bool
    
    enum ConflictType: String, CaseIterable, Codable {
        case concurrentEdit = "concurrentEdit"
        case deleteUpdate = "deleteUpdate"
        case duplicateCreate = "duplicateCreate"
        case versionMismatch = "versionMismatch"
        case dataCorruption = "dataCorruption"
        
        var description: String {
            switch self {
            case .concurrentEdit: return "Concurrent Edit"
            case .deleteUpdate: return "Delete vs Update"
            case .duplicateCreate: return "Duplicate Creation"
            case .versionMismatch: return "Version Mismatch"
            case .dataCorruption: return "Data Corruption"
            }
        }
    }
    
    enum ResolutionStrategy: String, CaseIterable, Codable {
        case useLocal = "useLocal"
        case useRemote = "useRemote"
        case merge = "merge"
        case createBoth = "createBoth"
        case manualReview = "manualReview"
        
        var description: String {
            switch self {
            case .useLocal: return "Use Local Version"
            case .useRemote: return "Use Remote Version"
            case .merge: return "Merge Changes"
            case .createBoth: return "Keep Both Versions"
            case .manualReview: return "Manual Review Required"
            }
        }
    }
    
    init(entityType: SyncOperation.EntityType, entityId: UUID, localData: Data, remoteData: Data, localTimestamp: Date, remoteTimestamp: Date, conflictType: ConflictType) {
        self.entityType = entityType
        self.entityId = entityId
        self.localData = localData
        self.remoteData = remoteData
        self.localTimestamp = localTimestamp
        self.remoteTimestamp = remoteTimestamp
        self.conflictType = conflictType
        self.detectedAt = Date()
        self.isResolved = false
    }
}

/// Represents the overall sync status
struct SyncStatus: Codable {
    var isOnline: Bool
    var lastSyncTime: Date?
    var pendingOperations: Int
    var failedOperations: Int
    var conflictedOperations: Int
    var totalOperationsToday: Int
    var syncProgress: Double // 0.0 to 1.0
    var estimatedTimeRemaining: TimeInterval?
    var currentOperation: String?
    
    init() {
        self.isOnline = true
        self.lastSyncTime = nil
        self.pendingOperations = 0
        self.failedOperations = 0
        self.conflictedOperations = 0
        self.totalOperationsToday = 0
        self.syncProgress = 0.0
        self.estimatedTimeRemaining = nil
        self.currentOperation = nil
    }
}

/// Represents sync analytics and metrics
struct SyncAnalytics: Codable {
    var totalSyncs: Int
    var successfulSyncs: Int
    var failedSyncs: Int
    var averageSyncTime: TimeInterval
    var dataTransferred: Int64 // bytes
    var conflictsResolved: Int
    var lastAnalyticsReset: Date
    var syncFrequency: [String: Int] // entity type -> count
    var peakSyncTimes: [Date] // times when sync activity is highest
    
    init() {
        self.totalSyncs = 0
        self.successfulSyncs = 0
        self.failedSyncs = 0
        self.averageSyncTime = 0
        self.dataTransferred = 0
        self.conflictsResolved = 0
        self.lastAnalyticsReset = Date()
        self.syncFrequency = [:]
        self.peakSyncTimes = []
    }
    
    var successRate: Double {
        guard totalSyncs > 0 else { return 0 }
        return Double(successfulSyncs) / Double(totalSyncs)
    }
    
    var failureRate: Double {
        guard totalSyncs > 0 else { return 0 }
        return Double(failedSyncs) / Double(totalSyncs)
    }
}

/// Represents a backup snapshot
struct BackupSnapshot: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let version: String
    let dataSize: Int64
    let entityCounts: [String: Int]
    let checksum: String
    let isAutomatic: Bool
    var isCorrupted: Bool
    
    init(version: String, dataSize: Int64, entityCounts: [String: Int], checksum: String, isAutomatic: Bool = true) {
        self.timestamp = Date()
        self.version = version
        self.dataSize = dataSize
        self.entityCounts = entityCounts
        self.checksum = checksum
        self.isAutomatic = isAutomatic
        self.isCorrupted = false
    }
    
    var formattedSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: dataSize)
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }
}

/// Represents sync configuration settings
struct SyncConfiguration: Codable {
    var isAutoSyncEnabled: Bool
    var syncInterval: TimeInterval // seconds
    var maxRetryAttempts: Int
    var batchSize: Int
    var compressionEnabled: Bool
    var encryptionEnabled: Bool
    var conflictResolutionStrategy: DataConflict.ResolutionStrategy
    var backupFrequency: BackupFrequency
    var maxBackupCount: Int
    var syncOnlyOnWiFi: Bool
    var lowPowerModeSync: Bool
    
    enum BackupFrequency: String, CaseIterable, Codable {
        case never = "never"
        case daily = "daily"
        case weekly = "weekly"
        case monthly = "monthly"
        case onSync = "onSync"
        
        var description: String {
            switch self {
            case .never: return "Never"
            case .daily: return "Daily"
            case .weekly: return "Weekly"
            case .monthly: return "Monthly"
            case .onSync: return "On Every Sync"
            }
        }
        
        var interval: TimeInterval? {
            switch self {
            case .never: return nil
            case .daily: return 86400 // 24 hours
            case .weekly: return 604800 // 7 days
            case .monthly: return 2592000 // 30 days
            case .onSync: return 0 // immediate
            }
        }
    }
    
    static let `default` = SyncConfiguration(
        isAutoSyncEnabled: true,
        syncInterval: 300, // 5 minutes
        maxRetryAttempts: 3,
        batchSize: 50,
        compressionEnabled: true,
        encryptionEnabled: true,
        conflictResolutionStrategy: .merge,
        backupFrequency: .daily,
        maxBackupCount: 10,
        syncOnlyOnWiFi: false,
        lowPowerModeSync: true
    )
}
