//
//  EmotionalDemoMode.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Demo mode to showcase all emotional features
/// Perfect for demonstrating the industry-leading contacts experience
struct EmotionalDemoMode: View {
    @State private var currentDemo = 0
    @State private var isPlaying = false
    @State private var celebrationTriggered = false
    
    let demoSteps = [
        "Heartbeat Animation for Favorites",
        "Breathing Effects for Online Contacts",
        "Connection Web Visualization",
        "Emotional Haptic Patterns",
        "Particle Celebration System",
        "Memory Timeline Experience",
        "Relationship Strength Visualization",
        "Complete Emotional Journey"
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Demo header
                VStack(spacing: 16) {
                    Text("🎯 Emotional Contacts Demo")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)
                    
                    Text("Experience the industry's most emotionally intelligent contact system")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                
                // Current demo step
                VStack(spacing: 20) {
                    Text("Step \(currentDemo + 1) of \(demoSteps.count)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(demoSteps[currentDemo])
                        .font(.title2)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    // Demo visualization
                    DemoVisualizationView(step: currentDemo, celebrationTriggered: $celebrationTriggered)
                        .frame(height: 200)
                }
                
                Spacer()
                
                // Demo controls
                VStack(spacing: 20) {
                    // Progress indicator
                    HStack(spacing: 8) {
                        ForEach(0..<demoSteps.count, id: \.self) { index in
                            Circle()
                                .fill(index <= currentDemo ? Color.blue : Color.gray.opacity(0.3))
                                .frame(width: 8, height: 8)
                                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: currentDemo)
                        }
                    }
                    
                    // Control buttons
                    HStack(spacing: 20) {
                        Button("Previous") {
                            if currentDemo > 0 {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentDemo -= 1
                                }
                                triggerDemoStep()
                            }
                        }
                        .disabled(currentDemo == 0)
                        
                        Button(isPlaying ? "Pause" : "Play Demo") {
                            isPlaying.toggle()
                            if isPlaying {
                                startAutoDemo()
                            }
                        }
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(isPlaying ? Color.red : Color.blue)
                        )
                        
                        Button("Next") {
                            if currentDemo < demoSteps.count - 1 {
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    currentDemo += 1
                                }
                                triggerDemoStep()
                            }
                        }
                        .disabled(currentDemo == demoSteps.count - 1)
                    }
                    
                    // Reset button
                    Button("Reset Demo") {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            currentDemo = 0
                            isPlaying = false
                        }
                    }
                    .foregroundColor(.secondary)
                }
                .padding(.bottom, 30)
            }
            .padding()
            .navigationBarHidden(true)
        }
        .celebrationBurst(isTriggered: $celebrationTriggered)
    }
    
    private func startAutoDemo() {
        guard isPlaying else { return }
        
        Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { timer in
            if !isPlaying {
                timer.invalidate()
                return
            }
            
            if currentDemo < demoSteps.count - 1 {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    currentDemo += 1
                }
                triggerDemoStep()
            } else {
                isPlaying = false
                timer.invalidate()
            }
        }
    }
    
    private func triggerDemoStep() {
        let haptics = EmotionalHapticsManager.shared
        
        switch currentDemo {
        case 0: // Heartbeat
            haptics.playHeartbeatPattern()
        case 1: // Breathing
            haptics.playBreathingPattern()
        case 2: // Connection Web
            haptics.playConnectionEstablished()
        case 3: // Haptic Patterns
            haptics.playRelationshipFeedback(for: .friend)
        case 4: // Particles
            celebrationTriggered = true
            haptics.playCelebrationPattern()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                celebrationTriggered = false
            }
        case 5: // Memory Timeline
            haptics.playSpecialMomentPattern()
        case 6: // Relationship Strength
            haptics.playConnectionPulse(for: .daily)
        case 7: // Complete Journey
            haptics.playCelebrationPattern()
            celebrationTriggered = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                celebrationTriggered = false
            }
        default:
            break
        }
    }
}

/// Visual demonstration of each emotional feature
struct DemoVisualizationView: View {
    let step: Int
    @Binding var celebrationTriggered: Bool
    @State private var animationTrigger = false
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 10)
            
            switch step {
            case 0: // Heartbeat Animation
                HeartbeatDemoView()
            case 1: // Breathing Effects
                BreathingDemoView()
            case 2: // Connection Web
                ConnectionWebDemoView()
            case 3: // Haptic Patterns
                HapticPatternsDemoView()
            case 4: // Particle System
                ParticleSystemDemoView(celebrationTriggered: $celebrationTriggered)
            case 5: // Memory Timeline
                MemoryTimelineDemoView()
            case 6: // Relationship Strength
                RelationshipStrengthDemoView()
            case 7: // Complete Journey
                CompleteJourneyDemoView()
            default:
                Text("Demo Step \(step)")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
        }
        .onChange(of: step) { _, _ in
            animationTrigger.toggle()
        }
    }
}

// Individual demo views for each feature
struct HeartbeatDemoView: View {
    var body: some View {
        VStack(spacing: 16) {
            Circle()
                .fill(
                    RadialGradient(
                        colors: [Color.red.opacity(0.8), Color.pink.opacity(0.4)],
                        center: .center,
                        startRadius: 10,
                        endRadius: 40
                    )
                )
                .frame(width: 80, height: 80)
                .modifier(HeartbeatAnimation(isFavorite: true))
            
            Text("❤️ Heartbeat for Favorites")
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct BreathingDemoView: View {
    var body: some View {
        VStack(spacing: 16) {
            Circle()
                .fill(
                    LinearGradient(
                        colors: [Color.green.opacity(0.8), Color.blue.opacity(0.6)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 80, height: 80)
                .modifier(BreathingAnimation(isOnline: true))
            
            Text("🌬️ Breathing for Online Status")
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct ConnectionWebDemoView: View {
    var body: some View {
        VStack(spacing: 16) {
            Circle()
                .fill(Color.blue.opacity(0.8))
                .frame(width: 80, height: 80)
                .modifier(ConnectionWebEffect(relationshipType: .friend, isActive: true))
            
            Text("🕸️ Connection Networks")
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct HapticPatternsDemoView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "iphone.radiowaves.left.and.right")
                .font(.system(size: 50))
                .foregroundColor(.purple)
            
            Text("📳 Emotional Haptic Patterns")
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct ParticleSystemDemoView: View {
    @Binding var celebrationTriggered: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            Circle()
                .fill(Color.yellow.opacity(0.8))
                .frame(width: 80, height: 80)
                .particleEffect(.celebration)
            
            Text("✨ Celebration Particles")
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct MemoryTimelineDemoView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "timeline.selection")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text("📖 Memory Timeline")
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct RelationshipStrengthDemoView: View {
    var body: some View {
        VStack(spacing: 16) {
            HStack(spacing: 4) {
                ForEach(0..<5, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(index < 4 ? Color.green : Color.gray.opacity(0.3))
                        .frame(width: 8, height: 20 + CGFloat(index * 4))
                        .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(Double(index) * 0.1), value: index)
                }
            }
            
            Text("📊 Relationship Strength")
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct CompleteJourneyDemoView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "heart.circle.fill")
                .font(.system(size: 50))
                .foregroundColor(.red)
                .particleEffect(.celebration)
            
            Text("🎯 Complete Emotional Journey")
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct EmotionalDemoMode_Previews: PreviewProvider {
    static var previews: some View {
        EmotionalDemoMode()
    }
}
