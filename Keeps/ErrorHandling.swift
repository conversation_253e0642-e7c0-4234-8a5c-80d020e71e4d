//
//  ErrorHandling.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI

// MARK: - Error Types

/// Comprehensive error handling for the app
enum KeepsError: LocalizedError, Identifiable {
    case networkError(String)
    case dataCorruption(String)
    case permissionDenied(String)
    case storageError(String)
    case validationError(String)
    case unknownError(String)
    
    var id: String {
        switch self {
        case .networkError(let message): return "network_\(message)"
        case .dataCorruption(let message): return "data_\(message)"
        case .permissionDenied(let message): return "permission_\(message)"
        case .storageError(let message): return "storage_\(message)"
        case .validationError(let message): return "validation_\(message)"
        case .unknownError(let message): return "unknown_\(message)"
        }
    }
    
    var errorDescription: String? {
        switch self {
        case .networkError(let message):
            return "Network Error: \(message)"
        case .dataCorruption(let message):
            return "Data Error: \(message)"
        case .permissionDenied(let message):
            return "Permission Error: \(message)"
        case .storageError(let message):
            return "Storage Error: \(message)"
        case .validationError(let message):
            return "Validation Error: \(message)"
        case .unknownError(let message):
            return "Unknown Error: \(message)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .networkError:
            return "Please check your internet connection and try again."
        case .dataCorruption:
            return "The data may be corrupted. Try refreshing or contact support."
        case .permissionDenied:
            return "Please grant the necessary permissions in Settings."
        case .storageError:
            return "Check available storage space and try again."
        case .validationError:
            return "Please check your input and try again."
        case .unknownError:
            return "Please try again or contact support if the problem persists."
        }
    }
    
    var icon: String {
        switch self {
        case .networkError: return "wifi.exclamationmark"
        case .dataCorruption: return "exclamationmark.triangle"
        case .permissionDenied: return "lock.shield"
        case .storageError: return "externaldrive.badge.exclamationmark"
        case .validationError: return "exclamationmark.circle"
        case .unknownError: return "questionmark.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .networkError: return .orange
        case .dataCorruption: return .red
        case .permissionDenied: return .purple
        case .storageError: return .blue
        case .validationError: return .yellow
        case .unknownError: return .gray
        }
    }
}

// MARK: - Error Manager

/// Centralized error handling and reporting
class ErrorManager: ObservableObject {
    static let shared = ErrorManager()
    
    @Published var currentError: KeepsError?
    @Published var errorHistory: [KeepsError] = []
    @Published var isShowingError = false
    
    private init() {}
    
    /// Handle and display error
    func handle(_ error: KeepsError, shouldDisplay: Bool = true) {
        DispatchQueue.main.async {
            self.errorHistory.append(error)
            
            if shouldDisplay {
                self.currentError = error
                self.isShowingError = true
            }
            
            // Log error for debugging
            print("🚨 Error: \(error.errorDescription ?? "Unknown")")
            
            // Send to analytics/crash reporting in production
            self.logError(error)
        }
    }
    
    /// Clear current error
    func clearError() {
        currentError = nil
        isShowingError = false
    }
    
    /// Retry mechanism for failed operations
    func retry(operation: @escaping () async throws -> Void) {
        Task { @MainActor in
            do {
                try await operation()
                self.clearError()
            } catch {
                self.handle(self.mapError(error))
            }
        }
    }
    
    private func logError(_ error: KeepsError) {
        // In production, send to analytics service
        // For now, just log to console
        let errorData = [
            "type": String(describing: error),
            "description": error.errorDescription ?? "",
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        print("Error logged: \(errorData)")
    }
    
    private func mapError(_ error: Error) -> KeepsError {
        if let keepsError = error as? KeepsError {
            return keepsError
        }
        
        // Map common system errors
        if let urlError = error as? URLError {
            return .networkError(urlError.localizedDescription)
        }
        
        return .unknownError(error.localizedDescription)
    }
}

// MARK: - Loading States

/// Loading state management
enum LoadingState {
    case idle
    case loading
    case success
    case failure(KeepsError)

    var isLoading: Bool {
        if case .loading = self { return true }
        return false
    }

    var error: KeepsError? {
        if case .failure(let error) = self { return error }
        return nil
    }
}

/// Loading state manager
class LoadingManager: ObservableObject {
    @Published var states: [String: LoadingState] = [:]
    
    func setState(_ state: LoadingState, for key: String) {
        DispatchQueue.main.async {
            self.states[key] = state
        }
    }
    
    func getState(for key: String) -> LoadingState {
        return states[key] ?? .idle
    }
    
    func isLoading(_ key: String) -> Bool {
        return getState(for: key).isLoading
    }
    
    func clearState(for key: String) {
        states.removeValue(forKey: key)
    }
}

// MARK: - Error Display Components

/// Enhanced error view with recovery options
struct ErrorView: View {
    let error: KeepsError
    let onRetry: (() -> Void)?
    let onDismiss: () -> Void
    
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    
    var body: some View {
        VStack(spacing: 20) {
            // Error icon
            Image(systemName: error.icon)
                .font(.system(size: 48, weight: .medium))
                .foregroundColor(error.color)
                .accessibilityHidden(true)
            
            // Error message
            VStack(spacing: 8) {
                Text("Oops! Something went wrong")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                
                Text(error.errorDescription ?? "Unknown error occurred")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                if let suggestion = error.recoverySuggestion {
                    Text(suggestion)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.top, 4)
                }
            }
            .dynamicTypeSupport()
            
            // Action buttons
            HStack(spacing: 12) {
                if let onRetry = onRetry {
                    AccessibleButton(
                        action: onRetry,
                        accessibilityLabel: "Retry",
                        accessibilityHint: "Attempts to perform the failed operation again"
                    ) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("Retry")
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(error.color)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
                
                AccessibleButton(
                    action: onDismiss,
                    accessibilityLabel: "Dismiss",
                    accessibilityHint: "Closes this error message"
                ) {
                    HStack {
                        Image(systemName: "xmark")
                        Text("Dismiss")
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color(.systemGray4))
                    .foregroundColor(.primary)
                    .cornerRadius(8)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Error: \(error.errorDescription ?? "Unknown error")")
        .accessibilityAnnouncement("An error occurred: \(error.errorDescription ?? "Unknown error")")
    }
}

/// Loading view with accessibility support
struct LoadingView: View {
    let message: String
    let showProgress: Bool
    let progress: Double?
    
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    
    init(message: String = "Loading...", showProgress: Bool = false, progress: Double? = nil) {
        self.message = message
        self.showProgress = showProgress
        self.progress = progress
    }
    
    var body: some View {
        VStack(spacing: 16) {
            if showProgress, let progress = progress {
                AccessibleProgressView(
                    progress: progress,
                    accessibilityLabel: "Loading progress"
                )
                .frame(width: 200)
            } else {
                ProgressView()
                    .scaleEffect(1.2)
                    .accessibilityLabel("Loading indicator")
            }
            
            Text(message)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .dynamicTypeSupport()
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel(message)
        .accessibilityAddTraits(.updatesFrequently)
    }
}

/// Empty state view
struct EmptyStateView: View {
    let icon: String
    let title: String
    let message: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: icon)
                .font(.system(size: 48, weight: .medium))
                .foregroundColor(.secondary)
                .accessibilityHidden(true)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                
                Text(message)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .dynamicTypeSupport()
            
            if let actionTitle = actionTitle, let action = action {
                AccessibleButton(
                    action: action,
                    accessibilityLabel: actionTitle,
                    accessibilityHint: "Performs the suggested action"
                ) {
                    Text(actionTitle)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
            }
        }
        .padding(24)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(title). \(message)")
    }
}

// MARK: - View Extensions

extension View {
    /// Add error handling overlay
    func errorHandling(
        error: Binding<KeepsError?>,
        onRetry: (() -> Void)? = nil
    ) -> some View {
        self.overlay {
            if let currentError = error.wrappedValue {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .overlay {
                        ErrorView(
                            error: currentError,
                            onRetry: onRetry,
                            onDismiss: {
                                error.wrappedValue = nil
                            }
                        )
                    }
                    .transition(.opacity.combined(with: .scale))
            }
        }
    }
    
    /// Add loading state overlay
    func loadingOverlay(
        isLoading: Bool,
        message: String = "Loading...",
        showProgress: Bool = false,
        progress: Double? = nil
    ) -> some View {
        self.overlay {
            if isLoading {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .overlay {
                        LoadingView(
                            message: message,
                            showProgress: showProgress,
                            progress: progress
                        )
                    }
                    .transition(.opacity)
            }
        }
    }
}
