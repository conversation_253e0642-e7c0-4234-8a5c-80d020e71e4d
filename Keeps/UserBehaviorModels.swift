//
//  UserBehaviorModels.swift
//  Keeps
//
//  Comprehensive models for user behavior analysis and pattern recognition
//  Supporting the Smart Suggestions Engine with detailed behavioral data
//

import Foundation
import SwiftUI

// MARK: - User Behavior Patterns

/// Comprehensive user behavior patterns for AI analysis
struct UserBehaviorPatterns {
    
    // MARK: - Interaction Patterns
    
    var dailyInteractionCount: Int = 0
    var peakUsageHours: [Int] = []
    var preferredSections: [SectionUsage] = []
    var sessionDuration: TimeInterval = 0
    var lastInteractionTime: Date = Date()
    
    // MARK: - Workflow Patterns
    
    var commonWorkflows: [WorkflowPattern] = []
    var workflowCompletionRate: Double = 0.0
    var averageWorkflowDuration: TimeInterval = 0.0
    var workflowPreferences: [WorkflowType: Double] = [:]
    
    // MARK: - Social Patterns
    
    var frequentCollaborators: [CollaboratorPattern] = []
    var teamFormationPatterns: [TeamPattern] = []
    var communicationFrequency: CommunicationPattern = CommunicationPattern()
    var networkSize: Int = 0
    
    // MARK: - Goal Patterns
    
    var goalCategories: [BehaviorGoalCategory] = []
    var goalCompletionRate: Double = 0.0
    var goalTimingPatterns: [TimingPattern] = []
    var motivationFactors: [String] = []
    
    // MARK: - Productivity Patterns
    
    var productiveHours: [Int] = []
    var focusSessionDuration: TimeInterval = 0
    var multitaskingTendency: Double = 0.0
    var procrastinationIndicators: [String] = []
    
    // MARK: - Learning Patterns
    
    var learningVelocity: Double = 0.0
    var featureAdoptionRate: Double = 0.0
    var helpSeekingBehavior: HelpSeekingPattern = HelpSeekingPattern()
    var expertiseAreas: [String] = []
}

// MARK: - Section Usage

/// Usage patterns for different app sections
struct SectionUsage {
    let section: AppSection
    var frequency: Int
    var averageDuration: TimeInterval
    var lastAccessed: Date = Date()
    var engagementScore: Double = 0.0
    
    var usageIntensity: UsageIntensity {
        switch frequency {
        case 0...5: return .low
        case 6...15: return .medium
        case 16...30: return .high
        default: return .veryHigh
        }
    }
}

enum UsageIntensity: String, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case veryHigh = "Very High"
    
    var color: Color {
        switch self {
        case .low: return .gray
        case .medium: return .blue
        case .high: return .orange
        case .veryHigh: return .red
        }
    }
}

// MARK: - Workflow Patterns

/// Patterns related to workflow usage and completion
struct WorkflowPattern {
    let workflowType: WorkflowType
    var frequency: Int
    var averageCompletionTime: TimeInterval
    var successRate: Double
    var lastUsed: Date = Date()
    var preferredSteps: [String] = []
    var skipPatterns: [String] = []
    
    var efficiency: WorkflowEfficiency {
        switch successRate {
        case 0.0...0.5: return .poor
        case 0.5...0.7: return .fair
        case 0.7...0.9: return .good
        default: return .excellent
        }
    }
}

enum WorkflowEfficiency: String, CaseIterable {
    case poor = "Poor"
    case fair = "Fair"
    case good = "Good"
    case excellent = "Excellent"
    
    var color: Color {
        switch self {
        case .poor: return .red
        case .fair: return .orange
        case .good: return .blue
        case .excellent: return .green
        }
    }
}

// MARK: - Collaborator Patterns

/// Patterns related to collaboration and relationships
struct CollaboratorPattern {
    let personId: UUID
    var interactionCount: Int
    var lastInteraction: Date
    var relationshipStrength: Double // 0.0 to 1.0
    var collaborationTypes: [String] = []
    var communicationFrequency: Double = 0.0
    var sharedGoals: Int = 0
    
    var relationshipLevel: RelationshipLevel {
        switch relationshipStrength {
        case 0.0...0.3: return .acquaintance
        case 0.3...0.6: return .colleague
        case 0.6...0.8: return .friend
        default: return .closeCollaborator
        }
    }
}

enum RelationshipLevel: String, CaseIterable {
    case acquaintance = "Acquaintance"
    case colleague = "Colleague"
    case friend = "Friend"
    case closeCollaborator = "Close Collaborator"
    
    var color: Color {
        switch self {
        case .acquaintance: return .gray
        case .colleague: return .blue
        case .friend: return .green
        case .closeCollaborator: return .purple
        }
    }
}

// MARK: - Team Patterns

/// Patterns related to team formation and dynamics
struct TeamPattern {
    let teamId: UUID
    let creationDate: Date
    var memberCount: Int
    var activityLevel: Double // 0.0 to 1.0
    var collaborationScore: Double = 0.0
    var projectSuccessRate: Double = 0.0
    var communicationStyle: String = ""
    
    var teamHealth: TeamHealth {
        let healthScore = (activityLevel + collaborationScore + projectSuccessRate) / 3.0
        switch healthScore {
        case 0.0...0.4: return .struggling
        case 0.4...0.6: return .developing
        case 0.6...0.8: return .performing
        default: return .highPerforming
        }
    }
}

enum TeamHealth: String, CaseIterable {
    case struggling = "Struggling"
    case developing = "Developing"
    case performing = "Performing"
    case highPerforming = "High Performing"
    
    var color: Color {
        switch self {
        case .struggling: return .red
        case .developing: return .orange
        case .performing: return .blue
        case .highPerforming: return .green
        }
    }
}

// MARK: - Communication Patterns

/// Patterns related to communication behavior
struct CommunicationPattern {
    var dailyAverage: Double = 0.0
    var peakDays: [Int] = []
    var preferredMethods: [String] = []
    var responseTime: TimeInterval = 0.0
    var initiationRate: Double = 0.0 // How often user initiates vs responds
    var groupVsIndividual: Double = 0.5 // 0.0 = all individual, 1.0 = all group
    
    var communicationStyle: CommunicationStyle {
        switch initiationRate {
        case 0.0...0.3: return .reactive
        case 0.3...0.7: return .balanced
        default: return .proactive
        }
    }
}

enum CommunicationStyle: String, CaseIterable {
    case reactive = "Reactive"
    case balanced = "Balanced"
    case proactive = "Proactive"
    
    var description: String {
        switch self {
        case .reactive: return "Tends to respond rather than initiate"
        case .balanced: return "Good balance of initiating and responding"
        case .proactive: return "Often initiates conversations and connections"
        }
    }
}

// MARK: - Goal Categories

/// Categories and patterns for goal setting
struct BehaviorGoalCategory {
    let name: String
    var frequency: Int
    var completionRate: Double
    var averageTimeToComplete: TimeInterval = 0.0
    var difficultyLevel: Double = 0.5 // 0.0 = easy, 1.0 = very difficult
    var motivationLevel: Double = 0.5 // 0.0 = low motivation, 1.0 = high motivation
    
    var categoryType: GoalCategoryType {
        switch name.lowercased() {
        case let n where n.contains("work") || n.contains("career"): return .professional
        case let n where n.contains("health") || n.contains("fitness"): return .health
        case let n where n.contains("learn") || n.contains("skill"): return .learning
        case let n where n.contains("social") || n.contains("relationship"): return .social
        case let n where n.contains("creative") || n.contains("art"): return .creative
        default: return .personal
        }
    }
}

enum GoalCategoryType: String, CaseIterable {
    case professional = "Professional"
    case health = "Health"
    case learning = "Learning"
    case social = "Social"
    case creative = "Creative"
    case personal = "Personal"
    
    var icon: String {
        switch self {
        case .professional: return "briefcase.fill"
        case .health: return "heart.fill"
        case .learning: return "book.fill"
        case .social: return "person.2.fill"
        case .creative: return "paintbrush.fill"
        case .personal: return "person.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .professional: return .blue
        case .health: return .red
        case .learning: return .green
        case .social: return .orange
        case .creative: return .purple
        case .personal: return .indigo
        }
    }
}

// MARK: - Timing Patterns

/// Patterns related to timing and scheduling
struct TimingPattern {
    let type: TimingType
    let value: Int // Hour (0-23) or Day (1-7)
    var frequency: Int
    var efficiency: Double = 0.0 // How productive during this time
    var preference: Double = 0.0 // How much user prefers this time
}

enum TimingType: String, CaseIterable {
    case hourly = "Hourly"
    case daily = "Daily"
    case weekly = "Weekly"
    case monthly = "Monthly"
    
    var description: String {
        switch self {
        case .hourly: return "Hour of day patterns"
        case .daily: return "Day of week patterns"
        case .weekly: return "Week of month patterns"
        case .monthly: return "Month of year patterns"
        }
    }
}

// MARK: - Help Seeking Patterns

/// Patterns related to how users seek help and learn
struct HelpSeekingPattern {
    var helpRequestFrequency: Double = 0.0
    var preferredHelpTypes: [HelpType] = []
    var selfSufficiencyLevel: Double = 0.5 // 0.0 = always needs help, 1.0 = very independent
    var learningStyle: LearningStyle = .visual
    var documentationUsage: Double = 0.0
}

enum HelpType: String, CaseIterable, Codable {
    case tutorial = "Tutorial"
    case documentation = "Documentation"
    case examples = "Examples"
    case support = "Support"
    case community = "Community"
    
    var icon: String {
        switch self {
        case .tutorial: return "play.circle.fill"
        case .documentation: return "doc.text.fill"
        case .examples: return "lightbulb.fill"
        case .support: return "questionmark.circle.fill"
        case .community: return "person.3.fill"
        }
    }
}

enum LearningStyle: String, CaseIterable {
    case visual = "Visual"
    case auditory = "Auditory"
    case kinesthetic = "Kinesthetic"
    case reading = "Reading"
    
    var description: String {
        switch self {
        case .visual: return "Learns best through visual aids and demonstrations"
        case .auditory: return "Learns best through listening and discussion"
        case .kinesthetic: return "Learns best through hands-on practice"
        case .reading: return "Learns best through reading and written instructions"
        }
    }
}

// MARK: - User Interaction Models

/// Represents a single user interaction for analysis
struct UserInteraction {
    let id = UUID()
    let timestamp: Date
    let type: BehaviorInteractionType
    let section: AppSection
    let duration: TimeInterval
    let context: [String: Any]
    
    init(type: BehaviorInteractionType, section: AppSection, duration: TimeInterval = 0, context: [String: Any] = [:]) {
        self.timestamp = Date()
        self.type = type
        self.section = section
        self.duration = duration
        self.context = context
    }
}

/// Types of user interactions that can be tracked
enum BehaviorInteractionType: Codable, Equatable {
    case sectionViewed(AppSection)
    case personInteraction(UUID)
    case teamInteraction(UUID)
    case workflowStarted(WorkflowType)
    case workflowCompleted(WorkflowType)
    case goalCreated(String) // category
    case goalCompleted(UUID)
    case teamCreated(UUID)
    case suggestionAccepted(String)
    case suggestionDismissed(String)
    case searchPerformed(String)
    case helpRequested(HelpType)
    
    var isHighFrequency: Bool {
        switch self {
        case .sectionViewed, .searchPerformed:
            return true
        default:
            return false
        }
    }
    
    var category: InteractionCategory {
        switch self {
        case .sectionViewed:
            return .navigation
        case .personInteraction, .teamInteraction:
            return .social
        case .workflowStarted, .workflowCompleted:
            return .workflow
        case .goalCreated, .goalCompleted:
            return .productivity
        case .teamCreated:
            return .collaboration
        case .suggestionAccepted, .suggestionDismissed:
            return .ai
        case .searchPerformed:
            return .discovery
        case .helpRequested:
            return .learning
        }
    }
}

enum InteractionCategory: String, CaseIterable {
    case navigation = "Navigation"
    case social = "Social"
    case workflow = "Workflow"
    case productivity = "Productivity"
    case collaboration = "Collaboration"
    case ai = "AI"
    case discovery = "Discovery"
    case learning = "Learning"
    
    var color: Color {
        switch self {
        case .navigation: return .blue
        case .social: return .green
        case .workflow: return .purple
        case .productivity: return .orange
        case .collaboration: return .cyan
        case .ai: return .pink
        case .discovery: return .yellow
        case .learning: return .indigo
        }
    }
}
