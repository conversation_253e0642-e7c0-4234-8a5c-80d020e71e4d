//
//  PeopleFloatingActionsView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Floating action buttons for people view with enhanced animations
/// Features grouping toggle, add person, and quick actions
struct PeopleFloatingActionsView: View {
    let peopleManager: PeopleManager
    @Binding var showingAddPerson: Bool
    @Binding var isGroupingActive: Bool
    
    @State private var isAnimating = false
    @State private var showingQuickActions = false
    
    var body: some View {
        HStack(spacing: 16) {
            // Grouping control button
            Button(action: {
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
                
                withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                    isGroupingActive.toggle()
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: isGroupingActive ? "circle.grid.2x2.fill" : "circle.grid.2x2")
                        .font(.system(size: 16, weight: .medium))
                        .modifier(RotatingEffect(isAnimating: isGroupingActive))
                    
                    Text(isGroupingActive ? "Ungroup" : "Group")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(
                            LinearGradient(
                                colors: isGroupingActive ? 
                                    [Color.purple, Color.purple.opacity(0.8)] :
                                    [Color.gray, Color.gray.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .modifier(FloatingButtonEffect())
                )
            }
            
            Spacer()
            
            // Quick actions menu
            if showingQuickActions {
                HStack(spacing: 12) {
                    // Shuffle positions
                    QuickActionButton(
                        icon: "shuffle",
                        color: .orange,
                        action: {
                            shuffleBubblePositions()
                        }
                    )
                    
                    // Focus favorites
                    QuickActionButton(
                        icon: "star.fill",
                        color: .yellow,
                        action: {
                            focusOnFavorites()
                        }
                    )
                    
                    // Show online only
                    QuickActionButton(
                        icon: "circle.fill",
                        color: .green,
                        action: {
                            toggleOnlineFilter()
                        }
                    )
                }
                .transition(.move(edge: .trailing).combined(with: .opacity))
            }
            
            // Quick actions toggle
            Button(action: {
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
                
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showingQuickActions.toggle()
                }
            }) {
                Image(systemName: showingQuickActions ? "xmark" : "ellipsis")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color.blue.opacity(0.8), Color.blue],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .modifier(FloatingButtonEffect())
                    )
                    .rotationEffect(.degrees(showingQuickActions ? 180 : 0))
            }
            
            // Add person button
            Button(action: {
                let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
                impactFeedback.impactOccurred()
                
                showingAddPerson = true
            }) {
                Image(systemName: "person.badge.plus")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 56, height: 56)
                    .background(
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color.blue, Color.purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .modifier(FloatingButtonEffect())
                    )
                    .modifier(BounceEffect(isAnimating: isAnimating))
            }
        }
        .padding(.horizontal, 20)
        .shadow(color: .black.opacity(0.2), radius: 15, x: 0, y: 8)
        .onAppear {
            isAnimating = true
        }
    }
    
    // MARK: - Quick Actions
    
    private func shuffleBubblePositions() {
        // Trigger a re-layout of bubbles with animation
        withAnimation(.spring(response: 1.0, dampingFraction: 0.6)) {
            // This would trigger a re-render of the bubble positions
            // In a real implementation, you might update a state variable
            // that causes the bubble layout to recalculate
        }
        
        // Hide quick actions after use
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            showingQuickActions = false
        }
    }
    
    private func focusOnFavorites() {
        withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
            peopleManager.showFavoritesOnly = true
            showingQuickActions = false
        }
    }
    
    private func toggleOnlineFilter() {
        withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
            peopleManager.showOnlineOnly.toggle()
            showingQuickActions = false
        }
    }
}

/// Quick action button component
struct QuickActionButton: View {
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 36, height: 36)
                .background(
                    Circle()
                        .fill(color)
                        .shadow(color: color.opacity(0.3), radius: 4, x: 0, y: 2)
                )
        }
    }
}

/// Rotating effect for icons
struct RotatingEffect: ViewModifier {
    let isAnimating: Bool
    @State private var rotation: Double = 0
    
    func body(content: Content) -> some View {
        content
            .rotationEffect(.degrees(rotation))
            .onAppear {
                if isAnimating {
                    withAnimation(
                        .linear(duration: 2)
                        .repeatForever(autoreverses: false)
                    ) {
                        rotation = 360
                    }
                }
            }
            .onChange(of: isAnimating) { _, animating in
                if animating {
                    withAnimation(
                        .linear(duration: 2)
                        .repeatForever(autoreverses: false)
                    ) {
                        rotation = 360
                    }
                } else {
                    withAnimation(.easeOut(duration: 0.5)) {
                        rotation = 0
                    }
                }
            }
    }
}

/// Bounce effect for buttons
struct BounceEffect: ViewModifier {
    let isAnimating: Bool
    @State private var scale: CGFloat = 1.0
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .onAppear {
                if isAnimating {
                    withAnimation(
                        .easeInOut(duration: 1.5)
                        .repeatForever(autoreverses: true)
                    ) {
                        scale = 1.05
                    }
                }
            }
    }
}

/// Floating button effect with hover and shadow
struct FloatingButtonEffect: ViewModifier {
    @State private var isHovering = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isHovering ? 1.05 : 1.0)
            .shadow(
                color: .black.opacity(isHovering ? 0.3 : 0.2),
                radius: isHovering ? 20 : 15,
                x: 0,
                y: isHovering ? 10 : 8
            )
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isHovering)
            .onHover { hovering in
                isHovering = hovering
            }
    }
}

struct PeopleFloatingActionsView_Previews: PreviewProvider {
    static var previews: some View {
        let peopleManager = PeopleManager()

        return VStack {
            Spacer()

            PeopleFloatingActionsView(
                peopleManager: peopleManager,
                showingAddPerson: .constant(false),
                isGroupingActive: .constant(false)
            )
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.gray.opacity(0.1))
    }
}
