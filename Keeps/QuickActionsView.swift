//
//  QuickActionsView.swift
//  Keeps
//
//  Quick actions floating interface for common workflows
//

import SwiftUI

// MARK: - Quick Actions Floating Button

/// Floating action button with expandable quick actions
struct QuickActionsFloatingButton: View {
    @StateObject private var quickActionManager = QuickActionManager()
    @State private var isExpanded = false
    @State private var animateActions = false
    
    var body: some View {
        VStack {
            Spacer()
            
            HStack {
                Spacer()
                
                ZStack {
                    // Expanded actions
                    if isExpanded {
                        VStack(spacing: 12) {
                            ForEach(Array(quickActionManager.availableActions.enumerated()), id: \.element.id) { index, action in
                                SimpleQuickActionButton(action: action) {
                                    quickActionManager.executeQuickAction(action)
                                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                        isExpanded = false
                                    }
                                }
                                .opacity(animateActions ? 1 : 0)
                                .scaleEffect(animateActions ? 1 : 0.8)
                                .animation(
                                    .spring(response: 0.4, dampingFraction: 0.8)
                                    .delay(Double(index) * 0.1),
                                    value: animateActions
                                )
                            }
                        }
                        .padding(.bottom, 80)
                    }
                    
                    // Main floating button
                    Button(action: toggleExpanded) {
                        Image(systemName: isExpanded ? "xmark" : "plus")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(width: 56, height: 56)
                            .background(
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            colors: [.blue, .purple],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                            )
                            .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
                    }
                    .rotationEffect(.degrees(isExpanded ? 45 : 0))
                    .scaleEffect(isExpanded ? 1.1 : 1.0)
                    .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isExpanded)
                }
            }
            .padding(.trailing, 20)
            .padding(.bottom, 100) // Account for tab bar
        }
        .sheet(item: $quickActionManager.selectedAction) { action in
            QuickActionDetailView(action: action)
        }
    }
    
    private func toggleExpanded() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            isExpanded.toggle()
            animateActions = isExpanded
        }
        
        if !isExpanded {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                animateActions = false
            }
        }
    }
}

// MARK: - Quick Action Button (using existing from PeopleFloatingActionsView)

// MARK: - Quick Action Detail View

struct QuickActionDetailView: View {
    let action: QuickAction
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 16) {
                    Circle()
                        .fill(action.color.opacity(0.2))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Image(systemName: action.icon)
                                .font(.title)
                                .foregroundColor(action.color)
                        )
                    
                    VStack(spacing: 8) {
                        Text(action.title)
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text(action.subtitle)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding(.top, 20)
                
                // Content based on action type
                actionContent
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .navigationTitle(action.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // Save action
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    @ViewBuilder
    private var actionContent: some View {
        switch action.id {
        case "add_interaction":
            InteractionCaptureForm()
        case "update_team":
            TeamUpdateForm()
        case "add_achievement":
            AchievementForm()
        case "plan_meeting":
            MeetingPlanningForm()
        case "weekly_review":
            WeeklyReviewForm()
        default:
            Text("Coming soon...")
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Action Forms

struct InteractionCaptureForm: View {
    @State private var selectedPerson = ""
    @State private var interactionType: InteractionType = .call
    @State private var notes = ""
    @State private var date = Date()
    
    var body: some View {
        VStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Person")
                    .font(.headline)
                    .fontWeight(.medium)
                
                TextField("Who did you interact with?", text: $selectedPerson)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Interaction Type")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Picker("Type", selection: $interactionType) {
                    ForEach(InteractionType.allCases, id: \.self) { type in
                        Label(type.rawValue, systemImage: type.icon)
                            .tag(type)
                    }
                }
                .pickerStyle(.segmented)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Date & Time")
                    .font(.headline)
                    .fontWeight(.medium)
                
                DatePicker("", selection: $date, displayedComponents: [.date, .hourAndMinute])
                    .datePickerStyle(.compact)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Notes")
                    .font(.headline)
                    .fontWeight(.medium)
                
                TextField("What did you discuss?", text: $notes, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(4...8)
            }
        }
    }
}

struct TeamUpdateForm: View {
    @State private var selectedTeam = ""
    @State private var updateType: UpdateType = .progress
    @State private var description = ""
    @State private var impact: ImpactLevel = .medium
    
    var body: some View {
        VStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Team")
                    .font(.headline)
                    .fontWeight(.medium)
                
                TextField("Which team?", text: $selectedTeam)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Update Type")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Picker("Type", selection: $updateType) {
                    ForEach(UpdateType.allCases, id: \.self) { type in
                        Label(type.rawValue, systemImage: type.icon)
                            .tag(type)
                    }
                }
                .pickerStyle(.menu)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Description")
                    .font(.headline)
                    .fontWeight(.medium)
                
                TextField("What happened?", text: $description, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(4...8)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Impact Level")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Picker("Impact", selection: $impact) {
                    ForEach(ImpactLevel.allCases, id: \.self) { level in
                        Text(level.rawValue)
                            .tag(level)
                    }
                }
                .pickerStyle(.segmented)
            }
        }
    }
}

struct AchievementForm: View {
    @State private var title = ""
    @State private var description = ""
    @State private var category: GoalCategory = .general
    @State private var relatedPeople: [String] = []
    
    var body: some View {
        VStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Achievement")
                    .font(.headline)
                    .fontWeight(.medium)
                
                TextField("What did you accomplish?", text: $title)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Category")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Picker("Category", selection: $category) {
                    ForEach(GoalCategory.allCases, id: \.self) { cat in
                        Label(cat.rawValue, systemImage: cat.icon)
                            .tag(cat)
                    }
                }
                .pickerStyle(.menu)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Details")
                    .font(.headline)
                    .fontWeight(.medium)
                
                TextField("Tell us more about it", text: $description, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(4...8)
            }
        }
    }
}

struct MeetingPlanningForm: View {
    var body: some View {
        Text("Meeting planning form coming soon...")
            .foregroundColor(.secondary)
    }
}

struct WeeklyReviewForm: View {
    var body: some View {
        Text("Weekly review form coming soon...")
            .foregroundColor(.secondary)
    }
}

// MARK: - Simple Quick Action Button

struct SimpleQuickActionButton: View {
    let action: QuickAction
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                Image(systemName: action.icon)
                    .font(.headline)
                    .foregroundColor(action.color)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(action.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(action.subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
