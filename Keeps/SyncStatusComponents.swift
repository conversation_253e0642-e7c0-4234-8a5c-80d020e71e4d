//
//  SyncStatusComponents.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Sync Status Indicator

/// Main sync status indicator showing overall sync health
struct SyncStatusIndicator: View {
    @ObservedObject var syncManager: DataSyncManager
    @State private var isExpanded = false
    
    var body: some View {
        HStack(spacing: 8) {
            // Status icon with animation
            Image(systemName: statusIcon)
                .foregroundColor(statusColor)
                .font(.system(size: 16, weight: .medium))
                .rotationEffect(.degrees(syncManager.syncStatus.syncProgress > 0 && syncManager.syncStatus.syncProgress < 1 ? 360 : 0))
                .animation(
                    syncManager.syncStatus.syncProgress > 0 && syncManager.syncStatus.syncProgress < 1 ?
                    .linear(duration: 1).repeatForever(autoreverses: false) : .default,
                    value: syncManager.syncStatus.syncProgress
                )
            
            if isExpanded {
                VStack(alignment: .leading, spacing: 2) {
                    Text(statusText)
                        .font(.caption)
                        .fontWeight(.medium)
                    
                    if let currentOperation = syncManager.syncStatus.currentOperation {
                        Text(currentOperation)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    if syncManager.syncStatus.syncProgress > 0 && syncManager.syncStatus.syncProgress < 1 {
                        ProgressView(value: syncManager.syncStatus.syncProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: statusColor))
                            .frame(width: 100)
                    }
                }
                .transition(.opacity.combined(with: .scale))
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(statusColor.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(statusColor.opacity(0.3), lineWidth: 1)
                )
        )
        .onTapGesture {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isExpanded.toggle()
            }
        }
    }
    
    private var statusIcon: String {
        if !syncManager.syncStatus.isOnline {
            return "wifi.slash"
        } else if syncManager.syncStatus.syncProgress > 0 && syncManager.syncStatus.syncProgress < 1 {
            return "arrow.clockwise"
        } else if syncManager.syncStatus.failedOperations > 0 {
            return "exclamationmark.triangle"
        } else if syncManager.syncStatus.conflictedOperations > 0 {
            return "exclamationmark.circle"
        } else {
            return "checkmark.circle"
        }
    }
    
    private var statusColor: Color {
        if !syncManager.syncStatus.isOnline {
            return .red
        } else if syncManager.syncStatus.syncProgress > 0 && syncManager.syncStatus.syncProgress < 1 {
            return .blue
        } else if syncManager.syncStatus.failedOperations > 0 {
            return .orange
        } else if syncManager.syncStatus.conflictedOperations > 0 {
            return .purple
        } else {
            return .green
        }
    }
    
    private var statusText: String {
        if !syncManager.syncStatus.isOnline {
            return "Offline"
        } else if syncManager.syncStatus.syncProgress > 0 && syncManager.syncStatus.syncProgress < 1 {
            return "Syncing..."
        } else if syncManager.syncStatus.failedOperations > 0 {
            return "\(syncManager.syncStatus.failedOperations) Failed"
        } else if syncManager.syncStatus.conflictedOperations > 0 {
            return "\(syncManager.syncStatus.conflictedOperations) Conflicts"
        } else {
            return "Up to date"
        }
    }
}

// MARK: - Sync Progress Bar

/// Detailed sync progress bar with operation details
struct SyncProgressBar: View {
    @ObservedObject var syncManager: DataSyncManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Sync Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("\(Int(syncManager.syncStatus.syncProgress * 100))%")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(value: syncManager.syncStatus.syncProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                .frame(height: 8)
                .background(Color.gray.opacity(0.2))
                .cornerRadius(4)
            
            if let currentOperation = syncManager.syncStatus.currentOperation {
                Text(currentOperation)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            HStack {
                Label("\(syncManager.syncStatus.pendingOperations)", systemImage: "clock")
                    .font(.caption2)
                    .foregroundColor(.orange)
                
                Spacer()
                
                if let estimatedTime = syncManager.syncStatus.estimatedTimeRemaining {
                    Text("~\(Int(estimatedTime))s remaining")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Network Status Badge

/// Network connectivity status badge
struct NetworkStatusBadge: View {
    @ObservedObject var offlineQueue: OfflineSyncQueue
    
    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: offlineQueue.networkStatus.icon)
                .font(.system(size: 12, weight: .medium))
            
            Text(offlineQueue.networkStatus.description)
                .font(.caption2)
                .fontWeight(.medium)
        }
        .foregroundColor(offlineQueue.networkStatus.color)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            Capsule()
                .fill(offlineQueue.networkStatus.color.opacity(0.15))
        )
    }
}

// MARK: - Conflict Alert Badge

/// Badge showing active conflicts that need resolution
struct ConflictAlertBadge: View {
    @ObservedObject var syncManager: DataSyncManager
    @State private var isShowingConflicts = false
    
    var body: some View {
        if !syncManager.activeConflicts.isEmpty {
            Button(action: {
                isShowingConflicts = true
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 12))
                    
                    Text("\(syncManager.activeConflicts.count)")
                        .font(.caption2)
                        .fontWeight(.bold)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(Color.red)
                )
            }
            .sheet(isPresented: $isShowingConflicts) {
                ConflictResolutionSheet(syncManager: syncManager)
            }
        }
    }
}

// MARK: - Sync Analytics Card

/// Card displaying sync analytics and statistics
struct SyncAnalyticsCard: View {
    @ObservedObject var syncManager: DataSyncManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Sync Analytics")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                AnalyticsMetric(
                    title: "Success Rate",
                    value: "\(Int(syncManager.syncAnalytics.successRate * 100))%",
                    icon: "checkmark.circle",
                    color: .green
                )
                
                AnalyticsMetric(
                    title: "Total Syncs",
                    value: "\(syncManager.syncAnalytics.totalSyncs)",
                    icon: "arrow.clockwise",
                    color: .blue
                )
                
                AnalyticsMetric(
                    title: "Avg Time",
                    value: "\(Int(syncManager.syncAnalytics.averageSyncTime * 1000))ms",
                    icon: "timer",
                    color: .orange
                )
                
                AnalyticsMetric(
                    title: "Data Transferred",
                    value: formatBytes(syncManager.syncAnalytics.dataTransferred),
                    icon: "arrow.up.arrow.down",
                    color: .purple
                )
            }
            
            if let lastSync = syncManager.syncStatus.lastSyncTime {
                Text("Last sync: \(formatRelativeTime(lastSync))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatRelativeTime(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - Analytics Metric

/// Individual analytics metric display
struct AnalyticsMetric: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(color)
                
                Spacer()
            }
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.1))
        )
    }
}

// MARK: - Backup Status Card

/// Card showing backup status and controls
struct BackupStatusCard: View {
    @ObservedObject var syncManager: DataSyncManager
    @State private var isShowingBackups = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Backup Status")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    isShowingBackups = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            if let latestBackup = syncManager.backupSnapshots.first {
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Latest Backup")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(latestBackup.formattedDate)
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        Text(latestBackup.formattedSize)
                            .font(.caption)
                            .fontWeight(.medium)
                        
                        Text("\(latestBackup.entityCounts.values.reduce(0, +)) items")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            } else {
                Text("No backups available")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Button(action: {
                    Task {
                        await syncManager.createBackup(isAutomatic: false)
                    }
                }) {
                    Label("Create Backup", systemImage: "plus.circle")
                        .font(.caption)
                }
                .disabled(syncManager.isPerformingBackup)
                
                Spacer()
                
                if syncManager.isPerformingBackup {
                    HStack(spacing: 4) {
                        ProgressView()
                            .scaleEffect(0.7)
                        
                        Text("Creating...")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .sheet(isPresented: $isShowingBackups) {
            BackupManagementSheet(syncManager: syncManager)
        }
    }
}

// MARK: - Placeholder Sheets

/// Placeholder for conflict resolution sheet
struct ConflictResolutionSheet: View {
    @ObservedObject var syncManager: DataSyncManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Conflict Resolution")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Conflict resolution interface would be implemented here")
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
                
                Spacer()
            }
            .navigationTitle("Conflicts")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

/// Placeholder for backup management sheet
struct BackupManagementSheet: View {
    @ObservedObject var syncManager: DataSyncManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Backup Management")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Backup management interface would be implemented here")
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding()
                
                Spacer()
            }
            .navigationTitle("Backups")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
