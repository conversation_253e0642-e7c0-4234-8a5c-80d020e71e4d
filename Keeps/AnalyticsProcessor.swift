//
//  AnalyticsProcessor.swift
//  Keeps
//
//  Advanced analytics processor for user behavior analysis and pattern recognition
//  Powers the Smart Suggestions Engine with intelligent insights
//

import Foundation
import CoreData
import Combine

// MARK: - Analytics Processor

/// Processes user behavior data to identify patterns and generate insights
class AnalyticsProcessor: ObservableObject {
    
    // MARK: - Private Properties
    
    private var interactionHistory: [UserInteraction] = []
    private var behaviorPatterns: UserBehaviorPatterns = UserBehaviorPatterns()
    private let maxHistorySize = 10000
    
    // MARK: - Public Methods
    
    /// Record a user interaction for analysis
    func recordInteraction(_ interaction: UserInteraction) {
        interactionHistory.append(interaction)
        
        // Maintain history size limit
        if interactionHistory.count > maxHistorySize {
            interactionHistory.removeFirst(interactionHistory.count - maxHistorySize)
        }
        
        // Update real-time patterns for high-frequency interactions
        if interaction.type.isHighFrequency {
            updateRealtimePatterns(with: interaction)
        }
    }
    
    /// Analyze user behavior and return updated patterns
    func analyzeUserBehavior() async -> UserBehaviorPatterns {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let patterns = self.performBehaviorAnalysis()
                continuation.resume(returning: patterns)
            }
        }
    }
    
    /// Get updated patterns without full analysis
    func getUpdatedPatterns() async -> UserBehaviorPatterns {
        return behaviorPatterns
    }
    
    /// Identify automation opportunities based on repetitive patterns
    func identifyAutomationOpportunities(patterns: UserBehaviorPatterns) async -> [AutomationSuggestion] {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let suggestions = self.analyzeAutomationOpportunities(patterns: patterns)
                continuation.resume(returning: suggestions)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func performBehaviorAnalysis() -> UserBehaviorPatterns {
        var patterns = UserBehaviorPatterns()
        
        // Analyze interaction frequency
        patterns.dailyInteractionCount = calculateDailyInteractionCount()
        patterns.peakUsageHours = identifyPeakUsageHours()
        patterns.preferredSections = analyzePreferredSections()
        
        // Analyze workflow patterns
        patterns.commonWorkflows = identifyCommonWorkflows()
        patterns.workflowCompletionRate = calculateWorkflowCompletionRate()
        patterns.averageWorkflowDuration = calculateAverageWorkflowDuration()
        
        // Analyze social patterns
        patterns.frequentCollaborators = identifyFrequentCollaborators()
        patterns.teamFormationPatterns = analyzeTeamFormationPatterns()
        patterns.communicationFrequency = analyzeCommunicationFrequency()
        
        // Analyze goal patterns
        patterns.goalCategories = analyzeBehaviorGoalCategories()
        patterns.goalCompletionRate = calculateGoalCompletionRate()
        patterns.goalTimingPatterns = analyzeGoalTimingPatterns()
        
        // Update stored patterns
        behaviorPatterns = patterns
        
        return patterns
    }
    
    private func updateRealtimePatterns(with interaction: UserInteraction) {
        // Update real-time metrics
        behaviorPatterns.lastInteractionTime = interaction.timestamp
        behaviorPatterns.sessionDuration += interaction.duration
        
        // Update section preferences
        if let sectionIndex = behaviorPatterns.preferredSections.firstIndex(where: { $0.section == interaction.section }) {
            behaviorPatterns.preferredSections[sectionIndex].frequency += 1
        } else {
            behaviorPatterns.preferredSections.append(
                SectionUsage(section: interaction.section, frequency: 1, averageDuration: interaction.duration)
            )
        }
    }
    
    private func calculateDailyInteractionCount() -> Int {
        let today = Calendar.current.startOfDay(for: Date())
        let todayInteractions = interactionHistory.filter { interaction in
            Calendar.current.isDate(interaction.timestamp, inSameDayAs: today)
        }
        return todayInteractions.count
    }
    
    private func identifyPeakUsageHours() -> [Int] {
        var hourCounts: [Int: Int] = [:]
        
        for interaction in interactionHistory {
            let hour = Calendar.current.component(.hour, from: interaction.timestamp)
            hourCounts[hour, default: 0] += 1
        }
        
        let sortedHours = hourCounts.sorted { $0.value > $1.value }
        return Array(sortedHours.prefix(3).map { $0.key })
    }
    
    private func analyzePreferredSections() -> [SectionUsage] {
        var sectionCounts: [AppSection: (count: Int, totalDuration: TimeInterval)] = [:]
        
        for interaction in interactionHistory {
            let current = sectionCounts[interaction.section] ?? (count: 0, totalDuration: 0)
            sectionCounts[interaction.section] = (
                count: current.count + 1,
                totalDuration: current.totalDuration + interaction.duration
            )
        }
        
        return sectionCounts.map { section, data in
            SectionUsage(
                section: section,
                frequency: data.count,
                averageDuration: data.count > 0 ? data.totalDuration / Double(data.count) : 0
            )
        }.sorted { $0.frequency > $1.frequency }
    }
    
    private func identifyCommonWorkflows() -> [WorkflowPattern] {
        var workflowCounts: [WorkflowType: Int] = [:]
        
        // Analyze workflow usage from interactions
        for interaction in interactionHistory {
            if case .workflowStarted(let workflowType) = interaction.type {
                workflowCounts[workflowType, default: 0] += 1
            }
        }
        
        return workflowCounts.map { workflowType, count in
            WorkflowPattern(
                workflowType: workflowType,
                frequency: count,
                averageCompletionTime: calculateAverageCompletionTime(for: workflowType),
                successRate: calculateSuccessRate(for: workflowType)
            )
        }.sorted { $0.frequency > $1.frequency }
    }
    
    private func calculateWorkflowCompletionRate() -> Double {
        let workflowStarts = interactionHistory.filter { interaction in
            if case .workflowStarted = interaction.type { return true }
            return false
        }.count
        
        let workflowCompletions = interactionHistory.filter { interaction in
            if case .workflowCompleted = interaction.type { return true }
            return false
        }.count
        
        return workflowStarts > 0 ? Double(workflowCompletions) / Double(workflowStarts) : 0.0
    }
    
    private func calculateAverageWorkflowDuration() -> TimeInterval {
        var workflowDurations: [TimeInterval] = []
        var workflowStarts: [WorkflowType: Date] = [:]
        
        for interaction in interactionHistory {
            switch interaction.type {
            case .workflowStarted(let workflowType):
                workflowStarts[workflowType] = interaction.timestamp
            case .workflowCompleted(let workflowType):
                if let startTime = workflowStarts[workflowType] {
                    let duration = interaction.timestamp.timeIntervalSince(startTime)
                    workflowDurations.append(duration)
                    workflowStarts.removeValue(forKey: workflowType)
                }
            default:
                break
            }
        }
        
        return workflowDurations.isEmpty ? 0 : workflowDurations.reduce(0, +) / Double(workflowDurations.count)
    }
    
    private func identifyFrequentCollaborators() -> [CollaboratorPattern] {
        var collaboratorCounts: [UUID: Int] = [:]
        
        for interaction in interactionHistory {
            if case .personInteraction(let personId) = interaction.type {
                collaboratorCounts[personId, default: 0] += 1
            }
        }
        
        return collaboratorCounts.map { personId, count in
            CollaboratorPattern(
                personId: personId,
                interactionCount: count,
                lastInteraction: getLastInteractionDate(for: personId),
                relationshipStrength: calculateRelationshipStrength(for: personId)
            )
        }.sorted { $0.interactionCount > $1.interactionCount }
    }
    
    private func analyzeTeamFormationPatterns() -> [TeamPattern] {
        var teamPatterns: [TeamPattern] = []
        
        // Analyze team creation and membership patterns
        for interaction in interactionHistory {
            if case .teamCreated(let teamId) = interaction.type {
                let pattern = TeamPattern(
                    teamId: teamId,
                    creationDate: interaction.timestamp,
                    memberCount: getTeamMemberCount(teamId),
                    activityLevel: calculateTeamActivityLevel(teamId)
                )
                teamPatterns.append(pattern)
            }
        }
        
        return teamPatterns.sorted { $0.activityLevel > $1.activityLevel }
    }
    
    private func analyzeCommunicationFrequency() -> CommunicationPattern {
        let _ = interactionHistory.count // Total interactions for future analysis
        let communicationInteractions = interactionHistory.filter { interaction in
            switch interaction.type {
            case .personInteraction, .teamInteraction:
                return true
            default:
                return false
            }
        }.count
        
        return CommunicationPattern(
            dailyAverage: Double(communicationInteractions) / 7.0, // Assuming 7-day analysis
            peakDays: identifyPeakCommunicationDays(),
            preferredMethods: analyzePreferredCommunicationMethods()
        )
    }
    
    private func analyzeBehaviorGoalCategories() -> [BehaviorGoalCategory] {
        var categoryPatterns: [String: Int] = [:]
        
        for interaction in interactionHistory {
            if case .goalCreated(let category) = interaction.type {
                categoryPatterns[category, default: 0] += 1
            }
        }
        
        return categoryPatterns.map { category, count in
            BehaviorGoalCategory(
                name: category,
                frequency: count,
                completionRate: calculateCategoryCompletionRate(category)
            )
        }.sorted(by: { $0.frequency > $1.frequency })
    }

    private func calculateCategoryCompletionRate(_ category: String) -> Double {
        // Placeholder implementation - calculate completion rate for specific category
        return 0.75 // 75% completion rate as placeholder
    }

    private func calculateGoalCompletionRate() -> Double {
        let goalCreations = interactionHistory.filter { interaction in
            if case .goalCreated = interaction.type { return true }
            return false
        }.count
        
        let goalCompletions = interactionHistory.filter { interaction in
            if case .goalCompleted = interaction.type { return true }
            return false
        }.count
        
        return goalCreations > 0 ? Double(goalCompletions) / Double(goalCreations) : 0.0
    }
    
    private func analyzeGoalTimingPatterns() -> [TimingPattern] {
        var timingPatterns: [TimingPattern] = []
        
        // Analyze when goals are typically created and completed
        let goalInteractions = interactionHistory.filter { interaction in
            switch interaction.type {
            case .goalCreated, .goalCompleted:
                return true
            default:
                return false
            }
        }
        
        var hourPatterns: [Int: Int] = [:]
        var dayPatterns: [Int: Int] = [:]
        
        for interaction in goalInteractions {
            let hour = Calendar.current.component(.hour, from: interaction.timestamp)
            let weekday = Calendar.current.component(.weekday, from: interaction.timestamp)
            
            hourPatterns[hour, default: 0] += 1
            dayPatterns[weekday, default: 0] += 1
        }
        
        // Convert to timing patterns
        for (hour, count) in hourPatterns {
            timingPatterns.append(TimingPattern(type: .hourly, value: hour, frequency: count))
        }
        
        for (day, count) in dayPatterns {
            timingPatterns.append(TimingPattern(type: .daily, value: day, frequency: count))
        }
        
        return timingPatterns.sorted { $0.frequency > $1.frequency }
    }
    
    private func analyzeAutomationOpportunities(patterns: UserBehaviorPatterns) -> [AutomationSuggestion] {
        var suggestions: [AutomationSuggestion] = []
        
        // Identify repetitive workflows
        for workflowPattern in patterns.commonWorkflows {
            if workflowPattern.frequency > 5 && workflowPattern.successRate > 0.8 {
                suggestions.append(AutomationSuggestion(
                    type: .workflowAutomation,
                    title: "Automate \(workflowPattern.workflowType.rawValue)",
                    description: "You've completed this workflow \(workflowPattern.frequency) times with \(Int(workflowPattern.successRate * 100))% success rate",
                    confidence: min(0.9, Double(workflowPattern.frequency) / 10.0),
                    automationType: .workflowTrigger(workflowPattern.workflowType)
                ))
            }
        }
        
        // Identify repetitive goal patterns
        for goalCategory in patterns.goalCategories {
            if goalCategory.frequency > 3 && goalCategory.completionRate > 0.7 {
                suggestions.append(AutomationSuggestion(
                    type: .goalAutomation,
                    title: "Auto-suggest \(goalCategory.name) goals",
                    description: "Automatically suggest goals in this category based on your patterns",
                    confidence: goalCategory.completionRate,
                    automationType: .goalSuggestion(goalCategory.name)
                ))
            }
        }
        
        // Identify communication automation opportunities
        for collaborator in patterns.frequentCollaborators.prefix(3) {
            if collaborator.interactionCount > 10 {
                suggestions.append(AutomationSuggestion(
                    type: .communicationAutomation,
                    title: "Auto-remind for frequent collaborator",
                    description: "Set up automatic reminders to connect with this person",
                    confidence: min(0.8, Double(collaborator.interactionCount) / 20.0),
                    automationType: .reminderAutomation(collaborator.personId)
                ))
            }
        }
        
        return suggestions.sorted { $0.confidence > $1.confidence }
    }
    
    // MARK: - Helper Methods
    
    private func calculateAverageCompletionTime(for workflowType: WorkflowType) -> TimeInterval {
        // Implementation for calculating average completion time
        return 300.0 // Placeholder: 5 minutes
    }
    
    private func calculateSuccessRate(for workflowType: WorkflowType) -> Double {
        // Implementation for calculating success rate
        return 0.85 // Placeholder: 85% success rate
    }
    
    private func getLastInteractionDate(for personId: UUID) -> Date {
        // Implementation for getting last interaction date
        return Date() // Placeholder
    }
    
    private func calculateRelationshipStrength(for personId: UUID) -> Double {
        // Implementation for calculating relationship strength
        return 0.7 // Placeholder
    }
    
    private func getTeamMemberCount(_ teamId: UUID) -> Int {
        // Implementation for getting team member count
        return 5 // Placeholder
    }
    
    private func calculateTeamActivityLevel(_ teamId: UUID) -> Double {
        // Implementation for calculating team activity level
        return 0.8 // Placeholder
    }
    
    private func identifyPeakCommunicationDays() -> [Int] {
        // Implementation for identifying peak communication days
        return [2, 3, 4] // Placeholder: Tuesday, Wednesday, Thursday
    }
    
    private func analyzePreferredCommunicationMethods() -> [String] {
        // Implementation for analyzing preferred communication methods
        return ["Direct Message", "Team Chat", "Video Call"] // Placeholder
    }
    
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let userInteraction = Notification.Name("userInteraction")
}
