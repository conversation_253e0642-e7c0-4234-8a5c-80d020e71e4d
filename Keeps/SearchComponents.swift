//
//  SearchComponents.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import SwiftUIX

// MARK: - Search Results Components

/// Main search results view with categorization
struct SearchResultsView: View {
    @ObservedObject var searchManager: SearchManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Results summary
                SearchResultsSummary(
                    resultCount: searchManager.searchResults.count,
                    query: searchManager.currentQuery.text,
                    hasFilters: searchManager.currentQuery.hasActiveFilters
                )
                .padding(.horizontal)
                
                // Categorized results
                let groupedResults = Dictionary(grouping: searchManager.searchResults) { $0.section }
                
                ForEach(SearchSection.allCases.filter { groupedResults[$0] != nil }, id: \.self) { section in
                    if let results = groupedResults[section], !results.isEmpty {
                        SearchResultsSection(
                            section: section,
                            results: results,
                            onResultTapped: { result in
                                searchManager.navigateToResult(result)
                            }
                        )
                        .padding(.horizontal)
                    }
                }
            }
            .padding(.vertical)
        }
    }
}

/// Search results summary header
struct SearchResultsSummary: View {
    let resultCount: Int
    let query: String
    let hasFilters: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("\(resultCount) result\(resultCount == 1 ? "" : "s")")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                HStack(spacing: 4) {
                    Text("for")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("\"\(query)\"")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                    
                    if hasFilters {
                        Text("with filters")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
    }
}

/// Section of search results
struct SearchResultsSection: View {
    let section: SearchSection
    let results: [SearchResult]
    let onResultTapped: (SearchResult) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Section header
            HStack(spacing: 8) {
                Image(systemName: section.icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(section.color)
                
                Text(section.rawValue)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("(\(results.count))")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
            }
            
            // Results
            VStack(spacing: 8) {
                ForEach(results) { result in
                    SearchResultCard(
                        result: result,
                        onTap: {
                            onResultTapped(result)
                        }
                    )
                }
            }
        }
        .padding(16)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

/// Individual search result card
struct SearchResultCard: View {
    let result: SearchResult
    let onTap: () -> Void
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Section indicator
                VStack {
                    Image(systemName: result.section.icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(result.section.color)
                        .frame(width: 32, height: 32)
                        .background(
                            Circle()
                                .fill(result.section.color.opacity(0.1))
                        )
                    
                    Spacer()
                }
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(result.title)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        // Priority indicator
                        if result.metadata.priority != .medium {
                            Circle()
                                .fill(result.metadata.priority.color)
                                .frame(width: 6, height: 6)
                        }
                    }
                    
                    if let subtitle = result.subtitle {
                        Text(subtitle)
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                    
                    // Metadata badges
                    HStack(spacing: 6) {
                        if result.metadata.isFavorite {
                            MetadataBadge(icon: "star.fill", color: .orange)
                        }
                        
                        if result.metadata.isRecent {
                            MetadataBadge(icon: "clock.fill", color: .blue)
                        }
                        
                        if result.metadata.hasAttachments {
                            MetadataBadge(icon: "paperclip", color: .gray)
                        }
                        
                        Spacer()
                        
                        Text(result.lastModified, style: .relative)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Navigation arrow
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
            }
            .padding(12)
            .background(Color(.systemGray6).opacity(isPressed ? 1 : 0))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

/// Small metadata badge
struct MetadataBadge: View {
    let icon: String
    let color: Color
    
    var body: some View {
        Image(systemName: icon)
            .font(.system(size: 10, weight: .medium))
            .foregroundColor(color)
    }
}

// MARK: - Search History Components

/// Search history sheet
struct SearchHistorySheet: View {
    @ObservedObject var searchManager: SearchManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                if searchManager.searchHistory.isEmpty {
                    SearchHistoryEmptyState()
                } else {
                    List {
                        ForEach(searchManager.searchHistory) { entry in
                            SearchHistoryRow(
                                entry: entry,
                                onTap: {
                                    searchManager.searchFromHistory(entry)
                                    dismiss()
                                }
                            )
                        }
                        .onDelete { indexSet in
                            // Remove specific entries
                            for index in indexSet {
                                searchManager.searchHistory.remove(at: index)
                            }
                        }
                    }
                }
            }
            .navigationTitle("Search History")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                if !searchManager.searchHistory.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Clear All") {
                            searchManager.clearHistory()
                        }
                        .foregroundColor(.red)
                    }
                }
            }
        }
    }
}

/// Search history row
struct SearchHistoryRow: View {
    let entry: SearchHistoryEntry
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                Image(systemName: "clock")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .frame(width: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(entry.query)
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                    
                    HStack(spacing: 8) {
                        Text(entry.section)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(entry.resultCount) result\(entry.resultCount == 1 ? "" : "s")")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(entry.timestamp, style: .relative)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Image(systemName: "arrow.up.left")
                    .font(.system(size: 12))
                    .foregroundColor(.blue)
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// Empty search history state
struct SearchHistoryEmptyState: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "clock")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("No Search History")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("Your recent searches will appear here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(40)
    }
}

// MARK: - Quick Actions Components

/// Quick search actions
struct QuickSearchActionsView: View {
    @ObservedObject var searchManager: SearchManager
    
    var body: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                SearchQuickActionButton(
                    title: "Recent People",
                    icon: "person.crop.circle",
                    color: .green,
                    action: {
                        searchManager.updateSection(.people)
                        searchManager.currentQuery.filters.isRecent = true
                        searchManager.search()
                    }
                )

                SearchQuickActionButton(
                    title: "Active Teams",
                    icon: "person.3",
                    color: .orange,
                    action: {
                        searchManager.updateSection(.teams)
                        // Add active teams filter
                        searchManager.search()
                    }
                )
            }

            HStack(spacing: 12) {
                SearchQuickActionButton(
                    title: "This Week",
                    icon: "calendar",
                    color: .purple,
                    action: {
                        searchManager.updateSection(.timeline)
                        searchManager.currentQuery.filters.dateRange = .thisWeek
                        searchManager.search()
                    }
                )

                SearchQuickActionButton(
                    title: "Favorites",
                    icon: "star.fill",
                    color: .yellow,
                    action: {
                        searchManager.currentQuery.filters.isFavorite = true
                        searchManager.search()
                    }
                )
            }
        }
    }
}

/// Quick action button for search
struct SearchQuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Search Tips Component

/// Search tips view
struct SearchTipsView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            SearchTip(text: "Use quotes for exact phrases: \"project manager\"")
            SearchTip(text: "Filter by section using the tabs above")
            SearchTip(text: "Use filters to narrow down results")
            SearchTip(text: "Recent searches are saved for quick access")
        }
    }
}

/// Individual search tip
struct SearchTip: View {
    let text: String

    var body: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(Color.blue.opacity(0.2))
                .frame(width: 6, height: 6)

            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()
        }
    }
}

// MARK: - Search Filters Sheet

/// Advanced search filters sheet
struct SearchFiltersSheet: View {
    @ObservedObject var searchManager: SearchManager
    @Environment(\.dismiss) private var dismiss
    @State private var tempFilters: SearchFilters

    init(searchManager: SearchManager) {
        self.searchManager = searchManager
        self._tempFilters = State(initialValue: searchManager.currentQuery.filters)
    }

    var body: some View {
        NavigationView {
            Form {
                // Date Range Section
                Section("Date Range") {
                    DateRangeFilterView(dateRange: $tempFilters.dateRange)
                }

                // Priority Section
                Section("Priority") {
                    PriorityFilterView(priorities: $tempFilters.priorities)
                }

                // Status Filters Section
                Section("Status") {
                    StatusFilterView(
                        isFavorite: $tempFilters.isFavorite,
                        isRecent: $tempFilters.isRecent,
                        hasAttachments: $tempFilters.hasAttachments
                    )
                }

                // Relationship Types (for People)
                if searchManager.currentQuery.section == .people || searchManager.currentQuery.section == .all {
                    Section("Relationship Types") {
                        RelationshipTypeFilterView(relationshipTypes: $tempFilters.relationshipTypes)
                    }
                }
            }
            .navigationTitle("Search Filters")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Apply") {
                        searchManager.applyFilters(tempFilters)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
            .toolbar {
                ToolbarItem(placement: .bottomBar) {
                    Button("Clear All Filters") {
                        tempFilters = SearchFilters()
                    }
                    .foregroundColor(.red)
                }
            }
        }
    }
}

/// Date range filter component
struct DateRangeFilterView: View {
    @Binding var dateRange: DateRange?
    @State private var selectedPreset: String = "None"

    private let presets = [
        "None": nil,
        "Today": DateRange.today,
        "This Week": DateRange.thisWeek,
        "This Month": DateRange.thisMonth,
        "This Year": DateRange.thisYear
    ]

    var body: some View {
        Picker("Date Range", selection: $selectedPreset) {
            ForEach(Array(presets.keys.sorted()), id: \.self) { key in
                Text(key).tag(key)
            }
        }
        .pickerStyle(MenuPickerStyle())
        .onChange(of: selectedPreset) { _, newValue in
            dateRange = presets[newValue] ?? nil
        }
    }
}

/// Priority filter component
struct PriorityFilterView: View {
    @Binding var priorities: Set<SearchPriority>

    var body: some View {
        ForEach(SearchPriority.allCases, id: \.self) { priority in
            HStack {
                Button(action: {
                    if priorities.contains(priority) {
                        priorities.remove(priority)
                    } else {
                        priorities.insert(priority)
                    }
                }) {
                    HStack {
                        Image(systemName: priorities.contains(priority) ? "checkmark.square.fill" : "square")
                            .foregroundColor(priorities.contains(priority) ? .blue : .gray)

                        Circle()
                            .fill(priority.color)
                            .frame(width: 12, height: 12)

                        Text(priority.rawValue == 1 ? "Low" : priority.rawValue == 2 ? "Medium" : priority.rawValue == 3 ? "High" : "Critical")
                            .foregroundColor(.primary)

                        Spacer()
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

/// Status filter component
struct StatusFilterView: View {
    @Binding var isFavorite: Bool?
    @Binding var isRecent: Bool?
    @Binding var hasAttachments: Bool?

    var body: some View {
        FilterToggleRow(title: "Favorites Only", icon: "star.fill", color: .orange, value: $isFavorite)
        FilterToggleRow(title: "Recent Only", icon: "clock.fill", color: .blue, value: $isRecent)
        FilterToggleRow(title: "Has Attachments", icon: "paperclip", color: .gray, value: $hasAttachments)
    }
}

/// Filter toggle row
struct FilterToggleRow: View {
    let title: String
    let icon: String
    let color: Color
    @Binding var value: Bool?

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 20)

            Text(title)

            Spacer()

            Button(action: {
                switch value {
                case nil:
                    value = true
                case true:
                    value = false
                case false:
                    value = nil
                case .some(_):
                    value = nil
                }
            }) {
                Group {
                    switch value {
                    case nil:
                        Image(systemName: "minus.circle")
                            .foregroundColor(.gray)
                    case true:
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    case false:
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.red)
                    case .some(_):
                        Image(systemName: "minus.circle")
                            .foregroundColor(.gray)
                    }
                }
                .font(.system(size: 20))
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

/// Relationship type filter component
struct RelationshipTypeFilterView: View {
    @Binding var relationshipTypes: Set<String>

    private let types = ["Family", "Friend", "Colleague", "Professional", "Acquaintance"]

    var body: some View {
        ForEach(types, id: \.self) { type in
            HStack {
                Button(action: {
                    if relationshipTypes.contains(type) {
                        relationshipTypes.remove(type)
                    } else {
                        relationshipTypes.insert(type)
                    }
                }) {
                    HStack {
                        Image(systemName: relationshipTypes.contains(type) ? "checkmark.square.fill" : "square")
                            .foregroundColor(relationshipTypes.contains(type) ? .blue : .gray)

                        Text(type)
                            .foregroundColor(.primary)

                        Spacer()
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}
