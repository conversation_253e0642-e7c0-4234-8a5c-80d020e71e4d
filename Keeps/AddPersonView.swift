//
//  AddPersonView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI

/// View for adding a new person/contact with comprehensive form
/// Features validation, preview, and smooth animations
struct AddPersonView: View {
    let peopleManager: PeopleManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var personName = ""
    @State private var personRole = ""
    @State private var personCompany = ""
    @State private var personEmail = ""
    @State private var personPhone = ""
    @State private var personLocation = ""
    @State private var selectedRelationshipType: Person.RelationshipType = .colleague
    @State private var selectedInteractionFrequency: Person.InteractionFrequency = .rarely
    @State private var personNotes = ""
    @State private var isFavorite = false
    @State private var isCreating = false
    
    var body: some View {
        NavigationView {
            Form {
                // Basic Information
                Section(header: Text("Basic Information")) {
                    TextField("Full Name", text: $personName)
                        .textContentType(.name)
                    
                    TextField("Role/Title", text: $personRole)
                        .textContentType(.jobTitle)
                    
                    TextField("Company", text: $personCompany)
                        .textContentType(.organizationName)
                }
                
                // Contact Information
                Section(header: Text("Contact Information")) {
                    TextField("Email", text: $personEmail)
                        .textContentType(.emailAddress)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                    
                    TextField("Phone", text: $personPhone)
                        .textContentType(.telephoneNumber)
                        .keyboardType(.phonePad)
                    
                    TextField("Location", text: $personLocation)
                        .textContentType(.location)
                }
                
                // Relationship Information
                Section(header: Text("Relationship")) {
                    Picker("Relationship Type", selection: $selectedRelationshipType) {
                        ForEach(Person.RelationshipType.allCases, id: \.self) { type in
                            HStack {
                                Image(systemName: type.icon)
                                    .foregroundColor(type.color)
                                Text(type.rawValue)
                            }
                            .tag(type)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    Picker("Interaction Frequency", selection: $selectedInteractionFrequency) {
                        ForEach(Person.InteractionFrequency.allCases, id: \.self) { frequency in
                            Text(frequency.rawValue).tag(frequency)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    Toggle("Add to Favorites", isOn: $isFavorite)
                }
                
                // Notes
                Section(header: Text("Notes")) {
                    TextField("Additional notes...", text: $personNotes, axis: .vertical)
                        .lineLimit(3...6)
                }
                
                // Preview
                Section(header: Text("Preview")) {
                    if !personName.isEmpty {
                        PersonPreviewCard(
                            name: personName,
                            role: personRole,
                            company: personCompany,
                            email: personEmail,
                            phone: personPhone,
                            location: personLocation,
                            relationshipType: selectedRelationshipType,
                            interactionFrequency: selectedInteractionFrequency,
                            isFavorite: isFavorite
                        )
                    } else {
                        Text("Enter name to see preview")
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }
            }
            .navigationTitle("Add Person")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add") {
                        addPerson()
                    }
                    .disabled(personName.isEmpty || isCreating)
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    /// Add the new person
    private func addPerson() {
        guard !personName.isEmpty else { return }
        
        isCreating = true
        
        let newPerson = Person(
            name: personName,
            role: personRole,
            company: personCompany,
            email: personEmail,
            phone: personPhone,
            avatarImageName: "person.circle.fill",
            isOnline: false,
            availability: .offline,
            relationshipType: selectedRelationshipType,
            interactionFrequency: selectedInteractionFrequency,
            location: personLocation,
            isFavorite: isFavorite
        )
        
        // Add notes if provided
        if !personNotes.isEmpty {
            newPerson.notes = personNotes
        }
        
        // Add to people manager
        peopleManager.addPerson(newPerson)
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // Dismiss with animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            dismiss()
        }
    }
}

/// Preview card for new person
struct PersonPreviewCard: View {
    let name: String
    let role: String
    let company: String
    let email: String
    let phone: String
    let location: String
    let relationshipType: Person.RelationshipType
    let interactionFrequency: Person.InteractionFrequency
    let isFavorite: Bool
    
    var contactInfo: String {
        var info: [String] = []
        if !role.isEmpty { info.append(role) }
        if !company.isEmpty { info.append(company) }
        return info.joined(separator: " at ")
    }
    
    var body: some View {
        VStack(spacing: 12) {
            // Avatar and basic info
            HStack(spacing: 12) {
                // Avatar
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                relationshipType.color.opacity(0.8),
                                relationshipType.color
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 50, height: 50)
                    .overlay(
                        Text(name.prefix(2).uppercased())
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(.white)
                    )
                
                // Info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(name)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        if isFavorite {
                            Image(systemName: "star.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                        }
                    }
                    
                    if !contactInfo.isEmpty {
                        Text(contactInfo)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // Relationship indicator
                VStack {
                    Image(systemName: relationshipType.icon)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(relationshipType.color)
                        .frame(width: 20, height: 20)
                        .background(
                            Circle()
                                .fill(relationshipType.color.opacity(0.1))
                        )
                    
                    Text(relationshipType.rawValue)
                        .font(.system(size: 8, weight: .medium))
                        .foregroundColor(.secondary)
                }
            }
            
            // Contact details
            if !email.isEmpty || !phone.isEmpty || !location.isEmpty {
                VStack(spacing: 6) {
                    if !email.isEmpty {
                        ContactPreviewRow(icon: "envelope", text: email, color: .blue)
                    }
                    
                    if !phone.isEmpty {
                        ContactPreviewRow(icon: "phone", text: phone, color: .green)
                    }
                    
                    if !location.isEmpty {
                        ContactPreviewRow(icon: "location", text: location, color: .red)
                    }
                }
            }
            
            // Interaction frequency
            HStack {
                Text("Interaction:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(interactionFrequency.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Spacer()
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(relationshipType.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

/// Contact preview row
struct ContactPreviewRow: View {
    let icon: String
    let text: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(color)
                .frame(width: 12)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

/// Edit person view (similar to add but with existing data)
struct EditPersonView: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var personName = ""
    @State private var personRole = ""
    @State private var personCompany = ""
    @State private var personEmail = ""
    @State private var personPhone = ""
    @State private var personLocation = ""
    @State private var selectedRelationshipType: Person.RelationshipType = .colleague
    @State private var selectedInteractionFrequency: Person.InteractionFrequency = .rarely
    @State private var personNotes = ""
    @State private var isFavorite = false
    @State private var isSaving = false
    
    var body: some View {
        NavigationView {
            Form {
                // Basic Information
                Section(header: Text("Basic Information")) {
                    TextField("Full Name", text: $personName)
                        .textContentType(.name)
                    
                    TextField("Role/Title", text: $personRole)
                        .textContentType(.jobTitle)
                    
                    TextField("Company", text: $personCompany)
                        .textContentType(.organizationName)
                }
                
                // Contact Information
                Section(header: Text("Contact Information")) {
                    TextField("Email", text: $personEmail)
                        .textContentType(.emailAddress)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                    
                    TextField("Phone", text: $personPhone)
                        .textContentType(.telephoneNumber)
                        .keyboardType(.phonePad)
                    
                    TextField("Location", text: $personLocation)
                        .textContentType(.location)
                }
                
                // Relationship Information
                Section(header: Text("Relationship")) {
                    Picker("Relationship Type", selection: $selectedRelationshipType) {
                        ForEach(Person.RelationshipType.allCases, id: \.self) { type in
                            HStack {
                                Image(systemName: type.icon)
                                    .foregroundColor(type.color)
                                Text(type.rawValue)
                            }
                            .tag(type)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    Picker("Interaction Frequency", selection: $selectedInteractionFrequency) {
                        ForEach(Person.InteractionFrequency.allCases, id: \.self) { frequency in
                            Text(frequency.rawValue).tag(frequency)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    
                    Toggle("Add to Favorites", isOn: $isFavorite)
                }
                
                // Notes
                Section(header: Text("Notes")) {
                    TextField("Additional notes...", text: $personNotes, axis: .vertical)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("Edit Person")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        savePerson()
                    }
                    .disabled(personName.isEmpty || isSaving)
                    .fontWeight(.semibold)
                }
            }
            .onAppear {
                loadPersonData()
            }
        }
    }
    
    /// Load existing person data
    private func loadPersonData() {
        personName = person.name
        personRole = person.role
        personCompany = person.company
        personEmail = person.email
        personPhone = person.phone
        personLocation = person.location
        selectedRelationshipType = person.relationshipType
        selectedInteractionFrequency = person.interactionFrequency
        personNotes = person.notes
        isFavorite = person.isFavorite
    }
    
    /// Save person changes
    private func savePerson() {
        guard !personName.isEmpty else { return }
        
        isSaving = true
        
        // Update person properties
        person.name = personName
        person.role = personRole
        person.company = personCompany
        person.email = personEmail
        person.phone = personPhone
        person.location = personLocation
        person.relationshipType = selectedRelationshipType
        person.interactionFrequency = selectedInteractionFrequency
        person.notes = personNotes
        person.isFavorite = isFavorite
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // Dismiss with animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            dismiss()
        }
    }
}

struct AddPersonView_Previews: PreviewProvider {
    static var previews: some View {
        let peopleManager = PeopleManager()
        return AddPersonView(peopleManager: peopleManager)
    }
}
