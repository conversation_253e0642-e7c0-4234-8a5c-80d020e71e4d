//
//  EnhancedTimelineModels.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 31/05/2025.
//

import SwiftUI
import Foundation

// MARK: - Enhanced Timeline Models for People & Team Integration
/// Revolutionary timeline models that integrate people interactions, team achievements, and social context

// MARK: - People Interaction Marker
struct PeopleInteractionMarker: Identifiable, Codable {
    let id = UUID()
    let personId: UUID
    let personName: String
    let interactionType: InteractionType
    let date: Date
    let weekNumber: Int
    let description: String
    let emotionalContext: EmotionalContext
    let duration: TimeInterval? // For meetings, calls, etc.
    let location: String?
    let isSignificant: Bool // For important interactions
    
    enum InteractionType: String, CaseIterable, Codable {
        case meeting = "Meeting"
        case call = "Call"
        case message = "Message"
        case collaboration = "Collaboration"
        case social = "Social"
        case milestone = "Milestone"
        case conflict = "Conflict"
        case celebration = "Celebration"
        
        var icon: String {
            switch self {
            case .meeting: return "person.2.fill"
            case .call: return "phone.fill"
            case .message: return "message.fill"
            case .collaboration: return "person.3.sequence.fill"
            case .social: return "heart.fill"
            case .milestone: return "star.fill"
            case .conflict: return "exclamationmark.triangle.fill"
            case .celebration: return "party.popper.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .meeting: return KeepsDesignSystem.Colors.primary
            case .call: return KeepsDesignSystem.Colors.success
            case .message: return KeepsDesignSystem.Colors.info
            case .collaboration: return KeepsDesignSystem.Colors.accent
            case .social: return KeepsDesignSystem.Colors.secondary
            case .milestone: return KeepsDesignSystem.Colors.warning
            case .conflict: return KeepsDesignSystem.Colors.error
            case .celebration: return KeepsDesignSystem.Colors.success
            }
        }
    }
    
    enum EmotionalContext: String, CaseIterable, Codable {
        case positive = "Positive"
        case neutral = "Neutral"
        case challenging = "Challenging"
        case inspiring = "Inspiring"
        case supportive = "Supportive"
        case transformative = "Transformative"
        
        var color: Color {
            switch self {
            case .positive: return .green
            case .neutral: return .gray
            case .challenging: return .orange
            case .inspiring: return .purple
            case .supportive: return .blue
            case .transformative: return .pink
            }
        }
        
        var emoji: String {
            switch self {
            case .positive: return "😊"
            case .neutral: return "😐"
            case .challenging: return "💪"
            case .inspiring: return "✨"
            case .supportive: return "🤝"
            case .transformative: return "🦋"
            }
        }
    }
}

// MARK: - Team Achievement Milestone
struct TeamAchievementMilestone: Identifiable, Codable {
    let id = UUID()
    let teamId: UUID
    let teamName: String
    let achievementTitle: String
    let description: String
    let date: Date
    let weekNumber: Int
    let achievementType: AchievementType
    let participants: [UUID] // Person IDs involved
    let impact: ImpactLevel
    let celebrationStyle: CelebrationStyle
    let isSharedMilestone: Bool // If it affects multiple people's timelines
    
    enum AchievementType: String, CaseIterable, Codable {
        case projectCompletion = "Project Completion"
        case teamFormation = "Team Formation"
        case goalAchievement = "Goal Achievement"
        case innovation = "Innovation"
        case collaboration = "Collaboration"
        case recognition = "Recognition"
        case breakthrough = "Breakthrough"
        case launch = "Launch"
        
        var icon: String {
            switch self {
            case .projectCompletion: return "checkmark.seal.fill"
            case .teamFormation: return "person.3.sequence.fill"
            case .goalAchievement: return "target"
            case .innovation: return "lightbulb.fill"
            case .collaboration: return "hands.sparkles.fill"
            case .recognition: return "trophy.fill"
            case .breakthrough: return "bolt.fill"
            case .launch: return "rocket.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .projectCompletion: return .green
            case .teamFormation: return .blue
            case .goalAchievement: return .purple
            case .innovation: return .orange
            case .collaboration: return .pink
            case .recognition: return .yellow
            case .breakthrough: return .red
            case .launch: return .cyan
            }
        }
    }
    
    enum ImpactLevel: String, CaseIterable, Codable {
        case personal = "Personal"
        case team = "Team"
        case organizational = "Organizational"
        case industry = "Industry"
        
        var priority: Int {
            switch self {
            case .personal: return 1
            case .team: return 2
            case .organizational: return 3
            case .industry: return 4
            }
        }
    }
    
    enum CelebrationStyle: String, CaseIterable, Codable {
        case confetti = "Confetti"
        case fireworks = "Fireworks"
        case sparkles = "Sparkles"
        case glow = "Glow"
        case pulse = "Pulse"
        case none = "None"
        
        var animationDuration: Double {
            switch self {
            case .confetti: return 3.0
            case .fireworks: return 2.5
            case .sparkles: return 2.0
            case .glow: return 1.5
            case .pulse: return 1.0
            case .none: return 0.0
            }
        }
    }
}

// MARK: - Cross-Section Milestone
struct CrossSectionMilestone: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let date: Date
    let weekNumber: Int
    let category: MilestoneCategory
    let connectedSections: [ConnectedSection]
    let significance: SignificanceLevel
    let visualStyle: VisualStyle
    
    enum MilestoneCategory: String, CaseIterable, Codable {
        case relationship = "Relationship"
        case career = "Career"
        case personal = "Personal"
        case team = "Team"
        case project = "Project"
        case learning = "Learning"
        case health = "Health"
        case travel = "Travel"
        
        var icon: String {
            switch self {
            case .relationship: return "heart.fill"
            case .career: return "briefcase.fill"
            case .personal: return "person.fill"
            case .team: return "person.3.fill"
            case .project: return "folder.fill"
            case .learning: return "book.fill"
            case .health: return "heart.text.square.fill"
            case .travel: return "airplane"
            }
        }
        
        var color: Color {
            switch self {
            case .relationship: return .pink
            case .career: return .blue
            case .personal: return .purple
            case .team: return .green
            case .project: return .orange
            case .learning: return .cyan
            case .health: return .red
            case .travel: return .indigo
            }
        }
    }
    
    enum ConnectedSection: String, CaseIterable, Codable {
        case people = "People"
        case teams = "Teams"
        case timeline = "Timeline"
        case projects = "Projects"
        
        var displayName: String {
            return rawValue
        }
    }
    
    enum SignificanceLevel: String, CaseIterable, Codable {
        case minor = "Minor"
        case moderate = "Moderate"
        case major = "Major"
        case life_changing = "Life Changing"
        
        var scale: CGFloat {
            switch self {
            case .minor: return 1.0
            case .moderate: return 1.2
            case .major: return 1.5
            case .life_changing: return 2.0
            }
        }
        
        var priority: Int {
            switch self {
            case .minor: return 1
            case .moderate: return 2
            case .major: return 3
            case .life_changing: return 4
            }
        }
    }
    
    enum VisualStyle: String, CaseIterable, Codable {
        case standard = "Standard"
        case highlighted = "Highlighted"
        case celebration = "Celebration"
        case memorial = "Memorial"
        
        var animation: String {
            switch self {
            case .standard: return "none"
            case .highlighted: return "glow"
            case .celebration: return "confetti"
            case .memorial: return "fade"
            }
        }
    }
}

// MARK: - Social Context Event
struct SocialContextEvent: Identifiable, Codable {
    let id = UUID()
    let title: String
    let description: String
    let date: Date
    let weekNumber: Int
    let involvedPeople: [UUID] // Person IDs
    let relationshipContext: RelationshipContext
    let eventType: SocialEventType
    let emotionalImpact: EmotionalImpact
    let isPrivate: Bool
    let tags: [String]
    
    enum RelationshipContext: String, CaseIterable, Codable {
        case family = "Family"
        case friends = "Friends"
        case colleagues = "Colleagues"
        case romantic = "Romantic"
        case mentorship = "Mentorship"
        case community = "Community"
        case professional = "Professional"
        case casual = "Casual"
        
        var icon: String {
            switch self {
            case .family: return "house.fill"
            case .friends: return "person.2.fill"
            case .colleagues: return "briefcase.fill"
            case .romantic: return "heart.fill"
            case .mentorship: return "graduationcap.fill"
            case .community: return "building.2.fill"
            case .professional: return "network"
            case .casual: return "person.wave.2.fill"
            }
        }
        
        var color: Color {
            switch self {
            case .family: return .orange
            case .friends: return .blue
            case .colleagues: return .green
            case .romantic: return .pink
            case .mentorship: return .purple
            case .community: return .cyan
            case .professional: return .indigo
            case .casual: return .gray
            }
        }
    }
    
    enum SocialEventType: String, CaseIterable, Codable {
        case gathering = "Gathering"
        case conversation = "Conversation"
        case collaboration = "Collaboration"
        case support = "Support"
        case conflict_resolution = "Conflict Resolution"
        case celebration = "Celebration"
        case learning = "Learning"
        case bonding = "Bonding"
        
        var icon: String {
            switch self {
            case .gathering: return "person.3.fill"
            case .conversation: return "bubble.left.and.bubble.right.fill"
            case .collaboration: return "hands.sparkles.fill"
            case .support: return "hand.raised.fill"
            case .conflict_resolution: return "handshake.fill"
            case .celebration: return "party.popper.fill"
            case .learning: return "brain.head.profile"
            case .bonding: return "heart.circle.fill"
            }
        }
    }
    
    enum EmotionalImpact: String, CaseIterable, Codable {
        case very_positive = "Very Positive"
        case positive = "Positive"
        case neutral = "Neutral"
        case challenging = "Challenging"
        case difficult = "Difficult"
        case transformative = "Transformative"
        
        var color: Color {
            switch self {
            case .very_positive: return .green
            case .positive: return .blue
            case .neutral: return .gray
            case .challenging: return .orange
            case .difficult: return .red
            case .transformative: return .purple
            }
        }
        
        var intensity: Double {
            switch self {
            case .very_positive: return 1.0
            case .positive: return 0.8
            case .neutral: return 0.3
            case .challenging: return 0.6
            case .difficult: return 0.9
            case .transformative: return 1.0
            }
        }
    }
}

// MARK: - Collaborative Timeline Entry
struct CollaborativeTimelineEntry: Identifiable, Codable {
    let id = UUID()
    let weekNumber: Int
    let sharedWith: [UUID] // Person/Team IDs
    let collaborativeContent: CollaborativeContent
    let permissions: SharingPermissions
    let lastUpdated: Date
    let contributors: [UUID] // Who has contributed
    
    struct CollaborativeContent: Codable {
        var sharedTitle: String
        var sharedInsights: [String]
        var sharedAccomplishments: [String]
        var teamReflections: [TeamReflection]
        var groupGoals: [String]
    }
    
    struct TeamReflection: Identifiable, Codable {
        let id = UUID()
        let contributorId: UUID
        let contributorName: String
        let reflection: String
        let timestamp: Date
        let isPublic: Bool
    }
    
    enum SharingPermissions: String, CaseIterable, Codable {
        case view_only = "View Only"
        case comment = "Comment"
        case edit = "Edit"
        case admin = "Admin"
        
        var canEdit: Bool {
            return self == .edit || self == .admin
        }
        
        var canComment: Bool {
            return self != .view_only
        }
    }
}
