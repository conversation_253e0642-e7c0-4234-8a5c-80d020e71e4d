//
//  AccessibilityEnhancements.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI

// MARK: - Accessibility Manager

/// Centralized accessibility configuration and utilities
class AccessibilityManager: ObservableObject {
    static let shared = AccessibilityManager()
    
    @Published var isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
    @Published var isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
    @Published var isHighContrastEnabled = UIAccessibility.isDarkerSystemColorsEnabled
    @Published var preferredContentSizeCategory = UIApplication.shared.preferredContentSizeCategory
    
    private init() {
        setupNotifications()
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            forName: UIAccessibility.voiceOverStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
        }
        
        NotificationCenter.default.addObserver(
            forName: UIAccessibility.reduceMotionStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
        }
        
        NotificationCenter.default.addObserver(
            forName: UIContentSizeCategory.didChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.preferredContentSizeCategory = UIApplication.shared.preferredContentSizeCategory
        }
    }
    
    /// Get appropriate animation duration based on accessibility settings
    func animationDuration(_ defaultDuration: Double) -> Double {
        return isReduceMotionEnabled ? 0.1 : defaultDuration
    }
    
    /// Get appropriate spring animation based on accessibility settings
    func springAnimation(response: Double = 0.3, dampingFraction: Double = 0.8) -> Animation {
        if isReduceMotionEnabled {
            return .easeInOut(duration: 0.1)
        } else {
            return .spring(response: response, dampingFraction: dampingFraction)
        }
    }
    
    /// Check if large text is enabled
    var isLargeTextEnabled: Bool {
        preferredContentSizeCategory.isAccessibilityCategory
    }
    
    /// Get scaled font size
    func scaledFont(_ font: Font) -> Font {
        return font
    }
}

// MARK: - Accessibility Modifiers

extension View {
    /// Add comprehensive accessibility support to any view
    func accessibilityEnhanced(
        label: String,
        hint: String? = nil,
        value: String? = nil,
        traits: AccessibilityTraits = [],
        defaultAction: (() -> Void)? = nil
    ) -> some View {
        var view = self
            .accessibilityLabel(label)
            .accessibilityHint(hint ?? "")
            .accessibilityValue(value ?? "")
            .accessibilityAddTraits(traits)

        if let defaultAction = defaultAction {
            view = view.accessibilityAction(.default, defaultAction)
        }

        return view
    }
    
    /// Add dynamic type support
    func dynamicTypeSupport() -> some View {
        self.modifier(DynamicTypeModifier())
    }
    
    /// Add high contrast support
    func highContrastSupport(
        normalColor: Color,
        highContrastColor: Color
    ) -> some View {
        self.modifier(HighContrastModifier(
            normalColor: normalColor,
            highContrastColor: highContrastColor
        ))
    }
    
    /// Add reduced motion support
    func reducedMotionSupport<T: Equatable>(
        value: T,
        normalAnimation: Animation,
        reducedAnimation: Animation = .easeInOut(duration: 0.1)
    ) -> some View {
        self.modifier(ReducedMotionModifier(
            value: value,
            normalAnimation: normalAnimation,
            reducedAnimation: reducedAnimation
        ))
    }
}

// MARK: - Accessibility Modifiers Implementation

struct DynamicTypeModifier: ViewModifier {
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared

    func body(content: Content) -> some View {
        content
            .environment(\.sizeCategory, ContentSizeCategory(accessibilityManager.preferredContentSizeCategory) ?? .medium)
            .lineLimit(accessibilityManager.isLargeTextEnabled ? nil : 2)
    }
}

struct HighContrastModifier: ViewModifier {
    let normalColor: Color
    let highContrastColor: Color
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    
    func body(content: Content) -> some View {
        content
            .foregroundColor(accessibilityManager.isHighContrastEnabled ? highContrastColor : normalColor)
    }
}

struct ReducedMotionModifier<T: Equatable>: ViewModifier {
    let value: T
    let normalAnimation: Animation
    let reducedAnimation: Animation
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    
    func body(content: Content) -> some View {
        content
            .animation(
                accessibilityManager.isReduceMotionEnabled ? reducedAnimation : normalAnimation,
                value: value
            )
    }
}

// MARK: - Accessible Components

/// Accessible button with enhanced feedback
struct AccessibleButton<Content: View>: View {
    let action: () -> Void
    let content: Content
    let accessibilityLabel: String
    let accessibilityHint: String?
    
    @State private var isPressed = false
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    
    init(
        action: @escaping () -> Void,
        accessibilityLabel: String,
        accessibilityHint: String? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.action = action
        self.accessibilityLabel = accessibilityLabel
        self.accessibilityHint = accessibilityHint
        self.content = content()
    }
    
    var body: some View {
        Button(action: {
            // Enhanced haptic feedback for accessibility
            if accessibilityManager.isVoiceOverEnabled {
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }
            action()
        }) {
            content
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .opacity(isPressed ? 0.8 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint ?? "")
        .accessibilityAddTraits(.isButton)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(accessibilityManager.springAnimation()) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

/// Accessible text field with enhanced support
struct AccessibleTextField: View {
    @Binding var text: String
    let placeholder: String
    let accessibilityLabel: String
    let accessibilityHint: String?
    
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    
    init(
        text: Binding<String>,
        placeholder: String,
        accessibilityLabel: String,
        accessibilityHint: String? = nil
    ) {
        self._text = text
        self.placeholder = placeholder
        self.accessibilityLabel = accessibilityLabel
        self.accessibilityHint = accessibilityHint
    }
    
    var body: some View {
        TextField(placeholder, text: $text)
            .textFieldStyle(RoundedBorderTextFieldStyle())
            .accessibilityLabel(accessibilityLabel)
            .accessibilityHint(accessibilityHint ?? "")
            .accessibilityValue(text.isEmpty ? "Empty" : text)
            .dynamicTypeSupport()
    }
}

/// Accessible progress indicator
struct AccessibleProgressView: View {
    let progress: Double
    let accessibilityLabel: String
    
    var body: some View {
        ProgressView(value: progress)
            .accessibilityLabel(accessibilityLabel)
            .accessibilityValue("\(Int(progress * 100)) percent complete")
            .accessibilityAddTraits(.updatesFrequently)
    }
}

/// Accessible toggle with enhanced feedback
struct AccessibleToggle: View {
    @Binding var isOn: Bool
    let label: String
    let accessibilityHint: String?
    
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    
    init(
        isOn: Binding<Bool>,
        label: String,
        accessibilityHint: String? = nil
    ) {
        self._isOn = isOn
        self.label = label
        self.accessibilityHint = accessibilityHint
    }
    
    var body: some View {
        Toggle(label, isOn: $isOn)
            .accessibilityLabel(label)
            .accessibilityHint(accessibilityHint ?? "")
            .accessibilityValue(isOn ? "On" : "Off")
            .onChange(of: isOn) { _, newValue in
                if accessibilityManager.isVoiceOverEnabled {
                    let feedbackGenerator = UISelectionFeedbackGenerator()
                    feedbackGenerator.selectionChanged()
                    
                    // Announce state change
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        UIAccessibility.post(
                            notification: .announcement,
                            argument: "\(label) \(newValue ? "enabled" : "disabled")"
                        )
                    }
                }
            }
    }
}

// MARK: - Accessibility Announcements

extension View {
    /// Post accessibility announcement
    func accessibilityAnnouncement(_ message: String, delay: Double = 0.1) -> some View {
        self.onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                UIAccessibility.post(notification: .announcement, argument: message)
            }
        }
    }
    
    /// Post screen changed announcement
    func accessibilityScreenChanged(_ message: String? = nil) -> some View {
        self.onAppear {
            UIAccessibility.post(
                notification: .screenChanged,
                argument: message
            )
        }
    }
}
