//
//  AutomationManager.swift
//  Keeps
//
//  Advanced automation system for intelligent workflow triggers and automated actions
//  Reduces repetitive tasks and enhances user productivity through smart automation
//

import SwiftUI
import Combine
import UserNotifications

// MARK: - Automation Manager

/// Manages intelligent automation triggers and automated workflows
class AutomationManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var activeAutomations: [AutomationRule] = []
    @Published var automationHistory: [AutomationExecution] = []
    @Published var isAutomationEnabled = true
    @Published var automationLevel: AutomationLevel = .balanced
    
    // MARK: - Automation Statistics
    
    @Published var totalAutomationsExecuted = 0
    @Published var timeSavedMinutes = 0
    @Published var automationSuccessRate: Double = 0.0
    @Published var lastAutomationDate: Date?
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    private let notificationManager = NotificationManager()
    private let workflowTrigger = WorkflowTrigger()
    private let smartReminders = SmartReminderSystem()
    
    // MARK: - Initialization
    
    init() {
        setupAutomationObservation()
        loadSavedAutomations()
        startAutomationEngine()
    }
    
    // MARK: - Public Methods
    
    /// Create a new automation rule
    func createAutomation(_ rule: AutomationRule) {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            activeAutomations.append(rule)
            saveAutomations()
        }
        
        print("🤖 Created automation: \(rule.name)")
    }
    
    /// Enable or disable an automation rule
    func toggleAutomation(_ ruleId: UUID, enabled: Bool) {
        if let index = activeAutomations.firstIndex(where: { $0.id == ruleId }) {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                activeAutomations[index].isEnabled = enabled
                saveAutomations()
            }
        }
    }
    
    /// Delete an automation rule
    func deleteAutomation(_ ruleId: UUID) {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            activeAutomations.removeAll { $0.id == ruleId }
            saveAutomations()
        }
    }
    
    /// Execute automation based on trigger
    func executeAutomation(for trigger: AutomationTrigger, context: AutomationContext) {
        guard isAutomationEnabled else { return }
        
        let matchingRules = activeAutomations.filter { rule in
            rule.isEnabled && rule.trigger.matches(trigger) && rule.shouldExecute(context: context)
        }
        
        for rule in matchingRules {
            Task {
                await performAutomation(rule: rule, context: context)
            }
        }
    }
    
    /// Generate automation suggestions based on user patterns
    func generateAutomationSuggestions(patterns: UserBehaviorPatterns) -> [AutomationSuggestion] {
        var suggestions: [AutomationSuggestion] = []
        
        // Workflow automation suggestions
        for workflowPattern in patterns.commonWorkflows {
            if workflowPattern.frequency > 5 && workflowPattern.successRate > 0.8 {
                suggestions.append(AutomationSuggestion(
                    type: .workflowAutomation,
                    title: "Auto-start \(workflowPattern.workflowType.rawValue)",
                    description: "Automatically suggest this workflow based on your schedule",
                    confidence: workflowPattern.successRate,
                    automationType: .workflowTrigger(workflowPattern.workflowType)
                ))
            }
        }
        
        // Communication automation suggestions
        for collaborator in patterns.frequentCollaborators.prefix(3) {
            if collaborator.interactionCount > 15 {
                suggestions.append(AutomationSuggestion(
                    type: .communicationAutomation,
                    title: "Auto-remind for \(collaborator.personId)",
                    description: "Set up automatic reminders to stay connected",
                    confidence: min(0.9, Double(collaborator.interactionCount) / 20.0),
                    automationType: .reminderAutomation(collaborator.personId)
                ))
            }
        }
        
        // Goal automation suggestions
        for goalCategory in patterns.goalCategories {
            if goalCategory.frequency > 3 && goalCategory.completionRate > 0.7 {
                suggestions.append(AutomationSuggestion(
                    type: .goalAutomation,
                    title: "Auto-suggest \(goalCategory.name) goals",
                    description: "Automatically suggest goals in this successful category",
                    confidence: goalCategory.completionRate,
                    automationType: .goalSuggestion(goalCategory.name)
                ))
            }
        }
        
        // Daily routine automation
        if patterns.dailyInteractionCount > 10 {
            suggestions.append(AutomationSuggestion(
                type: .routineAutomation,
                title: "Daily check-in automation",
                description: "Automatically prompt for daily check-ins at your peak usage time",
                confidence: 0.8,
                automationType: .dailyCheckInReminder
            ))
        }
        
        return suggestions.sorted { $0.confidence > $1.confidence }
    }
    
    /// Create automation from suggestion
    func createAutomationFromSuggestion(_ suggestion: AutomationSuggestion) {
        let rule = AutomationRule(
            name: suggestion.title,
            description: suggestion.description,
            trigger: suggestion.automationType.defaultTrigger,
            actions: suggestion.automationType.defaultActions,
            conditions: [],
            isEnabled: true
        )
        
        createAutomation(rule)
    }
    
    // MARK: - Private Methods
    
    private func setupAutomationObservation() {
        // Observe user interactions for automation triggers
        NotificationCenter.default.publisher(for: .userInteraction)
            .sink { [weak self] notification in
                self?.handleUserInteraction(notification)
            }
            .store(in: &cancellables)
        
        // Observe workflow completions
        NotificationCenter.default.publisher(for: .workflowCompleted)
            .sink { [weak self] notification in
                self?.handleWorkflowCompletion(notification)
            }
            .store(in: &cancellables)
        
        // Observe time-based triggers
        Timer.publish(every: 60, on: .main, in: .common) // Every minute
            .autoconnect()
            .sink { [weak self] _ in
                self?.checkTimeBasedTriggers()
            }
            .store(in: &cancellables)
    }
    
    private func loadSavedAutomations() {
        // Load automations from UserDefaults or Core Data
        if let data = UserDefaults.standard.data(forKey: "savedAutomations"),
           let automations = try? JSONDecoder().decode([AutomationRule].self, from: data) {
            activeAutomations = automations
        }
    }
    
    private func saveAutomations() {
        if let data = try? JSONEncoder().encode(activeAutomations) {
            UserDefaults.standard.set(data, forKey: "savedAutomations")
        }
    }
    
    private func startAutomationEngine() {
        print("🤖 Automation engine started with \(activeAutomations.count) rules")
    }
    
    private func handleUserInteraction(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let interaction = userInfo["interaction"] as? UserInteraction else { return }
        
        let trigger = AutomationTrigger.userAction(interaction.type)
        let context = AutomationContext(
            timestamp: Date(),
            userInteraction: interaction,
            currentSection: interaction.section
        )
        
        executeAutomation(for: trigger, context: context)
    }
    
    private func handleWorkflowCompletion(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let workflowType = userInfo["workflowType"] as? WorkflowType else { return }
        
        let trigger = AutomationTrigger.workflowCompleted(workflowType.rawValue)
        let context = AutomationContext(
            timestamp: Date(),
            workflowType: workflowType,
            currentSection: .workflow
        )
        
        executeAutomation(for: trigger, context: context)
    }
    
    private func checkTimeBasedTriggers() {
        let now = Date()
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: now)
        let minute = calendar.component(.minute, from: now)
        let weekday = calendar.component(.weekday, from: now)
        
        // Check for time-based triggers
        let timeTrigger = AutomationTrigger.timeOfDay(hour: hour, minute: minute)
        let dayTrigger = AutomationTrigger.dayOfWeek(weekday)
        
        let context = AutomationContext(
            timestamp: now,
            currentSection: .general
        )
        
        executeAutomation(for: timeTrigger, context: context)
        executeAutomation(for: dayTrigger, context: context)
    }
    
    private func performAutomation(rule: AutomationRule, context: AutomationContext) async {
        var execution = AutomationExecution(
            ruleId: rule.id,
            ruleName: rule.name,
            trigger: rule.trigger,
            executedAt: Date(),
            context: context
        )
        
        var success = true
        
        // Execute each action in the rule
        for action in rule.actions {
            do {
                try await executeAction(action, context: context)
                print("✅ Executed automation action: \(action.type)")
            } catch {
                print("❌ Failed to execute automation action: \(action.type) - \(error)")
                success = false
            }
        }
        
        // Record execution
        await MainActor.run {
            execution.isSuccessful = success
            automationHistory.append(execution)
            
            if success {
                totalAutomationsExecuted += 1
                timeSavedMinutes += rule.estimatedTimeSaved
                lastAutomationDate = Date()
            }
            
            updateAutomationSuccessRate()
        }
    }
    
    private func executeAction(_ action: AutomationAction, context: AutomationContext) async throws {
        switch action.type {
        case .showNotification:
            await notificationManager.showNotification(
                title: action.parameters["title"] as? String ?? "Automation",
                body: action.parameters["body"] as? String ?? "Automated action executed"
            )
            
        case .startWorkflow:
            if let workflowTypeString = action.parameters["workflowType"] as? String,
               let workflowType = WorkflowType(rawValue: workflowTypeString) {
                await workflowTrigger.startWorkflow(workflowType)
            }
            
        case .createReminder:
            if let title = action.parameters["title"],
               let dateString = action.parameters["date"] {
                let date = Date() // Use current date as fallback
                await smartReminders.createReminder(title: title, date: date)
            }
            
        case .updateStatus:
            if let status = action.parameters["status"] as? String {
                await updateUserStatus(status)
            }
            
        case .sendMessage:
            if let personIdString = action.parameters["personId"],
               let message = action.parameters["message"],
               let personId = UUID(uuidString: personIdString) {
                await sendAutomatedMessage(to: personId, message: message)
            }
            
        case .createGoal:
            if let title = action.parameters["title"] as? String,
               let category = action.parameters["category"] as? String {
                await createAutomatedGoal(title: title, category: category)
            }
            
        case .scheduleCheckIn:
            await scheduleAutomatedCheckIn()
        }
    }
    
    private func updateAutomationSuccessRate() {
        let totalExecutions = automationHistory.count
        let successfulExecutions = automationHistory.filter { $0.isSuccessful }.count
        
        if totalExecutions > 0 {
            automationSuccessRate = Double(successfulExecutions) / Double(totalExecutions)
        }
    }
    
    // MARK: - Action Implementations
    
    private func updateUserStatus(_ status: String) async {
        // Implementation for updating user status
        print("📝 Updated user status: \(status)")
    }
    
    private func sendAutomatedMessage(to personId: UUID, message: String) async {
        // Implementation for sending automated message
        print("💬 Sent automated message to \(personId): \(message)")
    }
    
    private func createAutomatedGoal(title: String, category: String) async {
        // Implementation for creating automated goal
        print("🎯 Created automated goal: \(title) in \(category)")
    }
    
    private func scheduleAutomatedCheckIn() async {
        // Implementation for scheduling automated check-in
        print("📅 Scheduled automated check-in")
    }
}

// MARK: - Automation Models

/// Represents an automation rule
struct AutomationRule: Identifiable, Codable {
    let id: UUID
    var name: String
    var description: String
    var trigger: AutomationTrigger
    var actions: [AutomationAction]
    var conditions: [AutomationCondition]
    var isEnabled: Bool
    var createdAt: Date
    var lastExecuted: Date?
    var executionCount = 0
    var estimatedTimeSaved = 5 // minutes

    init(name: String, description: String, trigger: AutomationTrigger, actions: [AutomationAction], conditions: [AutomationCondition] = [], isEnabled: Bool = true) {
        self.id = UUID()
        self.name = name
        self.description = description
        self.trigger = trigger
        self.actions = actions
        self.conditions = conditions
        self.isEnabled = isEnabled
        self.createdAt = Date()
        self.lastExecuted = nil
    }
    
    func shouldExecute(context: AutomationContext) -> Bool {
        // Check if all conditions are met
        return conditions.allSatisfy { condition in
            condition.evaluate(context: context)
        }
    }
}

/// Automation triggers
enum AutomationTrigger: Codable {
    case userAction(BehaviorInteractionType)
    case timeOfDay(hour: Int, minute: Int)
    case dayOfWeek(Int)
    case workflowCompleted(String) // WorkflowType as String
    case goalCompleted(String) // UUID as String
    case personAdded(String) // UUID as String
    case teamCreated(String) // UUID as String
    case dataThreshold(String, Double) // metric name, threshold value
    
    func matches(_ other: AutomationTrigger) -> Bool {
        switch (self, other) {
        case (.userAction(let a), .userAction(let b)):
            return a == b
        case (.timeOfDay(let h1, let m1), .timeOfDay(let h2, let m2)):
            return h1 == h2 && m1 == m2
        case (.dayOfWeek(let d1), .dayOfWeek(let d2)):
            return d1 == d2
        case (.workflowCompleted(let w1), .workflowCompleted(let w2)):
            return w1 == w2
        default:
            return false
        }
    }
}

/// Automation actions
struct AutomationAction: Codable {
    let type: AutomationActionType
    let parameters: [String: String]
    let delay: TimeInterval // seconds to wait before executing
    
    init(type: AutomationActionType, parameters: [String: String] = [:], delay: TimeInterval = 0) {
        self.type = type
        self.parameters = parameters
        self.delay = delay
    }
}

/// Types of automation actions
enum AutomationActionType: String, Codable, CaseIterable {
    case showNotification = "Show Notification"
    case startWorkflow = "Start Workflow"
    case createReminder = "Create Reminder"
    case updateStatus = "Update Status"
    case sendMessage = "Send Message"
    case createGoal = "Create Goal"
    case scheduleCheckIn = "Schedule Check-in"
    
    var icon: String {
        switch self {
        case .showNotification: return "bell.fill"
        case .startWorkflow: return "arrow.triangle.branch"
        case .createReminder: return "alarm.fill"
        case .updateStatus: return "checkmark.circle.fill"
        case .sendMessage: return "message.fill"
        case .createGoal: return "target"
        case .scheduleCheckIn: return "calendar.badge.checkmark"
        }
    }
}

/// Automation conditions
struct AutomationCondition: Codable {
    let type: ConditionType
    let value: String
    let conditionOperator: ConditionOperator
    
    func evaluate(context: AutomationContext) -> Bool {
        // Implementation for evaluating conditions
        return true // Placeholder
    }
}

enum ConditionType: String, Codable {
    case timeRange = "Time Range"
    case dayOfWeek = "Day of Week"
    case sectionActive = "Section Active"
    case dataValue = "Data Value"
}

enum ConditionOperator: String, Codable {
    case equals = "Equals"
    case greaterThan = "Greater Than"
    case lessThan = "Less Than"
    case contains = "Contains"
}

/// Context for automation execution
struct AutomationContext {
    let timestamp: Date
    var userInteraction: UserInteraction?
    var workflowType: WorkflowType?
    var currentSection: AppSection
    var additionalData: [String: Any] = [:]
}

/// Record of automation execution
struct AutomationExecution: Identifiable {
    let id = UUID()
    let ruleId: UUID
    let ruleName: String
    let trigger: AutomationTrigger
    let executedAt: Date
    let context: AutomationContext
    var isSuccessful = false
    var errorMessage: String?

    init(ruleId: UUID, ruleName: String, trigger: AutomationTrigger, executedAt: Date, context: AutomationContext) {
        self.ruleId = ruleId
        self.ruleName = ruleName
        self.trigger = trigger
        self.executedAt = executedAt
        self.context = context
    }
}

/// Automation levels
enum AutomationLevel: String, CaseIterable {
    case minimal = "Minimal"
    case balanced = "Balanced"
    case aggressive = "Aggressive"
    
    var description: String {
        switch self {
        case .minimal: return "Only essential automations"
        case .balanced: return "Helpful automations without being intrusive"
        case .aggressive: return "Maximum automation for efficiency"
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let workflowCompleted = Notification.Name("workflowCompleted")
}
