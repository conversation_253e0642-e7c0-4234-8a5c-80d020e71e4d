//
//  PersonDetailView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Modern person detail view with horizontal page navigation
/// Features clean design, swipe gestures, and professional layout
struct PersonDetailView: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager
    @Environment(\.dismiss) private var dismiss
    @State private var showingEditPerson = false
    @State private var selectedPage = 0
    @State private var dragOffset: CGFloat = 0
    @State private var isLoading = false
    @State private var currentError: KeepsError?
    @State private var isEditingInline = false

    // Performance and accessibility
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    @ObservedObject private var errorManager = ErrorManager.shared

    var body: some View {
        NavigationView {
            ZStack {
                // Dynamic background with subtle blur
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        person.relationshipType.color.opacity(0.02),
                        person.relationshipType.color.opacity(0.05)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header with blur background
                    PersonDetailHeader(person: person, peopleManager: peopleManager)
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                        .padding(.bottom, 16)
                        .background(
                            .ultraThinMaterial,
                            in: RoundedRectangle(cornerRadius: 0)
                        )
                        .overlay(
                            Rectangle()
                                .fill(person.relationshipType.color.opacity(0.1))
                                .frame(height: 1),
                            alignment: .bottom
                        )

                    // Page content with horizontal swiping
                    TabView(selection: $selectedPage) {
                        // Page 1: Profile Overview with inline editing
                        PersonProfilePage(person: person, isEditing: $isEditingInline)
                            .tag(0)

                        // Page 2: Enhanced Notes Interface
                        EnhancedNotesInterface(person: person, peopleManager: peopleManager)
                            .tag(1)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .animation(.easeInOut(duration: 0.3), value: selectedPage)

                    // Morphing slot-based page indicator
                    Spacer()
                }
            }
            .overlay(alignment: .bottomTrailing) {
                MorphingSlotIndicator(selectedPage: $selectedPage, person: person)
                    .padding(.trailing, 20)
                    .padding(.bottom, 30)
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle(isEditingInline ? "Edit Contact" : person.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(isEditingInline ? "Cancel" : "Close") {
                        if isEditingInline {
                            isEditingInline = false
                        } else {
                            // Close the full-screen view by setting selectedPerson to nil
                            peopleManager.selectedPerson = nil
                        }
                    }
                    .foregroundColor(person.relationshipType.color)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    if isEditingInline {
                        Button("Save") {
                            // Save changes and exit edit mode
                            peopleManager.savePeople()
                            isEditingInline = false

                            // Haptic feedback
                            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                            impactFeedback.impactOccurred()
                        }
                        .fontWeight(.semibold)
                    } else {
                        Menu {
                            Button(action: {
                                isEditingInline = true
                            }) {
                                Label("Edit Inline", systemImage: "pencil")
                            }

                            Button(action: {
                                showingEditPerson = true
                            }) {
                                Label("Edit in Sheet", systemImage: "pencil.circle")
                            }

                            Divider()

                            Button(action: {
                                peopleManager.toggleFavorite(for: person)
                            }) {
                                Label(
                                    person.isFavorite ? "Remove from Favorites" : "Add to Favorites",
                                    systemImage: person.isFavorite ? "star.slash" : "star.fill"
                                )
                            }
                        } label: {
                            Image(systemName: "ellipsis.circle")
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditPerson) {
            EditPersonView(person: person, peopleManager: peopleManager)
        }
        .loadingOverlay(
            isLoading: isLoading,
            message: "Loading person details...",
            showProgress: false
        )
        .errorHandling(
            error: $currentError,
            onRetry: {
                // Retry loading person data
                loadPersonData()
            }
        )
        .onAppear {
            // Load person data with error handling
            loadPersonData()
        }
        .accessibilityScreenChanged("Person details for \(person.name)")
    }

    // MARK: - Data Loading

    private func loadPersonData() {
        isLoading = true
        currentError = nil

        // Simulate data loading with potential errors
        Task {
            do {
                // Simulate network delay
                try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

                // Simulate potential errors
                if Bool.random() && person.notes.isEmpty {
                    throw KeepsError.networkError("Failed to load person details")
                }

                DispatchQueue.main.async {
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.isLoading = false
                    if let keepsError = error as? KeepsError {
                        self.currentError = keepsError
                    } else {
                        self.currentError = .unknownError(error.localizedDescription)
                    }
                }
            }
        }
    }
}

/// Enhanced header with custom avatar system
struct PersonDetailHeader: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager
    @State private var avatarScale: CGFloat = 1.0
    @State private var headerAnimation = false

    var body: some View {
        VStack(spacing: 16) {
            // Enhanced avatar and basic info
            HStack(spacing: 16) {
                // Enhanced avatar
                ZStack {
                    // Background with gradient
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    person.relationshipType.color,
                                    person.relationshipType.color.opacity(0.7)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .shadow(color: person.relationshipType.color.opacity(0.3), radius: 8, x: 0, y: 4)

                    // Avatar content
                    if person.avatarImageName.contains("person") {
                        Image(systemName: person.avatarImageName)
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(.white)
                    } else {
                        Text(person.name.prefix(2).uppercased())
                            .font(.system(size: 24, weight: .semibold, design: .rounded))
                            .foregroundColor(.white)
                    }

                    // Enhanced online indicator
                    if person.isOnline {
                        ZStack {
                            Circle()
                                .fill(.green)
                                .frame(width: 16, height: 16)
                                .overlay(Circle().stroke(.white, lineWidth: 2))

                            // Pulsing ring
                            Circle()
                                .stroke(.green.opacity(0.4), lineWidth: 2)
                                .frame(width: 20, height: 20)
                                .scaleEffect(headerAnimation ? 1.3 : 1.0)
                                .opacity(headerAnimation ? 0 : 1)
                                .animation(.easeOut(duration: 1.5).repeatForever(autoreverses: false), value: headerAnimation)
                        }
                        .offset(x: 28, y: -28)
                    }
                }
                .scaleEffect(avatarScale)

                // Name and role
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(person.name)
                            .font(.title2)
                            .fontWeight(.bold)

                        if person.isFavorite {
                            Image(systemName: "star.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.orange)
                        }
                    }

                    if !person.role.isEmpty {
                        Text(person.role)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    if !person.company.isEmpty {
                        Text(person.company)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    // Status badge
                    HStack(spacing: 6) {
                        Circle()
                            .fill(person.availability.color)
                            .frame(width: 6, height: 6)

                        Text(person.availability.rawValue)
                            .font(.caption)
                            .foregroundColor(person.availability.color)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(person.availability.color.opacity(0.1))
                    )
                }

                Spacer()

                // Favorite toggle
                Button(action: {
                    peopleManager.toggleFavorite(for: person)
                }) {
                    Image(systemName: person.isFavorite ? "heart.fill" : "heart")
                        .font(.system(size: 20))
                        .foregroundColor(person.isFavorite ? .red : .gray)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
        .onAppear {
            withAnimation(.easeInOut(duration: 2).repeatForever(autoreverses: false)) {
                headerAnimation = true
            }
        }
    }
}

/// Page 1: Profile Overview with contact info and inline editing
struct PersonProfilePage: View {
    @ObservedObject var person: Person
    @Binding var isEditing: Bool

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Contact Information Section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Contact Information")
                        .font(.headline)
                        .fontWeight(.semibold)

                    VStack(spacing: 12) {
                        if isEditing {
                            // Editable fields
                            EditableContactField(
                                icon: "person.fill",
                                title: "Name",
                                value: $person.name,
                                color: .blue
                            )

                            EditableContactField(
                                icon: "briefcase.fill",
                                title: "Role",
                                value: $person.role,
                                color: .purple
                            )

                            EditableContactField(
                                icon: "building.2.fill",
                                title: "Company",
                                value: $person.company,
                                color: .orange
                            )

                            EditableContactField(
                                icon: "envelope.fill",
                                title: "Email",
                                value: $person.email,
                                color: .blue
                            )

                            EditableContactField(
                                icon: "phone.fill",
                                title: "Phone",
                                value: $person.phone,
                                color: .green
                            )

                            EditableContactField(
                                icon: "location.fill",
                                title: "Location",
                                value: $person.location,
                                color: .red
                            )

                            // Relationship type picker
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Relationship Type")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)

                                Picker("Relationship Type", selection: $person.relationshipType) {
                                    ForEach(Person.RelationshipType.allCases, id: \.self) { type in
                                        HStack {
                                            Image(systemName: type.icon)
                                                .foregroundColor(type.color)
                                            Text(type.rawValue)
                                        }
                                        .tag(type)
                                    }
                                }
                                .pickerStyle(MenuPickerStyle())
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(.systemGray6))
                                )
                            }

                        } else {
                            // Display mode
                            if !person.email.isEmpty {
                                ContactInfoRow(
                                    icon: "envelope.fill",
                                    title: "Email",
                                    value: person.email,
                                    color: .blue,
                                    action: {
                                        if let url = URL(string: "mailto:\(person.email)") {
                                            UIApplication.shared.open(url)
                                        }
                                    }
                                )
                            }

                            if !person.phone.isEmpty {
                                ContactInfoRow(
                                    icon: "phone.fill",
                                    title: "Phone",
                                    value: person.phone,
                                    color: .green,
                                    action: {
                                        let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                                        if let url = URL(string: "tel:\(cleanPhone)") {
                                            UIApplication.shared.open(url)
                                        }
                                    }
                                )
                            }

                            if !person.location.isEmpty {
                                ContactInfoRow(
                                    icon: "location.fill",
                                    title: "Location",
                                    value: person.location,
                                    color: .red,
                                    action: nil
                                )
                            }
                        }
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
                )

                // Enhanced Relationship Visualization
                VStack(alignment: .leading, spacing: 16) {
                    Text("Relationship & Connection")
                        .font(.headline)
                        .fontWeight(.semibold)

                    VStack(spacing: 16) {
                        // Relationship type with visual indicator
                        HStack(spacing: 12) {
                            Image(systemName: person.relationshipType.icon)
                                .font(.system(size: 20))
                                .foregroundColor(person.relationshipType.color)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(person.relationshipType.color.opacity(0.1))
                                )

                            VStack(alignment: .leading, spacing: 2) {
                                Text("Relationship Type")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text(person.relationshipType.rawValue)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                            }

                            Spacer()
                        }

                        // Connection strength meter
                        ConnectionStrengthMeter(person: person)

                        // Interaction frequency visualization
                        InteractionFrequencyChart(person: person)

                        // Last contact indicator
                        LastContactIndicator(person: person)
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
                )

                // Quick Actions
                VStack(alignment: .leading, spacing: 16) {
                    Text("Quick Actions")
                        .font(.headline)
                        .fontWeight(.semibold)

                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        if !person.email.isEmpty {
                            QuickActionCard(
                                icon: "envelope.fill",
                                title: "Email",
                                color: .blue,
                                action: {
                                    if let url = URL(string: "mailto:\(person.email)") {
                                        UIApplication.shared.open(url)
                                    }
                                }
                            )
                        }

                        if !person.phone.isEmpty {
                            QuickActionCard(
                                icon: "phone.fill",
                                title: "Call",
                                color: .green,
                                action: {
                                    let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                                    if let url = URL(string: "tel:\(cleanPhone)") {
                                        UIApplication.shared.open(url)
                                    }
                                }
                            )
                        }

                        QuickActionCard(
                            icon: "message.fill",
                            title: "Message",
                            color: .purple,
                            action: {
                                if !person.phone.isEmpty {
                                    let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                                    if let url = URL(string: "sms:\(cleanPhone)") {
                                        UIApplication.shared.open(url)
                                    }
                                }
                            }
                        )

                        QuickActionCard(
                            icon: "calendar.badge.plus",
                            title: "Schedule",
                            color: .orange,
                            action: {
                                if let url = URL(string: "calshow://") {
                                    UIApplication.shared.open(url)
                                }
                            }
                        )
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
                )

                Spacer(minLength: 40)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
        }
    }
}

// Note: PersonNotesPage replaced by EnhancedNotesInterface

/// Morphing slot-based page indicator with sliding pill animation
struct MorphingSlotIndicator: View {
    @Binding var selectedPage: Int
    @ObservedObject var person: Person
    @State private var showInitialHint = false

    private let slotWidth: CGFloat = 70
    private let slotHeight: CGFloat = 36

    var body: some View {
        VStack(spacing: 10) {
            // Brief swipe hint on first appearance
            if showInitialHint {
                HStack(spacing: 4) {
                    Image(systemName: "hand.draw")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text("Swipe")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .opacity(showInitialHint ? 0.7 : 0.0)
                .animation(.easeInOut(duration: 0.5), value: showInitialHint)
            }

            // Main slot container with proper shape
            ZStack {
                // Background slot with perfect rounded rectangle
                Capsule()
                    .fill(.ultraThinMaterial)
                    .frame(width: slotWidth, height: slotHeight)
                    .overlay(
                        Capsule()
                            .stroke(person.relationshipType.color.opacity(0.15), lineWidth: 1.5)
                    )
                    .shadow(color: .black.opacity(0.08), radius: 6, x: 0, y: 3)

                // Active indicator background
                if selectedPage == 0 || selectedPage == 1 {
                    Capsule()
                        .fill(
                            LinearGradient(
                                colors: [
                                    person.relationshipType.color,
                                    person.relationshipType.color.opacity(0.85)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: slotWidth - 6, height: slotHeight - 6)
                        .shadow(color: person.relationshipType.color.opacity(0.3), radius: 4, x: 0, y: 2)
                        .transition(.scale.combined(with: .opacity))
                }

                // Icons container
                HStack(spacing: 0) {
                    // Profile indicator
                    MorphingIcon(
                        icon: "person.circle.fill",
                        isActive: selectedPage == 0,
                        color: person.relationshipType.color,
                        selectedPage: selectedPage,
                        isProfileIcon: true
                    ) {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            selectedPage = 0
                        }
                    }

                    // Notes indicator
                    MorphingIcon(
                        icon: "note.text",
                        isActive: selectedPage == 1,
                        color: person.relationshipType.color,
                        selectedPage: selectedPage,
                        isProfileIcon: false
                    ) {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            selectedPage = 1
                        }
                    }
                }
                .frame(width: slotWidth, height: slotHeight)
            }
        }
        .onAppear {
            // Show brief hint
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                showInitialHint = true
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    showInitialHint = false
                }
            }
        }
    }
}

/// Morphing icon that slides and changes size smoothly
struct MorphingIcon: View {
    let icon: String
    let isActive: Bool
    let color: Color
    let selectedPage: Int
    let isProfileIcon: Bool
    let action: () -> Void

    @State private var isPressed = false

    private var iconOffset: CGFloat {
        if isActive { return 0 }

        // Smooth sliding animation based on which page is active
        if isProfileIcon {
            // Profile icon slides right when notes is active
            return selectedPage == 1 ? 18 : 0
        } else {
            // Notes icon slides left when profile is active
            return selectedPage == 0 ? -18 : 0
        }
    }

    private var iconSize: CGFloat {
        isActive ? 18 : 11
    }

    private var iconOpacity: Double {
        isActive ? 1.0 : 0.4
    }

    private var iconScale: CGFloat {
        isActive ? 1.0 : 0.75
    }

    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: iconSize, weight: .medium))
                .foregroundColor(isActive ? .white : color.opacity(0.6))
                .scaleEffect(iconScale)
                .opacity(iconOpacity)
                .offset(x: iconOffset)
        }
        .buttonStyle(PlainButtonStyle())
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .scaleEffect(isPressed ? 0.9 : 1.0)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isActive)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: selectedPage)
        .animation(.spring(response: 0.2, dampingFraction: 0.7), value: isPressed)
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
    }
}

struct PageTab: View {
    let icon: String
    let title: String
    let subtitle: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: isSelected ? icon : icon.replacingOccurrences(of: ".fill", with: ""))
                    .font(.system(size: 20, weight: isSelected ? .semibold : .medium))
                    .foregroundColor(isSelected ? color : .secondary)
                    .scaleEffect(isSelected ? 1.1 : 1.0)

                VStack(spacing: 2) {
                    Text(title)
                        .font(.caption)
                        .fontWeight(isSelected ? .semibold : .medium)
                        .foregroundColor(isSelected ? color : .secondary)

                    Text(subtitle)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .opacity(isSelected ? 1.0 : 0.7)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? color.opacity(0.1) : Color.clear)
                    .animation(.easeInOut(duration: 0.2), value: isSelected)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isSelected)
    }
}

/// Enhanced page indicator with smooth animations and micro-interactions
struct PersonPageIndicator: View {
    @Binding var selectedPage: Int
    let person: Person
    @State private var buttonScales: [CGFloat] = [1.0, 1.0]
    @State private var indicatorOffset: CGFloat = 0

    var body: some View {
        ZStack {
            // Background
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemGray6))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)

            // Sliding indicator background
            GeometryReader { geometry in
                RoundedRectangle(cornerRadius: 16)
                    .fill(person.relationshipType.color.opacity(0.1))
                    .frame(width: geometry.size.width / 2 - 8, height: geometry.size.height - 8)
                    .offset(x: 4 + (selectedPage == 1 ? geometry.size.width / 2 : 0))
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: selectedPage)
            }

            HStack(spacing: 0) {
                // Page 1 indicator
                Button(action: {
                    // Haptic feedback
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                        buttonScales[0] = 0.95
                        selectedPage = 0
                    }

                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                            buttonScales[0] = 1.0
                        }
                    }
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: selectedPage == 0 ? "person.circle.fill" : "person.circle")
                            .font(.system(size: 20, weight: selectedPage == 0 ? .semibold : .medium))
                            .foregroundColor(selectedPage == 0 ? person.relationshipType.color : .secondary)
                            .scaleEffect(selectedPage == 0 ? 1.1 : 1.0)
                            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: selectedPage)

                        Text("Profile")
                            .font(.caption)
                            .fontWeight(selectedPage == 0 ? .semibold : .medium)
                            .foregroundColor(selectedPage == 0 ? person.relationshipType.color : .secondary)
                    }
                    .scaleEffect(buttonScales[0])
                }
                .frame(maxWidth: .infinity)
                .buttonStyle(PlainButtonStyle())

                // Page 2 indicator
                Button(action: {
                    // Haptic feedback
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                        buttonScales[1] = 0.95
                        selectedPage = 1
                    }

                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                            buttonScales[1] = 1.0
                        }
                    }
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: selectedPage == 1 ? "note.text" : "note")
                            .font(.system(size: 20, weight: selectedPage == 1 ? .semibold : .medium))
                            .foregroundColor(selectedPage == 1 ? person.relationshipType.color : .secondary)
                            .scaleEffect(selectedPage == 1 ? 1.1 : 1.0)
                            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: selectedPage)

                        Text("Notes")
                            .font(.caption)
                            .fontWeight(selectedPage == 1 ? .semibold : .medium)
                            .foregroundColor(selectedPage == 1 ? person.relationshipType.color : .secondary)
                    }
                    .scaleEffect(buttonScales[1])
                }
                .frame(maxWidth: .infinity)
                .buttonStyle(PlainButtonStyle())
            }
        }
        .frame(height: 60)
        .padding(.horizontal, 40)
    }
}

// Note: Enhanced notes interface components moved to EnhancedNotesInterface.swift

/// Note templates view
struct NoteTemplatesView: View {
    let selectedTemplate: (String) -> Void

    private let templates = [
        ("Meeting Notes", "📅 Meeting with [Name]\n\n**Date:** \n**Attendees:** \n**Topics Discussed:**\n• \n\n**Action Items:**\n• \n\n**Next Steps:**\n• "),
        ("Follow-up", "🔄 Follow-up Notes\n\n**Previous conversation:** \n**Key points to address:**\n• \n\n**Questions to ask:**\n• \n\n**Next contact date:** "),
        ("Personal Note", "💭 Personal Notes\n\n**About:** \n**Interests:** \n**Family:** \n**Hobbies:** \n\n**Remember for next time:**\n• "),
        ("Project Update", "📋 Project Update\n\n**Project:** \n**Status:** \n**Progress:**\n• \n\n**Challenges:**\n• \n\n**Next milestones:**\n• "),
        ("Quick Note", "📝 Quick Note\n\n")
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Note Templates")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(templates, id: \.0) { template in
                    Button(action: {
                        selectedTemplate(template.1)
                    }) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(template.0)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)

                            Text(String(template.1.prefix(30)) + "...")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4)
        )
    }
}

/// Empty notes view
struct EmptyNotesView: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "note.text")
                .font(.system(size: 32))
                .foregroundColor(.secondary)

            Text("No notes yet")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            Text("Tap Edit to add your first note or use a template")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(24)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

/// Notes display with search highlighting
struct NotesDisplayView: View {
    let notes: String
    let searchText: String

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Note metadata
            HStack {
                Text("Last updated")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                Text(Date().formatted(date: .abbreviated, time: .shortened))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            // Notes content with search highlighting
            if searchText.isEmpty {
                Text(notes)
                    .font(.body)
                    .foregroundColor(.primary)
            } else {
                // Simple search highlighting (in a real app, you'd use AttributedString)
                Text(highlightSearchText(in: notes, searchText: searchText))
                    .font(.body)
                    .foregroundColor(.primary)
            }
        }
        .padding(16)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }

    private func highlightSearchText(in text: String, searchText: String) -> String {
        // Simple highlighting - in a real app you'd use AttributedString
        return text.replacingOccurrences(
            of: searchText,
            with: "🔍\(searchText)🔍",
            options: .caseInsensitive
        )
    }
}

/// Connection strength meter with animated progress
struct ConnectionStrengthMeter: View {
    @ObservedObject var person: Person
    @State private var animatedProgress: Double = 0

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Connection Strength")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(Int(person.interactionFrequency.emotionalIntensity * 100))%")
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(person.relationshipType.color)
            }

            // Animated progress bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color(.systemGray5))
                        .frame(height: 12)

                    // Progress with gradient
                    RoundedRectangle(cornerRadius: 6)
                        .fill(
                            LinearGradient(
                                colors: [
                                    person.relationshipType.color.opacity(0.6),
                                    person.relationshipType.color
                                ],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(
                            width: geometry.size.width * animatedProgress,
                            height: 12
                        )
                        .animation(.spring(response: 1.0, dampingFraction: 0.8), value: animatedProgress)

                    // Pulse effect for high connection
                    if person.interactionFrequency.emotionalIntensity > 0.7 {
                        RoundedRectangle(cornerRadius: 6)
                            .fill(person.relationshipType.color.opacity(0.3))
                            .frame(
                                width: geometry.size.width * animatedProgress,
                                height: 12
                            )
                            .scaleEffect(1.1)
                            .opacity(0.5)
                            .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: animatedProgress)
                    }
                }
            }
            .frame(height: 12)
            .onAppear {
                withAnimation(.spring(response: 1.2, dampingFraction: 0.8).delay(0.3)) {
                    animatedProgress = person.interactionFrequency.emotionalIntensity
                }
            }

            // Connection description
            Text(person.interactionFrequency.emotionalDescription)
                .font(.caption)
                .foregroundColor(person.relationshipType.color)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(person.relationshipType.color.opacity(0.1))
                )
        }
    }
}

/// Interaction frequency chart with visual bars
struct InteractionFrequencyChart: View {
    @ObservedObject var person: Person
    @State private var animatedBars: [Double] = [0, 0, 0, 0]

    private let timeFrames = ["Week", "Month", "Quarter", "Year"]
    private var frequencyData: [Double] {
        switch person.interactionFrequency {
        case .daily: return [1.0, 0.9, 0.8, 0.7]
        case .weekly: return [0.8, 1.0, 0.9, 0.8]
        case .monthly: return [0.3, 0.7, 1.0, 0.9]
        case .rarely: return [0.1, 0.3, 0.5, 1.0]
        case .never: return [0.0, 0.1, 0.2, 0.3]
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Interaction Pattern")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            HStack(spacing: 8) {
                ForEach(0..<4, id: \.self) { index in
                    VStack(spacing: 4) {
                        // Bar chart
                        RoundedRectangle(cornerRadius: 3)
                            .fill(person.relationshipType.color.opacity(0.7))
                            .frame(width: 20, height: max(4, animatedBars[index] * 40))
                            .animation(.spring(response: 0.8, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animatedBars[index])

                        // Label
                        Text(timeFrames[index])
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Current frequency indicator
                VStack(alignment: .trailing, spacing: 2) {
                    Text("Current")
                        .font(.caption2)
                        .foregroundColor(.secondary)

                    Text(person.interactionFrequency.rawValue)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(person.relationshipType.color)
                }
            }
        }
        .onAppear {
            withAnimation(.spring(response: 1.0, dampingFraction: 0.8).delay(0.5)) {
                animatedBars = frequencyData
            }
        }
    }
}

/// Last contact indicator with time visualization
struct LastContactIndicator: View {
    @ObservedObject var person: Person

    private var daysSinceContact: Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: person.lastSeen, to: Date())
        return components.day ?? 0
    }

    private var contactStatusColor: Color {
        let days = daysSinceContact
        if days < 7 { return .green }
        else if days < 30 { return .orange }
        else { return .red }
    }

    var body: some View {
        HStack(spacing: 12) {
            // Status indicator
            Circle()
                .fill(contactStatusColor)
                .frame(width: 8, height: 8)
                .overlay(
                    Circle()
                        .stroke(contactStatusColor.opacity(0.3), lineWidth: 4)
                        .scaleEffect(1.5)
                        .opacity(0.6)
                        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: contactStatusColor)
                )

            VStack(alignment: .leading, spacing: 2) {
                Text("Last Contact")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(person.timeSinceLastSeen)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(contactStatusColor)
            }

            Spacer()

            // Quick contact suggestion
            if daysSinceContact > 14 {
                Text("Consider reaching out")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(Color.orange.opacity(0.1))
                    )
            }
        }
    }
}

/// Enhanced tag cloud with dynamic sizing
struct TagCloudView: View {
    let tags: [String]
    let relationshipColor: Color
    @State private var animatedTags: [Bool] = []

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Tags")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                ForEach(Array(tags.enumerated()), id: \.offset) { index, tag in
                    Text(tag)
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 10)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(relationshipColor.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(relationshipColor.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .foregroundColor(relationshipColor)
                        .scaleEffect(animatedTags.indices.contains(index) && animatedTags[index] ? 1.0 : 0.8)
                        .opacity(animatedTags.indices.contains(index) && animatedTags[index] ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(index) * 0.1), value: animatedTags.indices.contains(index) ? animatedTags[index] : false)
                }
            }
        }
        .onAppear {
            animatedTags = Array(repeating: false, count: tags.count)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                for i in 0..<tags.count {
                    animatedTags[i] = true
                }
            }
        }
    }
}

/// Birthday countdown with visual appeal
struct BirthdayCountdownView: View {
    let birthday: Date
    let personName: String
    @State private var pulseAnimation = false

    private var daysUntilBirthday: Int {
        let calendar = Calendar.current
        let today = Date()
        let thisYear = calendar.component(.year, from: today)

        var nextBirthday = calendar.date(byAdding: .year, value: thisYear - calendar.component(.year, from: birthday), to: birthday) ?? birthday

        if nextBirthday < today {
            nextBirthday = calendar.date(byAdding: .year, value: 1, to: nextBirthday) ?? nextBirthday
        }

        return calendar.dateComponents([.day], from: today, to: nextBirthday).day ?? 0
    }

    private var birthdayColor: Color {
        if daysUntilBirthday <= 7 { return .red }
        else if daysUntilBirthday <= 30 { return .orange }
        else { return .blue }
    }

    var body: some View {
        HStack(spacing: 12) {
            // Birthday icon with animation
            Image(systemName: "gift.fill")
                .font(.system(size: 20))
                .foregroundColor(birthdayColor)
                .scaleEffect(pulseAnimation ? 1.2 : 1.0)
                .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: pulseAnimation)
                .onAppear { pulseAnimation = true }

            VStack(alignment: .leading, spacing: 2) {
                Text("Birthday")
                    .font(.caption)
                    .foregroundColor(.secondary)

                if daysUntilBirthday == 0 {
                    Text("🎉 Today!")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.red)
                } else if daysUntilBirthday == 1 {
                    Text("Tomorrow!")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                } else {
                    Text("In \(daysUntilBirthday) days")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(birthdayColor)
                }
            }

            Spacer()

            // Birthday date
            Text(birthday.formatted(date: .abbreviated, time: .omitted))
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(birthdayColor.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(birthdayColor.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

/// Timezone indicator with time display
struct TimezoneIndicatorView: View {
    let timezone: String
    let personName: String
    @State private var currentTime = Date()

    private var timeInTimezone: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        formatter.timeZone = TimeZone(identifier: timezone)
        return formatter.string(from: currentTime)
    }

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "clock.fill")
                .font(.system(size: 16))
                .foregroundColor(.blue)

            VStack(alignment: .leading, spacing: 2) {
                Text("Local Time")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(timeInTimezone)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }

            Spacer()

            Text(timezone)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(
                    Capsule()
                        .fill(Color(.systemGray6))
                )
        }
        .onAppear {
            // Update time every minute
            Timer.scheduledTimer(withTimeInterval: 60, repeats: true) { _ in
                currentTime = Date()
            }
        }
    }
}

/// Social links with platform icons
struct SocialLinksView: View {
    let socialLinks: [Person.SocialLink]

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Social Links")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                ForEach(socialLinks) { link in
                    HStack(spacing: 8) {
                        Image(systemName: platformIcon(for: link.platform))
                            .font(.system(size: 14))
                            .foregroundColor(platformColor(for: link.platform))

                        Text(link.platform)
                            .font(.caption)
                            .fontWeight(.medium)

                        Spacer()
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(platformColor(for: link.platform).opacity(0.1))
                    )
                }
            }
        }
    }

    private func platformIcon(for platform: String) -> String {
        switch platform.lowercased() {
        case "twitter", "x": return "bird"
        case "linkedin": return "briefcase"
        case "instagram": return "camera"
        case "facebook": return "person.2"
        case "github": return "chevron.left.forwardslash.chevron.right"
        default: return "link"
        }
    }

    private func platformColor(for platform: String) -> Color {
        switch platform.lowercased() {
        case "twitter", "x": return .blue
        case "linkedin": return .blue
        case "instagram": return .purple
        case "facebook": return .blue
        case "github": return .black
        default: return .gray
        }
    }
}

/// Interaction history row component
struct InteractionRow: View {
    let icon: String
    let title: String
    let date: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(color)
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(color.opacity(0.1))
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(date)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
        .padding(.vertical, 4)
    }
}

/// Header view with avatar and basic info
struct PersonHeaderView: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager
    
    var body: some View {
        VStack(spacing: 16) {
            // Large avatar with status
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                person.relationshipType.color.opacity(0.8),
                                person.relationshipType.color
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .overlay(
                        Circle()
                            .stroke(person.availability.color, lineWidth: 4)
                            .opacity(person.isOnline ? 1.0 : 0.3)
                    )
                    .shadow(color: person.relationshipType.color.opacity(0.3), radius: 15, x: 0, y: 8)
                
                // Avatar content
                if person.avatarImageName.contains("person") {
                    Image(systemName: person.avatarImageName)
                        .font(.system(size: 50, weight: .medium))
                        .foregroundColor(.white)
                } else {
                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(.white)
                }
                
                // Online status indicator
                if person.isOnline {
                    VStack {
                        HStack {
                            Spacer()
                            Circle()
                                .fill(person.availability.color)
                                .frame(width: 20, height: 20)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 3)
                                )
                                .modifier(BreathingAnimation(isOnline: person.isOnline))
                        }
                        Spacer()
                    }
                    .frame(width: 120, height: 120)
                }
            }
            
            // Name and title
            VStack(spacing: 8) {
                HStack {
                    Text(person.name)
                        .font(.title)
                        .fontWeight(.bold)
                    
                    if person.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.title3)
                            .foregroundColor(.yellow)
                    }
                }
                
                if !person.contactInfo.isEmpty {
                    Text(person.contactInfo)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Status and last seen
                HStack(spacing: 12) {
                    StatusBadge(
                        text: person.availability.rawValue,
                        color: person.availability.color,
                        isOnline: person.isOnline
                    )
                    
                    if !person.isOnline {
                        Text("Last seen \(person.timeSinceLastSeen)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            // Favorite toggle button
            Button(action: {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    peopleManager.toggleFavorite(for: person)
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: person.isFavorite ? "star.fill" : "star")
                        .font(.system(size: 14, weight: .medium))
                    
                    Text(person.isFavorite ? "Remove from Favorites" : "Add to Favorites")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(person.isFavorite ? .yellow : .blue)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(.systemGray6))
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 4)
        )
    }
}

/// Contact information section
struct PersonContactInfoView: View {
    @ObservedObject var person: Person
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Contact Information")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                if !person.email.isEmpty {
                    ContactInfoRow(
                        icon: "envelope.fill",
                        title: "Email",
                        value: person.email,
                        color: .blue,
                        action: {
                            if let url = URL(string: "mailto:\(person.email)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    )
                }
                
                if !person.phone.isEmpty {
                    ContactInfoRow(
                        icon: "phone.fill",
                        title: "Phone",
                        value: person.phone,
                        color: .green,
                        action: {
                            let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                            if let url = URL(string: "tel:\(cleanPhone)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    )
                }
                
                if !person.location.isEmpty {
                    ContactInfoRow(
                        icon: "location.fill",
                        title: "Location",
                        value: person.location,
                        color: .red,
                        action: nil
                    )
                }
                
                if !person.timezone.isEmpty && person.timezone != "UTC" {
                    ContactInfoRow(
                        icon: "clock.fill",
                        title: "Timezone",
                        value: person.timezone,
                        color: .orange,
                        action: nil
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
}

/// Contact info row component
struct ContactInfoRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    let action: (() -> Void)?
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(color.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.body)
                    .foregroundColor(.primary)
            }
            
            Spacer()
            
            if action != nil {
                Button(action: action!) {
                    Image(systemName: "arrow.up.right")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(color)
                }
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            action?()
        }
    }
}

/// Status badge component
struct StatusBadge: View {
    let text: String
    let color: Color
    let isOnline: Bool
    
    var body: some View {
        HStack(spacing: 6) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
                .modifier(BreathingAnimation(isOnline: isOnline))
            
            Text(text)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
}

/// Relationship and interaction information
struct PersonRelationshipView: View {
    @ObservedObject var person: Person

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Relationship & Interaction")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                RelationshipInfoRow(
                    icon: person.relationshipType.icon,
                    title: "Relationship",
                    value: person.relationshipType.rawValue,
                    color: person.relationshipType.color
                )

                RelationshipInfoRow(
                    icon: "clock.arrow.circlepath",
                    title: "Interaction Frequency",
                    value: person.interactionFrequency.rawValue,
                    color: .blue
                )

                if !person.tags.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Tags")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                            ForEach(person.tags, id: \.self) { tag in
                                Text(tag)
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.blue.opacity(0.1))
                                    )
                            }
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
}

/// Relationship info row
struct RelationshipInfoRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(color.opacity(0.1))
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.body)
                    .foregroundColor(.primary)
            }

            Spacer()
        }
    }
}

/// Notes section
struct PersonNotesView: View {
    @ObservedObject var person: Person

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Notes")
                .font(.headline)
                .fontWeight(.semibold)

            Text(person.notes)
                .font(.body)
                .foregroundColor(.primary)
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
}

/// Quick actions section
struct PersonQuickActionsView: View {
    @ObservedObject var person: Person

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                if !person.email.isEmpty {
                    QuickActionCard(
                        icon: "envelope.fill",
                        title: "Send Email",
                        color: .blue,
                        action: {
                            if let url = URL(string: "mailto:\(person.email)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    )
                }

                if !person.phone.isEmpty {
                    QuickActionCard(
                        icon: "phone.fill",
                        title: "Call",
                        color: .green,
                        action: {
                            let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                            if let url = URL(string: "tel:\(cleanPhone)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    )
                }

                QuickActionCard(
                    icon: "message.fill",
                    title: "Message",
                    color: .purple,
                    action: {
                        if !person.phone.isEmpty {
                            let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                            if let url = URL(string: "sms:\(cleanPhone)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    }
                )

                QuickActionCard(
                    icon: "calendar.badge.plus",
                    title: "Schedule",
                    color: .orange,
                    action: {
                        // Open calendar app
                        if let url = URL(string: "calshow://") {
                            UIApplication.shared.open(url)
                        }
                    }
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
}

/// Enhanced quick action card with micro-interactions
struct QuickActionCard: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    @State private var isPressed = false
    @State private var iconBounce = false

    var body: some View {
        Button(action: {
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()

            // Icon bounce animation
            withAnimation(.spring(response: 0.3, dampingFraction: 0.5)) {
                iconBounce = true
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.5)) {
                    iconBounce = false
                }
                action()
            }
        }) {
            VStack(spacing: 8) {
                ZStack {
                    // Icon background with gradient
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [color.opacity(0.2), color.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)
                        .scaleEffect(isPressed ? 0.9 : 1.0)

                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(color)
                        .scaleEffect(iconBounce ? 1.2 : 1.0)
                }

                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(.systemBackground),
                                Color(.systemGray6).opacity(0.3)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    colors: [color.opacity(0.3), color.opacity(0.1)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1.5
                            )
                    )
                    .shadow(color: color.opacity(0.1), radius: 4, x: 0, y: 2)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

/// Enhanced person header with emotional effects
struct EmotionalPersonHeaderView: View {
    @ObservedObject var person: Person
    let peopleManager: PeopleManager
    @Binding var celebrationTriggered: Bool

    var body: some View {
        VStack(spacing: 16) {
            // Enhanced avatar with emotional effects
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                person.relationshipType.color.opacity(0.8),
                                person.relationshipType.color
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .overlay(
                        Circle()
                            .stroke(person.availability.color, lineWidth: 4)
                            .opacity(person.isOnline ? 1.0 : 0.3)
                    )
                    .shadow(color: person.relationshipType.color.opacity(0.3), radius: 15, x: 0, y: 8)
                    // Add emotional animations
                    .modifier(HeartbeatAnimation(isFavorite: person.isFavorite))
                    .modifier(BreathingAnimation(isOnline: person.isOnline))
                    .modifier(EmotionalStateIndicator(
                        interactionFrequency: person.interactionFrequency,
                        relationshipType: person.relationshipType
                    ))

                // Avatar content
                if person.avatarImageName.contains("person") {
                    Image(systemName: person.avatarImageName)
                        .font(.system(size: 50, weight: .medium))
                        .foregroundColor(.white)
                } else {
                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: 36, weight: .bold))
                        .foregroundColor(.white)
                }

                // Online status indicator with breathing
                if person.isOnline {
                    VStack {
                        HStack {
                            Spacer()
                            Circle()
                                .fill(person.availability.color)
                                .frame(width: 20, height: 20)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 3)
                                )
                                .modifier(BreathingAnimation(isOnline: person.isOnline))
                        }
                        Spacer()
                    }
                    .frame(width: 120, height: 120)
                }
            }
            .celebrationBurst(isTriggered: $celebrationTriggered)

            // Name and title with emotional context
            VStack(spacing: 8) {
                HStack {
                    Text(person.name)
                        .font(.title)
                        .fontWeight(.bold)

                    if person.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.title3)
                            .foregroundColor(.yellow)
                            .particleEffect(.favorite)
                    }
                }

                // Emotional connection indicator
                Text(person.interactionFrequency.emotionalDescription)
                    .font(.subheadline)
                    .foregroundColor(person.relationshipType.color)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(person.relationshipType.color.opacity(0.1))
                    )

                if !person.contactInfo.isEmpty {
                    Text(person.contactInfo)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }

                // Status and last seen
                HStack(spacing: 12) {
                    StatusBadge(
                        text: person.availability.rawValue,
                        color: person.availability.color,
                        isOnline: person.isOnline
                    )

                    if !person.isOnline {
                        Text("Last seen \(person.timeSinceLastSeen)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // Enhanced favorite toggle with haptic feedback
            Button(action: {
                EmotionalHapticsManager.shared.playHeartbeatPattern()

                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    peopleManager.toggleFavorite(for: person)
                }

                if person.isFavorite {
                    celebrationTriggered = true
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        celebrationTriggered = false
                    }
                }
            }) {
                HStack(spacing: 8) {
                    Image(systemName: person.isFavorite ? "star.fill" : "star")
                        .font(.system(size: 14, weight: .medium))

                    Text(person.isFavorite ? "Remove from Favorites" : "Add to Favorites")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(person.isFavorite ? .yellow : .blue)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(.systemGray6))
                )
            }
            .emotionalHaptics(for: person, action: .favorite)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: person.relationshipType.color.opacity(0.2), radius: 15, x: 0, y: 8)
        )
    }
}

/// Enhanced relationship view with emotional insights
struct EmotionalRelationshipView: View {
    @ObservedObject var person: Person

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Emotional Connection")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 16) {
                // Emotional intensity visualization
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Connection Strength")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(person.interactionFrequency.emotionalIntensity * 100))%")
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundColor(person.relationshipType.color)
                    }

                    // Emotional intensity bar
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color(.systemGray5))
                                .frame(height: 8)

                            RoundedRectangle(cornerRadius: 4)
                                .fill(
                                    LinearGradient(
                                        colors: [
                                            person.relationshipType.color.opacity(0.6),
                                            person.relationshipType.color
                                        ],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(
                                    width: geometry.size.width * person.interactionFrequency.emotionalIntensity,
                                    height: 8
                                )
                                .animation(.spring(response: 0.8, dampingFraction: 0.8), value: person.interactionFrequency.emotionalIntensity)
                        }
                    }
                    .frame(height: 8)
                }

                // Relationship details
                RelationshipInfoRow(
                    icon: person.relationshipType.icon,
                    title: "Relationship",
                    value: person.relationshipType.rawValue,
                    color: person.relationshipType.color
                )

                RelationshipInfoRow(
                    icon: "clock.arrow.circlepath",
                    title: "Interaction Frequency",
                    value: person.interactionFrequency.rawValue,
                    color: .blue
                )

                // Emotional tags
                if !person.tags.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Tags")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                            ForEach(person.tags, id: \.self) { tag in
                                Text(tag)
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(person.relationshipType.color.opacity(0.1))
                                    )
                                    .foregroundColor(person.relationshipType.color)
                            }
                        }
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: person.relationshipType.color.opacity(0.1), radius: 10, x: 0, y: 4)
        )
    }
}

/// Enhanced quick actions with emotional haptic feedback
struct EmotionalQuickActionsView: View {
    @ObservedObject var person: Person
    @Binding var celebrationTriggered: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                if !person.email.isEmpty {
                    EmotionalQuickActionCard(
                        icon: "envelope.fill",
                        title: "Send Email",
                        color: .blue,
                        person: person,
                        action: {
                            if let url = URL(string: "mailto:\(person.email)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    )
                }

                if !person.phone.isEmpty {
                    EmotionalQuickActionCard(
                        icon: "phone.fill",
                        title: "Call",
                        color: .green,
                        person: person,
                        action: {
                            let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                            if let url = URL(string: "tel:\(cleanPhone)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    )
                }

                EmotionalQuickActionCard(
                    icon: "message.fill",
                    title: "Message",
                    color: .purple,
                    person: person,
                    action: {
                        if !person.phone.isEmpty {
                            let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                            if let url = URL(string: "sms:\(cleanPhone)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    }
                )

                EmotionalQuickActionCard(
                    icon: "heart.circle.fill",
                    title: "Celebrate",
                    color: .red,
                    person: person,
                    action: {
                        EmotionalHapticsManager.shared.playCelebrationPattern()
                        celebrationTriggered = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            celebrationTriggered = false
                        }
                    }
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
    }
}

/// Enhanced quick action card with emotional feedback
struct EmotionalQuickActionCard: View {
    let icon: String
    let title: String
    let color: Color
    let person: Person
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: {
            EmotionalHapticsManager.shared.playConnectionPulse(for: person.interactionFrequency)
            action()
        }) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(color)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
        .emotionalHaptics(for: person, action: .tap)
    }
}

/// Custom tab indicator for emotional navigation
struct EmotionalTabIndicator: View {
    @Binding var selectedTab: Int
    let person: Person

    var body: some View {
        HStack(spacing: 20) {
            // Details tab
            TabIndicatorButton(
                icon: "person.circle.fill",
                title: "Details",
                isSelected: selectedTab == 0,
                color: person.relationshipType.color,
                action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        selectedTab = 0
                    }
                }
            )

            // Memory timeline tab
            TabIndicatorButton(
                icon: "timeline.selection",
                title: "Memories",
                isSelected: selectedTab == 1,
                color: person.relationshipType.color,
                action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        selectedTab = 1
                    }
                }
            )
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 25)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 4)
        )
    }
}

/// Individual tab indicator button
struct TabIndicatorButton: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))

                if isSelected {
                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .transition(.move(edge: .trailing).combined(with: .opacity))
                }
            }
            .foregroundColor(isSelected ? .white : color)
            .padding(.horizontal, isSelected ? 16 : 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? color : Color.clear)
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isSelected)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Editable Contact Field

/// Editable contact field for inline editing
struct EditableContactField: View {
    let icon: String
    let title: String
    @Binding var value: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 20)

                TextField(title, text: $value)
                    .textFieldStyle(PlainTextFieldStyle())
                    .font(.body)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
}

struct PersonDetailView_Previews: PreviewProvider {
    static var previews: some View {
        let samplePerson = Person(
            name: "Sarah Johnson",
            role: "Product Manager",
            company: "TechCorp",
            email: "<EMAIL>",
            phone: "+****************",
            isOnline: true,
            availability: .available,
            relationshipType: .colleague,
            interactionFrequency: .daily,
            location: "San Francisco, CA",
            isFavorite: true
        )

        let peopleManager = PeopleManager()

        return PersonDetailView(person: samplePerson, peopleManager: peopleManager)
    }
}
