//
//  EvolutionTimelineModels.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI
import Foundation

// MARK: - Core Timeline Models

/// Represents a single week entry in the Evolution Timeline
/// Each week is a building block of personal growth and insight
class WeekEntry: Identifiable, Codable, ObservableObject {
    let id = UUID()
    @Published var weekNumber: Int // 1-4000+ representing life weeks
    @Published var startDate: Date
    @Published var endDate: Date
    @Published var title: String
    @Published var insight: String // Main reflection/insight for the week
    @Published var emotionalTag: EmotionalTag
    @Published var accomplishments: [String]
    @Published var linkedPeople: [UUID] // References to Person IDs
    @Published var linkedProjects: [String]
    @Published var audioRecordingURL: URL?
    @Published var sketchData: Data? // For drawing/sketching
    @Published var isCompleted: Bool
    @Published var createdDate: Date
    @Published var lastModified: Date
    
    init(weekNumber: Int, startDate: Date) {
        self.weekNumber = weekNumber
        self.startDate = startDate
        self.endDate = Calendar.current.date(byAdding: .day, value: 6, to: startDate) ?? startDate
        self.title = ""
        self.insight = ""
        self.emotionalTag = .neutral
        self.accomplishments = []
        self.linkedPeople = []
        self.linkedProjects = []
        self.audioRecordingURL = nil
        self.sketchData = nil
        self.isCompleted = false
        self.createdDate = Date()
        self.lastModified = Date()
    }

    /// Convenience initializer using birth date and week number
    init(weekNumber: Int, birthDate: Date) {
        let startDate = TimelineConfiguration.startDate(for: weekNumber, birthDate: birthDate)
        let endDate = TimelineConfiguration.endDate(for: weekNumber, birthDate: birthDate)

        self.weekNumber = weekNumber
        self.startDate = startDate
        self.endDate = endDate
        self.title = ""
        self.insight = ""
        self.emotionalTag = .neutral
        self.accomplishments = []
        self.linkedPeople = []
        self.linkedProjects = []
        self.audioRecordingURL = nil
        self.sketchData = nil
        self.isCompleted = false
        self.createdDate = Date()
        self.lastModified = Date()
    }
    
    // MARK: - Computed Properties
    
    /// Returns true if this is the current week
    var isCurrentWeek: Bool {
        let now = Date()
        return startDate <= now && now <= endDate
    }
    
    /// Returns true if this week is in the past
    var isPastWeek: Bool {
        return endDate < Date()
    }
    
    /// Returns true if this week has any content
    var hasContent: Bool {
        return !title.isEmpty || !insight.isEmpty || !accomplishments.isEmpty
    }

    /// Content level for memory dots (0-3)
    var contentLevel: Int {
        var level = 0

        // Basic content (title or insight)
        if !title.isEmpty || !insight.isEmpty {
            level += 1
        }

        // Good content (accomplishments)
        if !accomplishments.isEmpty {
            level += 1
        }

        // Rich content (multiple accomplishments or linked content)
        if accomplishments.count > 2 || !linkedPeople.isEmpty || !linkedProjects.isEmpty {
            level += 1
        }

        return min(level, 3)
    }

    /// Auto-complete week if it has meaningful content
    func updateCompletionStatus() {
        // Auto-complete if week has title and either insight or accomplishments
        if !title.isEmpty && (!insight.isEmpty || !accomplishments.isEmpty) {
            isCompleted = true
        }
    }
    
    /// Color based on emotional tag and completion status
    var displayColor: Color {
        // If week has content, show the emotional tag color regardless of completion
        if hasContent {
            return emotionalTag.color
        }

        // If no content and it's a past week, show muted gray
        if isPastWeek {
            return .gray.opacity(0.3)
        }

        // Future weeks or current week with no content - neutral
        return .gray.opacity(0.5)
    }
    
    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, weekNumber, startDate, endDate, title, insight
        case emotionalTag, accomplishments, linkedPeople, linkedProjects
        case audioRecordingURL, sketchData, isCompleted, createdDate, lastModified
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        weekNumber = try container.decode(Int.self, forKey: .weekNumber)
        startDate = try container.decode(Date.self, forKey: .startDate)
        endDate = try container.decode(Date.self, forKey: .endDate)
        title = try container.decode(String.self, forKey: .title)
        insight = try container.decode(String.self, forKey: .insight)
        emotionalTag = try container.decode(EmotionalTag.self, forKey: .emotionalTag)
        accomplishments = try container.decode([String].self, forKey: .accomplishments)
        linkedPeople = try container.decode([UUID].self, forKey: .linkedPeople)
        linkedProjects = try container.decode([String].self, forKey: .linkedProjects)
        audioRecordingURL = try container.decodeIfPresent(URL.self, forKey: .audioRecordingURL)
        sketchData = try container.decodeIfPresent(Data.self, forKey: .sketchData)
        isCompleted = try container.decode(Bool.self, forKey: .isCompleted)
        createdDate = try container.decode(Date.self, forKey: .createdDate)
        lastModified = try container.decode(Date.self, forKey: .lastModified)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(weekNumber, forKey: .weekNumber)
        try container.encode(startDate, forKey: .startDate)
        try container.encode(endDate, forKey: .endDate)
        try container.encode(title, forKey: .title)
        try container.encode(insight, forKey: .insight)
        try container.encode(emotionalTag, forKey: .emotionalTag)
        try container.encode(accomplishments, forKey: .accomplishments)
        try container.encode(linkedPeople, forKey: .linkedPeople)
        try container.encode(linkedProjects, forKey: .linkedProjects)
        try container.encode(audioRecordingURL, forKey: .audioRecordingURL)
        try container.encode(sketchData, forKey: .sketchData)
        try container.encode(isCompleted, forKey: .isCompleted)
        try container.encode(createdDate, forKey: .createdDate)
        try container.encode(lastModified, forKey: .lastModified)
    }
}

// MARK: - Emotional Tags

/// Emotional tags for categorizing weekly experiences
enum EmotionalTag: String, CaseIterable, Codable {
    case growth = "Growth"
    case focus = "Focus"
    case joy = "Joy"
    case challenge = "Challenge"
    case breakthrough = "Breakthrough"
    case reflection = "Reflection"
    case connection = "Connection"
    case achievement = "Achievement"
    case learning = "Learning"
    case neutral = "Neutral"
    
    var color: Color {
        switch self {
        case .growth: return .green
        case .focus: return .blue
        case .joy: return .yellow
        case .challenge: return .orange
        case .breakthrough: return .purple
        case .reflection: return .indigo
        case .connection: return .pink
        case .achievement: return .red
        case .learning: return .cyan
        case .neutral: return .gray
        }
    }
    
    var emoji: String {
        switch self {
        case .growth: return "🌱"
        case .focus: return "🎯"
        case .joy: return "😊"
        case .challenge: return "💪"
        case .breakthrough: return "💡"
        case .reflection: return "🤔"
        case .connection: return "🤝"
        case .achievement: return "🏆"
        case .learning: return "📚"
        case .neutral: return "📝"
        }
    }
}

// MARK: - Timeline Zoom Levels

/// Different zoom levels for viewing the timeline
enum TimelineZoomLevel: CaseIterable {
    case week      // Individual weeks
    case month     // Monthly view (4 weeks)
    case year      // Yearly view (52 weeks)
    case decade    // Decade view (520 weeks)
    case life      // Life view (4000+ weeks)
    
    var title: String {
        switch self {
        case .week: return "Week"
        case .month: return "Month"
        case .year: return "Year"
        case .decade: return "Decade"
        case .life: return "Life"
        }
    }

    var shortTitle: String {
        switch self {
        case .week: return "Wk"
        case .month: return "Mo"
        case .year: return "Yr"
        case .decade: return "Dec"
        case .life: return "Life"
        }
    }
    
    var weeksPerUnit: Int {
        switch self {
        case .week: return 12      // Show 12 weeks (3 months) for browsing
        case .month: return 24     // Show 24 weeks (6 months)
        case .year: return 52      // Show 52 weeks (1 year)
        case .decade: return 120   // Show 120 weeks (~2.3 years)
        case .life: return 200     // Show 200 weeks max for performance
        }
    }
}

// MARK: - Timeline Configuration

/// Configuration for the Evolution Timeline
struct TimelineConfiguration {
    static let totalLifeWeeks = 4000
    static let weeksPerYear = 52
    static let averageLifespan = 77 // years
    
    /// Calculate week number from birth date (more accurate)
    static func weekNumber(from date: Date, birthDate: Date) -> Int {
        let calendar = Calendar.current

        // Get the start of the week for birth date (Sunday)
        let birthWeekStart = calendar.dateInterval(of: .weekOfYear, for: birthDate)?.start ?? birthDate

        // Get the start of the week for the target date
        let targetWeekStart = calendar.dateInterval(of: .weekOfYear, for: date)?.start ?? date

        // Calculate weeks between the two week starts
        let weeksBetween = calendar.dateComponents([.weekOfYear], from: birthWeekStart, to: targetWeekStart).weekOfYear ?? 0

        return max(1, weeksBetween + 1)
    }

    /// Calculate start date for a given week number (more accurate)
    static func startDate(for weekNumber: Int, birthDate: Date) -> Date {
        let calendar = Calendar.current

        // Get the start of the birth week
        let birthWeekStart = calendar.dateInterval(of: .weekOfYear, for: birthDate)?.start ?? birthDate

        // Add the appropriate number of weeks
        return calendar.date(byAdding: .weekOfYear, value: weekNumber - 1, to: birthWeekStart) ?? birthDate
    }

    /// Calculate end date for a given week number
    static func endDate(for weekNumber: Int, birthDate: Date) -> Date {
        let startDate = self.startDate(for: weekNumber, birthDate: birthDate)
        let calendar = Calendar.current
        return calendar.date(byAdding: .day, value: 6, to: startDate) ?? startDate
    }
}

// MARK: - Timeline Milestone

/// Represents significant life milestones that can be highlighted on the timeline
struct TimelineMilestone: Identifiable, Codable {
    var id = UUID()
    var title: String
    var description: String
    var weekNumber: Int
    var category: MilestoneCategory
    var isUserDefined: Bool
    
    enum MilestoneCategory: String, CaseIterable, Codable {
        case birth = "Birth"
        case education = "Education"
        case career = "Career"
        case relationship = "Relationship"
        case family = "Family"
        case achievement = "Achievement"
        case travel = "Travel"
        case health = "Health"
        case custom = "Custom"
        
        var color: Color {
            switch self {
            case .birth: return .pink
            case .education: return .blue
            case .career: return .green
            case .relationship: return .red
            case .family: return .orange
            case .achievement: return .yellow
            case .travel: return .purple
            case .health: return .cyan
            case .custom: return .gray
            }
        }
        
        var icon: String {
            switch self {
            case .birth: return "star.fill"
            case .education: return "graduationcap.fill"
            case .career: return "briefcase.fill"
            case .relationship: return "heart.fill"
            case .family: return "house.fill"
            case .achievement: return "trophy.fill"
            case .travel: return "airplane"
            case .health: return "heart.text.square.fill"
            case .custom: return "bookmark.fill"
            }
        }
    }
}
