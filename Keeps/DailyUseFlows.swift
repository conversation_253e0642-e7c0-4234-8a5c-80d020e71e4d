//
//  DailyUseFlows.swift
//  Keeps
//
//  Daily use flows for optimized common workflows
//  Combines People, Teams, and Timeline in seamless daily interactions
//

import SwiftUI
import CoreData

// MARK: - Daily Check-in Manager

/// Manages daily check-in flows and user engagement
class DailyCheckInManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var showingDailyCheckIn = false
    @Published var currentCheckInStep: CheckInStep = .welcome
    @Published var checkInProgress: Double = 0.0
    @Published var isAnimating = false
    
    // MARK: - Check-in Data
    
    @Published var todaysMood: DailyMood = .neutral
    @Published var todaysGoals: [DailyGoal] = []
    @Published var plannedInteractions: [PlannedInteraction] = []
    @Published var teamUpdates: [TeamUpdate] = []
    @Published var reflectionText = ""
    @Published var gratitudeItems: [String] = ["", "", ""]
    
    // MARK: - State Management
    
    @Published var hasCompletedTodaysCheckIn = false
    @Published var lastCheckInDate: Date?
    
    private let userDefaults = UserDefaults.standard
    private let lastCheckInKey = "lastDailyCheckInDate"
    
    // MARK: - Initialization
    
    init() {
        loadCheckInStatus()
    }
    
    // MARK: - Public Methods
    
    /// Start daily check-in flow
    func startDailyCheckIn() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            showingDailyCheckIn = true
            currentCheckInStep = .welcome
            checkInProgress = 0.0
            resetCheckInData()
        }
    }
    
    /// Move to next check-in step
    func nextCheckInStep() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            isAnimating = true
            
            switch currentCheckInStep {
            case .welcome:
                currentCheckInStep = .mood
                checkInProgress = 0.2
            case .mood:
                currentCheckInStep = .goals
                checkInProgress = 0.4
            case .goals:
                currentCheckInStep = .people
                checkInProgress = 0.6
            case .people:
                currentCheckInStep = .teams
                checkInProgress = 0.8
            case .teams:
                currentCheckInStep = .reflection
                checkInProgress = 1.0
            case .reflection:
                completeCheckIn()
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.isAnimating = false
            }
        }
    }
    
    /// Complete daily check-in
    func completeCheckIn() {
        hasCompletedTodaysCheckIn = true
        lastCheckInDate = Date()
        userDefaults.set(Date(), forKey: lastCheckInKey)
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            showingDailyCheckIn = false
        }
        
        // Save check-in data to Core Data
        saveCheckInData()
    }
    
    /// Check if daily check-in is needed
    func shouldShowDailyCheckIn() -> Bool {
        guard let lastDate = lastCheckInDate else { return true }
        return !Calendar.current.isDateInToday(lastDate)
    }
    
    // MARK: - Private Methods
    
    private func loadCheckInStatus() {
        if let lastDate = userDefaults.object(forKey: lastCheckInKey) as? Date {
            lastCheckInDate = lastDate
            hasCompletedTodaysCheckIn = Calendar.current.isDateInToday(lastDate)
        }
    }
    
    private func resetCheckInData() {
        todaysMood = .neutral
        todaysGoals = []
        plannedInteractions = []
        teamUpdates = []
        reflectionText = ""
        gratitudeItems = ["", "", ""]
    }
    
    private func saveCheckInData() {
        // Implementation will be added when integrating with Core Data
        print("💾 Saving daily check-in data...")
    }
}

// MARK: - Daily Check-in Models

/// Steps in the daily check-in flow
enum CheckInStep: CaseIterable {
    case welcome
    case mood
    case goals
    case people
    case teams
    case reflection
    
    var title: String {
        switch self {
        case .welcome: return "Good Morning!"
        case .mood: return "How are you feeling?"
        case .goals: return "Today's Focus"
        case .people: return "People Connections"
        case .teams: return "Team Updates"
        case .reflection: return "Reflection"
        }
    }
    
    var subtitle: String {
        switch self {
        case .welcome: return "Let's plan your day together"
        case .mood: return "Check in with yourself"
        case .goals: return "What matters most today?"
        case .people: return "Who will you connect with?"
        case .teams: return "Any team progress to share?"
        case .reflection: return "Gratitude and insights"
        }
    }
    
    var icon: String {
        switch self {
        case .welcome: return "sun.max.fill"
        case .mood: return "heart.fill"
        case .goals: return "target"
        case .people: return "person.crop.circle.fill"
        case .teams: return "person.3.fill"
        case .reflection: return "lightbulb.fill"
        }
    }
}

/// Daily mood options
enum DailyMood: String, CaseIterable {
    case energetic = "Energetic"
    case focused = "Focused"
    case calm = "Calm"
    case neutral = "Neutral"
    case tired = "Tired"
    case stressed = "Stressed"
    
    var emoji: String {
        switch self {
        case .energetic: return "⚡️"
        case .focused: return "🎯"
        case .calm: return "😌"
        case .neutral: return "😐"
        case .tired: return "😴"
        case .stressed: return "😰"
        }
    }
    
    var color: Color {
        switch self {
        case .energetic: return .orange
        case .focused: return .blue
        case .calm: return .green
        case .neutral: return .gray
        case .tired: return .purple
        case .stressed: return .red
        }
    }
}

/// Daily goal structure
struct DailyGoal: Identifiable {
    let id = UUID()
    var title: String
    var category: GoalCategory
    var isCompleted = false
    var estimatedTime: Int // in minutes
    var relatedPeople: [UUID] = []
    var relatedTeams: [UUID] = []
}

// Note: Using existing GoalCategory from TeamEntityManager.swift

/// Planned interaction with people
struct PlannedInteraction: Identifiable {
    let id = UUID()
    var personId: UUID
    var personName: String
    var interactionType: InteractionType
    var scheduledTime: Date?
    var notes: String = ""
    var isCompleted = false
}

// Note: Using existing InteractionType from PersonEntityManager.swift

/// Team update structure
struct TeamUpdate: Identifiable {
    let id = UUID()
    var teamId: UUID
    var teamName: String
    var updateType: UpdateType
    var description: String
    var impact: ImpactLevel
}

/// Types of team updates
enum UpdateType: String, CaseIterable {
    case progress = "Progress"
    case milestone = "Milestone"
    case challenge = "Challenge"
    case success = "Success"
    case planning = "Planning"
    
    var icon: String {
        switch self {
        case .progress: return "chart.line.uptrend.xyaxis"
        case .milestone: return "flag.fill"
        case .challenge: return "exclamationmark.triangle.fill"
        case .success: return "checkmark.circle.fill"
        case .planning: return "calendar.badge.plus"
        }
    }
    
    var color: Color {
        switch self {
        case .progress: return .blue
        case .milestone: return .green
        case .challenge: return .orange
        case .success: return .mint
        case .planning: return .purple
        }
    }
}

// Note: Using existing ImpactLevel from TimelineEnhancedModels.swift

// MARK: - Quick Action Manager

/// Manages quick actions for common workflows
class QuickActionManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var showingQuickActions = false
    @Published var selectedAction: QuickAction?
    
    // MARK: - Quick Actions
    
    let availableActions: [QuickAction] = [
        QuickAction(
            id: "add_interaction",
            title: "Log Interaction",
            subtitle: "Record a conversation or meeting",
            icon: "person.crop.circle.badge.plus",
            color: .blue,
            category: .people
        ),
        QuickAction(
            id: "update_team",
            title: "Team Update",
            subtitle: "Share progress or milestone",
            icon: "person.3.sequence.fill",
            color: .green,
            category: .teams
        ),
        QuickAction(
            id: "add_achievement",
            title: "Add Achievement",
            subtitle: "Celebrate a win or accomplishment",
            icon: "star.fill",
            color: .yellow,
            category: .timeline
        ),
        QuickAction(
            id: "plan_meeting",
            title: "Plan Meeting",
            subtitle: "Schedule time with people",
            icon: "calendar.badge.plus",
            color: .purple,
            category: .cross_section
        ),
        QuickAction(
            id: "weekly_review",
            title: "Weekly Review",
            subtitle: "Reflect on the week's progress",
            icon: "chart.bar.fill",
            color: .orange,
            category: .cross_section
        )
    ]
    
    // MARK: - Public Methods
    
    func executeQuickAction(_ action: QuickAction) {
        selectedAction = action
        // Implementation will be added for each action type
        print("🚀 Executing quick action: \(action.title)")
    }
}

/// Quick action structure
struct QuickAction: Identifiable {
    let id: String
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let category: ActionCategory
}

/// Action categories
enum ActionCategory {
    case people
    case teams
    case timeline
    case cross_section
}
