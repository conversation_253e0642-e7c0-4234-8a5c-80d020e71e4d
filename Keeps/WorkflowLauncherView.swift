//
//  WorkflowLauncherView.swift
//  Keeps
//
//  Revolutionary workflow launcher with Apple-style design
//  Provides easy access to cross-section workflows from any part of the app
//

import SwiftUI

// MARK: - Workflow Launcher

/// Main workflow launcher interface
struct WorkflowLauncherView: View {
    @ObservedObject var workflowManager: CrossSectionWorkflowManager
    @Environment(\.dismiss) private var dismiss
    @State private var animateCards = false
    @State private var selectedWorkflow: WorkflowType?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    launcherHeader
                    
                    // Workflow categories
                    workflowCategories
                    
                    // Recent workflows
                    if !workflowManager.workflowHistory.isEmpty {
                        recentWorkflows
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
            }
            .background(
                LinearGradient(
                    colors: [
                        Color.blue.opacity(0.05),
                        Color.purple.opacity(0.05),
                        Color.clear
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
            .navigationTitle("Workflows")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            animateCards = true
        }
    }
    
    private var launcherHeader: some View {
        VStack(spacing: 16) {
            // Icon
            Circle()
                .fill(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 80, height: 80)
                .overlay(
                    Image(systemName: "arrow.triangle.branch")
                        .font(.title)
                        .foregroundColor(.white)
                )
                .scaleEffect(animateCards ? 1.0 : 0.8)
                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: animateCards)
            
            // Title and description
            VStack(spacing: 8) {
                Text("Cross-Section Workflows")
                    .font(.title2)
                    .fontWeight(.bold)
                    .opacity(animateCards ? 1 : 0)
                    .offset(y: animateCards ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.2), value: animateCards)
                
                Text("Connect your people, teams, and timeline goals through intelligent workflows")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .opacity(animateCards ? 1 : 0)
                    .offset(y: animateCards ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.3), value: animateCards)
            }
        }
        .padding(.top, 20)
    }
    
    private var workflowCategories: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Available Workflows")
                .font(.headline)
                .fontWeight(.semibold)
                .opacity(animateCards ? 1 : 0)
                .offset(x: animateCards ? 0 : -20)
                .animation(.easeOut(duration: 0.6).delay(0.4), value: animateCards)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(Array(WorkflowType.allCases.enumerated()), id: \.element) { index, workflowType in
                    WorkflowCard(
                        workflowType: workflowType,
                        isSelected: selectedWorkflow == workflowType
                    ) {
                        selectedWorkflow = workflowType
                        startWorkflow(workflowType)
                    }
                    .opacity(animateCards ? 1 : 0)
                    .offset(y: animateCards ? 0 : 30)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.5 + Double(index) * 0.1), value: animateCards)
                }
            }
        }
    }
    
    private var recentWorkflows: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Workflows")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(workflowManager.workflowHistory.prefix(3)) { workflow in
                RecentWorkflowCard(workflow: workflow) {
                    // Restart workflow
                    startWorkflow(workflow.type)
                }
            }
        }
    }
    
    private func startWorkflow(_ type: WorkflowType) {
        workflowManager.startWorkflow(type)
        dismiss()
    }
}

// MARK: - Workflow Card

struct WorkflowCard: View {
    let workflowType: WorkflowType
    let isSelected: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                // Icon
                Circle()
                    .fill(workflowType.color.opacity(0.2))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: workflowType.icon)
                            .font(.title2)
                            .foregroundColor(workflowType.color)
                    )
                
                // Content
                VStack(spacing: 8) {
                    Text(workflowType.rawValue)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                    
                    Text(workflowType.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(3)
                }
            }
            .padding(20)
            .frame(maxWidth: .infinity)
            .frame(height: 180)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(color: workflowType.color.opacity(0.2), radius: isPressed ? 8 : 4, x: 0, y: isPressed ? 4 : 2)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Recent Workflow Card

struct RecentWorkflowCard: View {
    let workflow: CompletedWorkflow
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                Circle()
                    .fill(workflow.type.color.opacity(0.2))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: workflow.type.icon)
                            .font(.headline)
                            .foregroundColor(workflow.type.color)
                    )
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(workflow.type.rawValue)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text("Completed \(formatDate(workflow.completedAt))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    // Stats
                    HStack(spacing: 12) {
                        if !workflow.selectedPeople.isEmpty {
                            Label("\(workflow.selectedPeople.count)", systemImage: "person.crop.circle")
                                .font(.caption2)
                                .foregroundColor(.blue)
                        }
                        
                        if !workflow.selectedTeams.isEmpty {
                            Label("\(workflow.selectedTeams.count)", systemImage: "person.3")
                                .font(.caption2)
                                .foregroundColor(.green)
                        }
                        
                        if !workflow.selectedTimelineGoals.isEmpty {
                            Label("\(workflow.selectedTimelineGoals.count)", systemImage: "target")
                                .font(.caption2)
                                .foregroundColor(.purple)
                        }
                    }
                }
                
                Spacer()
                
                // Restart button
                Image(systemName: "arrow.clockwise")
                    .font(.headline)
                    .foregroundColor(workflow.type.color)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.05))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - Workflow Quick Actions

/// Quick workflow actions that can be embedded in other views
struct WorkflowQuickActions: View {
    @ObservedObject var workflowManager: CrossSectionWorkflowManager
    let sourceSection: AppSection
    let sourceId: UUID?
    
    @State private var showingLauncher = false
    
    var body: some View {
        VStack(spacing: 12) {
            Text("Quick Workflows")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(getRelevantWorkflows(), id: \.self) { workflowType in
                    QuickWorkflowButton(workflowType: workflowType) {
                        startContextualWorkflow(workflowType)
                    }
                }
            }
            
            Button("View All Workflows") {
                showingLauncher = true
            }
            .font(.subheadline)
            .foregroundColor(.blue)
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .sheet(isPresented: $showingLauncher) {
            WorkflowLauncherView(workflowManager: workflowManager)
        }
    }
    
    private func getRelevantWorkflows() -> [WorkflowType] {
        switch sourceSection {
        case .people:
            return [.relationshipReview, .planWithPeople, .teamFormation]
        case .teams:
            return [.teamFormation, .goalAlignment, .achievementTracking]
        case .timeline:
            return [.planWithPeople, .goalAlignment, .weeklyReview]
        case .workflow:
            return [.goalAlignment, .weeklyReview, .achievementTracking]
        case .general:
            return WorkflowType.allCases
        }
    }
    
    private func startContextualWorkflow(_ type: WorkflowType) {
        let context = WorkflowContext(
            sourceSection: sourceSection,
            sourceId: sourceId,
            sourceData: [:]
        )
        workflowManager.startWorkflow(type, context: context)
    }
}

/// Quick workflow button for embedded use
struct QuickWorkflowButton: View {
    let workflowType: WorkflowType
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: workflowType.icon)
                    .font(.title2)
                    .foregroundColor(workflowType.color)
                
                Text(workflowType.rawValue)
                    .font(.caption2)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 8)
            .frame(maxWidth: .infinity)
            .background(workflowType.color.opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
