//
//  CreateTeamView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI

/// View for creating new teams with full functionality
struct CreateTeamView: View {
    @ObservedObject var teamManager: TeamManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var teamName = ""
    @State private var teamDescription = ""
    @State private var selectedCategory: TeamCategory = .creative
    @State private var teamLocation = ""
    @State private var isCreating = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Team Information")) {
                    TextField("Team Name", text: $teamName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    TextField("Description", text: $teamDescription, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3...6)
                    
                    TextField("Location", text: $teamLocation)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                
                Section(header: Text("Category")) {
                    Picker("Category", selection: $selectedCategory) {
                        ForEach(TeamCategory.allCases.filter { $0 != .all }, id: \.self) { category in
                            HStack {
                                Circle()
                                    .fill(category.color)
                                    .frame(width: 12, height: 12)
                                Text(category.displayName)
                            }
                            .tag(category)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                Section(header: Text("Preview")) {
                    if !teamName.isEmpty {
                        TeamPreviewCard(
                            name: teamName,
                            description: teamDescription.isEmpty ? "No description" : teamDescription,
                            category: selectedCategory,
                            location: teamLocation.isEmpty ? "No location" : teamLocation
                        )
                    } else {
                        Text("Enter team name to see preview")
                            .foregroundColor(.secondary)
                            .italic()
                    }
                }
            }
            .navigationTitle("Create Team")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createTeam()
                    }
                    .disabled(teamName.isEmpty || isCreating)
                    .fontWeight(.semibold)
                }
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    private func createTeam() {
        guard !teamName.isEmpty else {
            errorMessage = "Team name is required"
            showingError = true
            return
        }
        
        isCreating = true
        
        // Simulate network delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            teamManager.createTeam(
                name: teamName,
                description: teamDescription,
                category: selectedCategory,
                location: teamLocation
            )
            
            isCreating = false
            dismiss()
        }
    }
}

/// Preview card for the new team
struct TeamPreviewCard: View {
    let name: String
    let description: String
    let category: TeamCategory
    let location: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(category.displayName)
                    .font(.caption)
                    .foregroundColor(category.color)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(category.color.opacity(0.1))
                    .cornerRadius(8)
                
                Spacer()
                
                Text("NEW")
                    .font(.caption2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.green)
                    .cornerRadius(4)
            }
            
            Text(name)
                .font(.title3)
                .fontWeight(.bold)
            
            Text(description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
            
            HStack {
                Image(systemName: "location")
                    .foregroundColor(.secondary)
                Text(location)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("0 members")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    CreateTeamView(teamManager: TeamManager())
}
