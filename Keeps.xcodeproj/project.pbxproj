// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		652C5BEC2DE8B78400E1E9ED /* SwiftUIX in Frameworks */ = {isa = PBXBuildFile; productRef = 652C5BEB2DE8B78400E1E9ED /* SwiftUIX */; };
		652C5BEE2DE8B7DF00E1E9ED /* SwiftUIX in Frameworks */ = {isa = PBXBuildFile; productRef = 652C5BED2DE8B7DF00E1E9ED /* SwiftUIX */; };
		652C5BF02DE8B7E700E1E9ED /* SwiftUIX in Frameworks */ = {isa = PBXBuildFile; productRef = 652C5BEF2DE8B7E700E1E9ED /* SwiftUIX */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		652C5BCD2DE8B19800E1E9ED /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 652C5BB22DE8B19600E1E9ED /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 652C5BB92DE8B19600E1E9ED;
			remoteInfo = Keeps;
		};
		652C5BD72DE8B19800E1E9ED /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 652C5BB22DE8B19600E1E9ED /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 652C5BB92DE8B19600E1E9ED;
			remoteInfo = Keeps;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		652C5BBA2DE8B19600E1E9ED /* Keeps.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Keeps.app; sourceTree = BUILT_PRODUCTS_DIR; };
		652C5BCC2DE8B19800E1E9ED /* KeepsTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = KeepsTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		652C5BD62DE8B19800E1E9ED /* KeepsUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = KeepsUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		652C5BBC2DE8B19600E1E9ED /* Keeps */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Keeps;
			sourceTree = "<group>";
		};
		652C5BCF2DE8B19800E1E9ED /* KeepsTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = KeepsTests;
			sourceTree = "<group>";
		};
		652C5BD92DE8B19800E1E9ED /* KeepsUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = KeepsUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		652C5BB72DE8B19600E1E9ED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				652C5BEC2DE8B78400E1E9ED /* SwiftUIX in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		652C5BC92DE8B19800E1E9ED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				652C5BEE2DE8B7DF00E1E9ED /* SwiftUIX in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		652C5BD32DE8B19800E1E9ED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				652C5BF02DE8B7E700E1E9ED /* SwiftUIX in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		652C5BB12DE8B19600E1E9ED = {
			isa = PBXGroup;
			children = (
				652C5BBC2DE8B19600E1E9ED /* Keeps */,
				652C5BCF2DE8B19800E1E9ED /* KeepsTests */,
				652C5BD92DE8B19800E1E9ED /* KeepsUITests */,
				652C5BEA2DE8B77900E1E9ED /* Frameworks */,
				652C5BBB2DE8B19600E1E9ED /* Products */,
			);
			sourceTree = "<group>";
		};
		652C5BBB2DE8B19600E1E9ED /* Products */ = {
			isa = PBXGroup;
			children = (
				652C5BBA2DE8B19600E1E9ED /* Keeps.app */,
				652C5BCC2DE8B19800E1E9ED /* KeepsTests.xctest */,
				652C5BD62DE8B19800E1E9ED /* KeepsUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		652C5BEA2DE8B77900E1E9ED /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		652C5BB92DE8B19600E1E9ED /* Keeps */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 652C5BE02DE8B19800E1E9ED /* Build configuration list for PBXNativeTarget "Keeps" */;
			buildPhases = (
				652C5BB62DE8B19600E1E9ED /* Sources */,
				652C5BB72DE8B19600E1E9ED /* Frameworks */,
				652C5BB82DE8B19600E1E9ED /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				652C5BBC2DE8B19600E1E9ED /* Keeps */,
			);
			name = Keeps;
			packageProductDependencies = (
				652C5BEB2DE8B78400E1E9ED /* SwiftUIX */,
			);
			productName = Keeps;
			productReference = 652C5BBA2DE8B19600E1E9ED /* Keeps.app */;
			productType = "com.apple.product-type.application";
		};
		652C5BCB2DE8B19800E1E9ED /* KeepsTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 652C5BE32DE8B19800E1E9ED /* Build configuration list for PBXNativeTarget "KeepsTests" */;
			buildPhases = (
				652C5BC82DE8B19800E1E9ED /* Sources */,
				652C5BC92DE8B19800E1E9ED /* Frameworks */,
				652C5BCA2DE8B19800E1E9ED /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				652C5BCE2DE8B19800E1E9ED /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				652C5BCF2DE8B19800E1E9ED /* KeepsTests */,
			);
			name = KeepsTests;
			packageProductDependencies = (
				652C5BED2DE8B7DF00E1E9ED /* SwiftUIX */,
			);
			productName = KeepsTests;
			productReference = 652C5BCC2DE8B19800E1E9ED /* KeepsTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		652C5BD52DE8B19800E1E9ED /* KeepsUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 652C5BE62DE8B19800E1E9ED /* Build configuration list for PBXNativeTarget "KeepsUITests" */;
			buildPhases = (
				652C5BD22DE8B19800E1E9ED /* Sources */,
				652C5BD32DE8B19800E1E9ED /* Frameworks */,
				652C5BD42DE8B19800E1E9ED /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				652C5BD82DE8B19800E1E9ED /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				652C5BD92DE8B19800E1E9ED /* KeepsUITests */,
			);
			name = KeepsUITests;
			packageProductDependencies = (
				652C5BEF2DE8B7E700E1E9ED /* SwiftUIX */,
			);
			productName = KeepsUITests;
			productReference = 652C5BD62DE8B19800E1E9ED /* KeepsUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		652C5BB22DE8B19600E1E9ED /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					652C5BB92DE8B19600E1E9ED = {
						CreatedOnToolsVersion = 16.3;
					};
					652C5BCB2DE8B19800E1E9ED = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 652C5BB92DE8B19600E1E9ED;
					};
					652C5BD52DE8B19800E1E9ED = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 652C5BB92DE8B19600E1E9ED;
					};
				};
			};
			buildConfigurationList = 652C5BB52DE8B19600E1E9ED /* Build configuration list for PBXProject "Keeps" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 652C5BB12DE8B19600E1E9ED;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				652C5BE92DE8B6A000E1E9ED /* XCRemoteSwiftPackageReference "SwiftUIX" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 652C5BBB2DE8B19600E1E9ED /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				652C5BB92DE8B19600E1E9ED /* Keeps */,
				652C5BCB2DE8B19800E1E9ED /* KeepsTests */,
				652C5BD52DE8B19800E1E9ED /* KeepsUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		652C5BB82DE8B19600E1E9ED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		652C5BCA2DE8B19800E1E9ED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		652C5BD42DE8B19800E1E9ED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		652C5BB62DE8B19600E1E9ED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		652C5BC82DE8B19800E1E9ED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		652C5BD22DE8B19800E1E9ED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		652C5BCE2DE8B19800E1E9ED /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 652C5BB92DE8B19600E1E9ED /* Keeps */;
			targetProxy = 652C5BCD2DE8B19800E1E9ED /* PBXContainerItemProxy */;
		};
		652C5BD82DE8B19800E1E9ED /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 652C5BB92DE8B19600E1E9ED /* Keeps */;
			targetProxy = 652C5BD72DE8B19800E1E9ED /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		652C5BDE2DE8B19800E1E9ED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		652C5BDF2DE8B19800E1E9ED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		652C5BE12DE8B19800E1E9ED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.Keeps;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		652C5BE22DE8B19800E1E9ED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.Keeps;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		652C5BE42DE8B19800E1E9ED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.KeepsTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Keeps.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Keeps";
			};
			name = Debug;
		};
		652C5BE52DE8B19800E1E9ED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.KeepsTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Keeps.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Keeps";
			};
			name = Release;
		};
		652C5BE72DE8B19800E1E9ED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.KeepsUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Keeps;
			};
			name = Debug;
		};
		652C5BE82DE8B19800E1E9ED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 64NMP2XKXR;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = site.firmbond.KeepsUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Keeps;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		652C5BB52DE8B19600E1E9ED /* Build configuration list for PBXProject "Keeps" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				652C5BDE2DE8B19800E1E9ED /* Debug */,
				652C5BDF2DE8B19800E1E9ED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		652C5BE02DE8B19800E1E9ED /* Build configuration list for PBXNativeTarget "Keeps" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				652C5BE12DE8B19800E1E9ED /* Debug */,
				652C5BE22DE8B19800E1E9ED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		652C5BE32DE8B19800E1E9ED /* Build configuration list for PBXNativeTarget "KeepsTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				652C5BE42DE8B19800E1E9ED /* Debug */,
				652C5BE52DE8B19800E1E9ED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		652C5BE62DE8B19800E1E9ED /* Build configuration list for PBXNativeTarget "KeepsUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				652C5BE72DE8B19800E1E9ED /* Debug */,
				652C5BE82DE8B19800E1E9ED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		652C5BE92DE8B6A000E1E9ED /* XCRemoteSwiftPackageReference "SwiftUIX" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SwiftUIX/SwiftUIX";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.2.4;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		652C5BEB2DE8B78400E1E9ED /* SwiftUIX */ = {
			isa = XCSwiftPackageProductDependency;
			package = 652C5BE92DE8B6A000E1E9ED /* XCRemoteSwiftPackageReference "SwiftUIX" */;
			productName = SwiftUIX;
		};
		652C5BED2DE8B7DF00E1E9ED /* SwiftUIX */ = {
			isa = XCSwiftPackageProductDependency;
			package = 652C5BE92DE8B6A000E1E9ED /* XCRemoteSwiftPackageReference "SwiftUIX" */;
			productName = SwiftUIX;
		};
		652C5BEF2DE8B7E700E1E9ED /* SwiftUIX */ = {
			isa = XCSwiftPackageProductDependency;
			package = 652C5BE92DE8B6A000E1E9ED /* XCRemoteSwiftPackageReference "SwiftUIX" */;
			productName = SwiftUIX;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 652C5BB22DE8B19600E1E9ED /* Project object */;
}
