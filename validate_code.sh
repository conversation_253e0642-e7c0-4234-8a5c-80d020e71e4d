#!/bin/bash

# Code Validation Script for Keeps Team Collaboration App
# Validates Swift code structure and syntax without requiring full Xcode

set -e

echo "🔍 Validating Keeps Team Collaboration App Code..."
echo "================================================"

# Function to check Swift file structure
validate_swift_file() {
    local file="$1"
    local filename=$(basename "$file")
    
    echo "📄 Validating: $filename"
    
    # Check if file exists and is not empty
    if [ ! -f "$file" ] || [ ! -s "$file" ]; then
        echo "  ❌ File is missing or empty"
        return 1
    fi
    
    # Check for basic Swift syntax
    local has_import=$(grep -c "^import " "$file" || echo "0")
    local has_struct_or_class=$(grep -c "^struct\|^class\|^enum\|^protocol" "$file" || echo "0")
    local has_braces=$(grep -c "{" "$file" || echo "0")
    local closing_braces=$(grep -c "}" "$file" || echo "0")
    
    echo "  📊 Imports: $has_import"
    echo "  📊 Declarations: $has_struct_or_class"
    echo "  📊 Opening braces: $has_braces"
    echo "  📊 Closing braces: $closing_braces"
    
    # Basic validation
    if [ "$has_import" -eq 0 ]; then
        echo "  ⚠️  No import statements found"
    fi
    
    if [ "$has_struct_or_class" -eq 0 ]; then
        echo "  ❌ No Swift declarations found"
        return 1
    fi
    
    # Check brace balance (simple check)
    if [ "$has_braces" -ne "$closing_braces" ]; then
        echo "  ⚠️  Unbalanced braces detected"
    fi
    
    echo "  ✅ Basic syntax validation passed"
    return 0
}

# Function to validate project structure
validate_project_structure() {
    echo "📁 Validating project structure..."
    
    local required_files=(
        "Keeps/KeepsApp.swift"
        "Keeps/ContentView.swift"
        "Keeps/Models.swift"
        "Keeps/TeamsListView.swift"
        "Keeps/TeamCardView.swift"
        "Keeps/TeamDetailView.swift"
        "Keeps/TeamCollaborationTests.swift"
    )
    
    local missing_files=0
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            echo "  ✅ $file"
        else
            echo "  ❌ Missing: $file"
            ((missing_files++))
        fi
    done
    
    if [ "$missing_files" -eq 0 ]; then
        echo "  ✅ All required files present"
        return 0
    else
        echo "  ❌ $missing_files files missing"
        return 1
    fi
}

# Function to check code quality metrics
check_code_quality() {
    echo "📊 Analyzing code quality..."

    # Count Swift files
    local file_count=$(find Keeps -name "*.swift" -type f | wc -l)
    echo "  📈 Total Swift files: $file_count"

    # Count total lines
    local total_lines=$(find Keeps -name "*.swift" -exec cat {} \; | wc -l)
    echo "  📈 Total lines of code: $total_lines"

    # Count documentation comments
    local doc_comments=$(grep -r "///" Keeps/*.swift 2>/dev/null | wc -l || echo "0")
    echo "  📈 Documentation comments: $doc_comments"

    # Count TODO/FIXME comments
    local todo_comments=$(grep -r "TODO\|FIXME" Keeps/*.swift 2>/dev/null | wc -l || echo "0")
    echo "  📈 TODO/FIXME comments: $todo_comments"

    # Quality assessment
    if [ "$doc_comments" -gt 20 ]; then
        echo "  ✅ Good documentation coverage"
    else
        echo "  ⚠️  Consider adding more documentation"
    fi

    if [ "$todo_comments" -eq 0 ]; then
        echo "  ✅ No pending TODO items"
    else
        echo "  📝 $todo_comments TODO items to address"
    fi
}

# Function to validate specific Swift patterns
validate_swift_patterns() {
    echo "🔍 Validating Swift patterns and best practices..."
    
    local swift_files=(Keeps/*.swift)
    local issues=0
    
    for file in "${swift_files[@]}"; do
        if [ -f "$file" ]; then
            local filename=$(basename "$file")
            echo "  📄 Checking patterns in: $filename"
            
            # Check for SwiftUI imports
            if grep -q "import SwiftUI" "$file"; then
                echo "    ✅ SwiftUI import found"
            fi
            
            # Check for proper struct/class naming (PascalCase)
            local bad_naming=$(grep -E "^(struct|class|enum|protocol) [a-z]" "$file" || echo "")
            if [ -n "$bad_naming" ]; then
                echo "    ⚠️  Potential naming convention issues"
                ((issues++))
            fi
            
            # Check for @State, @Binding usage in SwiftUI files
            if grep -q "SwiftUI" "$file"; then
                if grep -q "@State\|@Binding\|@Environment" "$file"; then
                    echo "    ✅ SwiftUI property wrappers found"
                fi
            fi
            
            # Check for proper error handling
            if grep -q "do {" "$file" && grep -q "catch" "$file"; then
                echo "    ✅ Error handling patterns found"
            fi
        fi
    done
    
    if [ "$issues" -eq 0 ]; then
        echo "  ✅ No pattern issues detected"
    else
        echo "  ⚠️  $issues potential pattern issues found"
    fi
}

# Function to validate model relationships
validate_models() {
    echo "🏗️  Validating data models..."
    
    if [ -f "Keeps/Models.swift" ]; then
        echo "  📄 Checking Models.swift..."
        
        # Check for required model structures
        local models=("TeamMember" "Team" "TeamCategory" "TeamEvent" "SampleData")
        
        for model in "${models[@]}"; do
            if grep -q "struct $model\|class $model\|enum $model" "Keeps/Models.swift"; then
                echo "    ✅ $model found"
            else
                echo "    ❌ $model missing"
            fi
        done
        
        # Check for Identifiable conformance
        if grep -q "Identifiable" "Keeps/Models.swift"; then
            echo "    ✅ Identifiable conformance found"
        fi
        
        # Check for Codable conformance
        if grep -q "Codable" "Keeps/Models.swift"; then
            echo "    ✅ Codable conformance found"
        fi
        
    else
        echo "  ❌ Models.swift not found"
    fi
}

# Main validation function
main() {
    echo "🎯 Starting comprehensive code validation..."
    echo ""
    
    # Run all validations
    validate_project_structure
    echo ""
    
    check_code_quality
    echo ""
    
    validate_models
    echo ""
    
    validate_swift_patterns
    echo ""
    
    # Validate individual Swift files
    echo "📝 Validating individual Swift files..."
    local validation_failed=0
    
    for file in Keeps/*.swift; do
        if [ -f "$file" ]; then
            if ! validate_swift_file "$file"; then
                validation_failed=1
            fi
            echo ""
        fi
    done
    
    # Final summary
    echo "📋 Validation Summary"
    echo "===================="
    
    if [ "$validation_failed" -eq 0 ]; then
        echo "✅ All validations passed!"
        echo ""
        echo "🎉 Your Keeps Team Collaboration App code is ready!"
        echo ""
        echo "📱 Key Components Validated:"
        echo "  • Data models with proper structure"
        echo "  • SwiftUI views with best practices"
        echo "  • Proper imports and declarations"
        echo "  • Code quality and documentation"
        echo ""
        echo "🚀 Next Steps:"
        echo "  1. Open Keeps.xcodeproj in Xcode"
        echo "  2. Build and run the project (Cmd+R)"
        echo "  3. Test the team collaboration interface"
        echo "  4. Run unit tests (Cmd+U)"
        
        return 0
    else
        echo "❌ Some validations failed"
        echo "Please review the issues above and fix them before proceeding."
        return 1
    fi
}

# Run the validation
main "$@"
